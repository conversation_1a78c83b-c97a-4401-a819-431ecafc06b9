/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: test
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/20 17:05
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/20 17:05
/// @UpdateRemark: 更新说明
void main() {
  var url = "https://www.baidu.com/api?code=123";
  url = _addParamsToUrl(url, {"phone": "17501647310"});
  // print("addParams: $url");
}

String _addParamsToUrl(String url, Map<String, String?> params) {
  final uri = Uri.parse(url);
  final updatedUri = uri.replace(
    queryParameters: {
      ...uri.queryParameters, // 保留原有的参数
      ...params, // 添加新的参数
    },
  );
  return updatedUri.toString();
}
