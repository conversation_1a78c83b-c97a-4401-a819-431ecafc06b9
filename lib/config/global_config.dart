import 'package:flutter/material.dart';

import '../app/repository/modals/account/account.dart';
import '../common/http/http_utils.dart';
import '../utils/prefs_util.dart';
import 'config.dart';
import 'constant.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: global_config
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:16
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:16
/// @UpdateRemark: 更新说明
class GlobalConfig {
  /// 是否是release版
  static bool get isRelease => const bool.fromEnvironment("dart.vm.product");

  /// 用户信息
  static Account? account;

  /// 当前语言环境
  static Locale? localeInfo;

  /// 用户协议
  static bool? agreePrivacy;

  /// 当前路由
  static String? currentRouter;

  /// 是否开启代理(打包时选择不同配置关闭或者开启代理)
  static bool enableProxy = Config.vm;

  /// 初始化全局信息，会在APP启动时执行
  static Future init() async {
    /// 初始化本地存储
    await PrefsUtil().init();

    /// 初始化网络请求相关配置
    String baseUrl;
    if (enableProxy) {
      var isTest = PrefsUtil().getBool(PrefsKeys.serverWitchKey);
      baseUrl = isTest == false ? Constant.releaseBaseUrl : Constant.devBaseUrl;
    } else {
      baseUrl = Constant.releaseBaseUrl;
    }
    HttpUtils.init(baseUrl: baseUrl);

    /// 获取用户信息
    var userInfo = PrefsUtil().getJSON("account");
    debugPrint("userInfo----: $userInfo");
    if (userInfo != null) {
      account = Account.fromJson(userInfo);
    }

    /// 用户是否同意协议
    agreePrivacy = PrefsUtil().getBool(PrefsKeys.agreePrivacy);
  }

  /// 持久化用户信息
  static saveProfile() => PrefsUtil().setJSON("account", account?.toJson());
}
