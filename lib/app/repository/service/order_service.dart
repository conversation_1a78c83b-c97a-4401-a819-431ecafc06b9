import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/api.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail_list.dart';
import 'package:msmds_platform/app/repository/modals/order/order_gift.dart';
import 'package:msmds_platform/app/repository/modals/order/order_list.dart';
import 'package:msmds_platform/app/repository/modals/order/order_tab_list.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../utils/platform_util.dart';
import '../modals/order/order_tab.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 11:38
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 11:38
/// @UpdateRemark: 更新说明
class OrderService {
  /// 获取订单tab列表
  static Future<ApiResponse<List<OrderTab>>> getOrderTabList({
    String? orderNo,
    int? orderId,
  }) async {
    try {
      var response = await HttpUtils.get(Api.getAllOrderTab);

      BaseResponse<OrderTabList> result = BaseResponse.fromJson(
        response,
        (json) => OrderTabList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getUserOrderDetail-err: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取订单列表
  static Future<ApiResponse<OrderList>> listUserOrderTabDetail(
    int orderTabId,
    int pageNo,
    int pageSize,
    int? status,
  ) async {
    try {
      var data = FormData.fromMap({
        "orderTabId": orderTabId,
        "pageNo": pageNo,
        "pageSize": pageSize,
        "status": status,
      });

      var response = await HttpUtils.post(
        Api.getUserOrderList,
        data: data,
      );

      BaseResponse<OrderList> result = BaseResponse.fromJson(
        response,
        (json) => OrderList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 根据id获取订单详情
  static Future<ApiResponse<List<OrderDetail>>> getUserOrderDetail({
    String? orderNo,
    int? orderId,
  }) async {
    try {
      Map<String, dynamic> data = {};

      if (orderNo != null) {
        data["orderNo"] = orderNo;
      }

      if (orderId != null) {
        data["orderId"] = orderId;
      }

      var response = await HttpUtils.get(
        Api.getUserOrderDetail,
        params: data,
      );

      BaseResponse<OrderDetailList> result = BaseResponse.fromJson(
        response,
        (json) => OrderDetailList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getUserOrderDetail-err: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取订单可用的红包列表
  static Future<ApiResponse<OrderGift>> getOrderCanUseRedPacket(
    int orderId,
  ) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      var platformType = 0;
      if (PlatformUtils.isAndroid) {
        platformType = 2;
      } else if (PlatformUtils.isIOS) {
        platformType = 1;
      } else {
        platformType = 3;
      }

      var data = {
        "orderId": orderId,
        "version": packageInfo.version,
        "platformType": platformType,
        "layoutType": 1,
      };

      var response = await HttpUtils.get(
        BillApi.getOrderCanUseRedPacket,
        params: data,
      );

      debugPrint("getOrderCanUseRedPacket: $response");

      BaseResponse<OrderGift> result = BaseResponse.fromJson(
        response,
        (json) => OrderGift.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取订单可用的红包列表
  static Future<ApiResponse<bool>> activationGift(
    int giftId,
    int orderId,
  ) async {
    try {
      var data = FormData.fromMap({
        "giftId": giftId,
        "orderId": orderId,
      });

      var response = await HttpUtils.post(
        BillApi.activationGift,
        data: data,
      );

      debugPrint("activationGift: $response");

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
