import 'package:dio/dio.dart';
import 'package:msmds_platform/app/provider/search/filter_provider.dart';
import 'package:msmds_platform/app/repository/api.dart';
import 'package:msmds_platform/app/repository/modals/search/history_list.dart';
import 'package:msmds_platform/app/repository/modals/search/search_goods.dart';
import 'package:msmds_platform/app/repository/modals/search/search_goods_list.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 10:57
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 10:57
/// @UpdateRemark: 更新说明
class SearchService {
  /// 关键词搜索
  static Future<ApiResponse<List<SearchGoods>>> searchKeyword(
    int pageNo,
    String keyword,
    FilterData filterData, {
    int? storeType,
  }) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      var data = {
        "version": packageInfo.version,
        "pageNo": pageNo,
        "keyword": keyword,
      };

      if (storeType != null) {
        data["storeType"] = storeType;
      }

      if (filterData.sortType != -1) {
        data["sortType"] = filterData.sortType;
        data["sort"] = filterData.sort;
      }

      if (filterData.hasCoupon != -1) {
        data["hasCoupon"] = filterData.hasCoupon;
      }

      var response = await HttpUtils.get(SearchApi.search, params: data);

      BaseResponse<SearchGoodsList> result = BaseResponse.fromJson(
        response,
        (json) => SearchGoodsList.fromJson(json),
      );

      return ApiResponse.completed(result.data?.goodsInfoList);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取搜索记录
  static Future<ApiResponse<HistoryList>> listSearchHistory() async {
    try {
      var response = await HttpUtils.get(SearchApi.searchHistoryList);

      BaseResponse<HistoryList> result = BaseResponse.fromJson(
        response,
        (json) => HistoryList.fromJson(response),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 清除搜索记录
  static Future<ApiResponse<String>> cleanSearchHistory() async {
    try {
      var response = await HttpUtils.post(
        SearchApi.deleteSearchHistory,
        data: {"all": true},
      );

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 保存搜索记录
  static Future<ApiResponse<String>> saveSearchHistory(
      String searchTerm) async {
    try {
      var response = await HttpUtils.post(
        SearchApi.saveSearchHistory,
        data: FormData.fromMap({"searchTerm": searchTerm}),
      );

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
