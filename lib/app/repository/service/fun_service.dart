import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_douyin_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_free_lunch_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_meituan_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_promotion.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';

import '../api.dart';

class FunService {
  static Future<ApiResponse<List<FunPromotionCollection>>>
      fetchListPromotionColumn() async {
    try {
      final response = await HttpUtils.get(Api.listPromotionColumn);

      BaseResponse<List<FunPromotionCollection>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => FunPromotionCollection.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FunTabResult>> fetchTabConfig(String code) async {
    try {
      final response = await HttpUtils.get(
        Api.getByCode,
        params: {'code': code},
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      final data = result.data;
      if (data == null || data.isEmpty) {
        return ApiResponse.completed(const FunTabResult());
      }

      final dynamic decoded = jsonDecode(data);
      if (decoded is List) {
        final tabs = decoded
            .map((e) =>
                FunTabConfig.fromJson(Map<String, dynamic>.from(e as Map)))
            .toList();
        return ApiResponse.completed(FunTabResult(tabs: tabs));
      }

      return ApiResponse.completed(const FunTabResult());
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<MeituanCouponResponse>> fetchMeituanCoupons(
    MeituanCouponParams params,
  ) async {
    try {
      final response = await HttpUtils.get(
        Api.mtlmQueryCoupon,
        params: params.toQueryParameters(),
      );

      final BaseResponse<MeituanCouponResponse> result = BaseResponse.fromJson(
        response,
        (json) => MeituanCouponResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<DouyinLifeResponse>> fetchDouyinLifeList(
    DouyinLifeParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        Api.funLifeSearch,
        data: params.toJson(),
      );

      final BaseResponse<DouyinLifeResponse> result = BaseResponse.fromJson(
        response,
        (json) => DouyinLifeResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FreeLunchResponse>> fetchFreeLunchList(
    FreeLunchParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        Api.freeLunchList,
        data: params.toJson(),
        // options: Options(baseUrl: _resolveFreeLunchBaseUrl()),
      );

      final BaseResponse<FreeLunchResponse> result = BaseResponse.fromJson(
        response,
        (json) => FreeLunchResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FreeLunchResponse>> fetchMtActivityList(
    FreeLunchParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        Api.mtActivityList,
        data: params.toJson(),
        // options: Options(baseUrl: _resolveFreeLunchBaseUrl()),
      );

      final BaseResponse<FreeLunchResponse> result = BaseResponse.fromJson(
        response,
        (json) => FreeLunchResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse<FreeLunchResponse>> fetchElmActivityList(
    FreeLunchParams params,
  ) async {
    try {
      final response = await HttpUtils.post(
        Api.elmActivityList,
        data: params.toJson(),
        // options: Options(baseUrl: _resolveFreeLunchBaseUrl()),
      );

      final BaseResponse<FreeLunchResponse> result = BaseResponse.fromJson(
        response,
        (json) => FreeLunchResponse.fromJson(
          Map<String, dynamic>.from(json as Map),
        ),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse> fetchMeituanShareLink(
    MeituanCouponItem item
  ) async {
    try {

      final params = item.toJson();
      params.removeWhere((key, value) => value == null);
      // params追加字段
      params['pid'] = 'msmds';
      
      final response = await HttpUtils.get(
        Api.mtlmShareLink,
        params: params,
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }


  static Future<ApiResponse<String>> fetchMeituanGetCoupon(
    MeituanCouponItem item,
    int linkType,
  ) async {
    try {
      final params = item.toJson();
      params.removeWhere((key, value) => value == null);
      params['pid'] = 'msmds';
      params['linkType'] = linkType;
      params['skuViewId'] = item.skuViewId;
      params['platform'] = item.platform;
      params['bizLine'] = item.bizLine;

      final response = await HttpUtils.get(
        ActivityApi.getMtReferralLink,
        params: params,
      );

      final BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json as String,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
//   static String _resolveFreeLunchBaseUrl() {
//     final baseUrl = Http.dio.options.baseUrl;
//     if (baseUrl.contains('test')) {
//       return Constant.devFreeLunchBaseUrl;
//     }
//     return Constant.releaseFreeLunchBaseUrl;
//   }
}

class MeituanCouponParams {
  final double latitude;
  final double longitude;
  final int listTopiId;
  final int platform;
  final int pageNo;
  final int pageSize;
  final int? sortField;
  final int? ascDescOrder;
  final int? bizLine;

  const MeituanCouponParams({
    required this.latitude,
    required this.longitude,
    required this.listTopiId,
    required this.platform,
    required this.pageNo,
    required this.pageSize,
    this.sortField,
    this.ascDescOrder,
    this.bizLine,
  });

  Map<String, dynamic> toQueryParameters() {
    final params = <String, dynamic>{
      'latitude': (latitude * 1000000).floor(),
      'longitude': (longitude * 1000000).floor(),
      'listTopiId': listTopiId,
      'platform': platform,
      'pageNo': pageNo,
      'pageSize': pageSize,
    };
    if (sortField != null) {
      params['sortField'] = sortField;
    }
    if (ascDescOrder != null) {
      params['ascDescOrder'] = ascDescOrder;
    }
    if (bizLine != null) {
      params['bizLine'] = bizLine;
    }
    return params;
  }
}

class DouyinLifeParams {
  final double latitude;
  final double longitude;
  final int pageNum;
  final int pageSize;
  final int sort;
  final int categoryId;

  const DouyinLifeParams({
    required this.latitude,
    required this.longitude,
    required this.pageNum,
    required this.pageSize,
    required this.sort,
    required this.categoryId,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'pageNum': pageNum,
      'pageSize': pageSize,
      'sort': sort,
      'categoryId': categoryId,
    };
  }
}

class FreeLunchParams {
  final double lat;
  final double lng;
  final int sort;
  final String channelCode;
  final String? mobile;
  final String? mtPageId;
  final String? pageId;

  const FreeLunchParams({
    required this.lat,
    required this.lng,
    required this.sort,
    this.channelCode = 'msmds',
    this.mobile,
    this.mtPageId,
    this.pageId,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'channelCode': channelCode,
      'lat': lat,
      'lng': lng,
      'sort': sort,
    };
    if (mobile != null) {
      data['mobile'] = mobile;
    }
    if (mtPageId != null) {
      data['mtPageId'] = mtPageId;
    }
    if (pageId != null) {
      data['pageId'] = pageId;
    }
    return data;
  }
}
