import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/api.dart';
import 'package:msmds_platform/app/repository/modals/convert/convert_data.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_detail.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_list.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_pkg.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_pkg_list.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_tb_pkg.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_tb_pkg_list.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../modals/goods/temp_goods.dart';
import '../modals/goods/temp_goods_list.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: goods_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/28 11:40
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/28 11:40
/// @UpdateRemark: 更新说明
class GoodsService {
  /// 获取商品包信息列表
  static Future<ApiResponse<List<GoodsPkg>>> getListGoodsPkg(
    String displayArea,
    int goodsType,
  ) async {
    try {
      var data = {
        "displayArea": displayArea,
        "goodsType": goodsType,
      };

      var response = await HttpUtils.get(Api.listGoodsPkg, params: data);

      BaseResponse<GoodsPkgList> result = BaseResponse.fromJson(
        response,
        (json) => GoodsPkgList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取商品包商品列表
  static Future<ApiResponse<GoodsList>> getListGoodsByPkg(
    int pageNo,
    int pageSize,
    int pkgId,
    int goodsType,
  ) async {
    try {
      var data = {
        "pageNo": pageNo,
        "pageSize": pageSize,
        "pkgId": pkgId,
        "goodsType": goodsType,
      };

      var response = await HttpUtils.get(Api.listGoodsByPkgId, params: data);

      BaseResponse<GoodsList> result = BaseResponse.fromJson(
        response,
        (json) => GoodsList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 粘贴板识别
  static Future<ApiResponse<ConvertData>> clipboardConvert(
    String version,
    String content,
  ) async {
    try {
      var data = {
        "version": version,
        "content": content,
      };

      var response = await HttpUtils.post(GoodsApi.appAnalysis, data: data);

      BaseResponse<ConvertData> result = BaseResponse.fromJson(
        response,
        (json) => ConvertData.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 根据商品包ID获取淘宝精选商品
  static Future<ApiResponse<List<GoodsTbPkg>?>> getGoodsByPkg(
    int pageNo,
    int pageSize,
    String? pkgId,
  ) async {
    try {
      var data = {
        "pageNo": pageNo,
        "pageSize": pageSize,
        "pkgId": pkgId,
        "code": "appMsmdsTb",
      };

      var response = await HttpUtils.get(GoodsApi.findTBPkgSku, params: data);

      BaseResponse<GoodsTbPkgList> result = BaseResponse.fromJson(
        response,
        (json) => GoodsTbPkgList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 根据商品包ID获取京东商品列表
  static Future<ApiResponse<List<TempGoods>>> getJdListGoodsByPkg(
    int pageNo,
    int pageSize,
    String? pkgId,
  ) async {
    try {
      var data = FormData.fromMap({
        "pageNo": pageNo,
        "pageSize": pageSize,
        "pkgId": pkgId,
      });

      var response = await HttpUtils.post(GoodsApi.findJDFeatured, data: data);

      BaseResponse<TempGoodsList> result = BaseResponse.fromJson(
        response,
        (json) => TempGoodsList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getJdListGoodsByPkg-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 获取商品详情
  static Future<ApiResponse<GoodsDetail>> getGoodsDetail(
    String goodsId,
    int platformType, {
    String? bizSceneId,
  }) async {
    try {
      var packageInfo = await PackageInfo.fromPlatform();
      var data = {
        "version": packageInfo.version,
        "goodsId": goodsId,
        "platformType": platformType,
      };

      if (bizSceneId != null) {
        data["bizSceneId"] = bizSceneId;
      }

      var response = await HttpUtils.get(GoodsApi.goodsDetail, params: data);

      BaseResponse<GoodsDetail> result = BaseResponse.fromJson(
        response,
        (json) => GoodsDetail.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getGoodsDetail-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 淘宝商品转链
  static Future<ApiResponse<String>> getTBGoodChangeUrl(
    String? url, {
    String? code,
  }) async {
    try {
      var data = {"url": url};

      if (code != null) {
        data["code"] = code;
      }

      var response = await HttpUtils.post(GoodsApi.getTBGoodUrl, data: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getTBGoodChangeUrl-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 淘宝转链分享
  static Future<ApiResponse<String>> shareContent(
    String? goodsId,
    int platformType,
    String? purchaseDescription, {
    String? bizSceneId,
  }) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      var data = {
        "goodsId": goodsId,
        "platformType": platformType,
        "purchaseDescription": purchaseDescription,
        "version": packageInfo.version,
      };

      if (bizSceneId != null) {
        data["bizSceneId"] = bizSceneId;
      }

      var response = await HttpUtils.get(GoodsApi.shareContent, params: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("shareContent-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
