import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/api.dart';
import 'package:msmds_platform/app/repository/modals/activity/ele_activity.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_cms_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_resource_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/dy_change_goods.dart';
import 'package:msmds_platform/app/repository/modals/transfer/jd_activity_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/jd_change_goods.dart';
import 'package:msmds_platform/app/repository/modals/transfer/tb_activity_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/tb_change_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/wph_change_goods.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';

import '../modals/transfer/pdd_change_goods.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: chain_transfer_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 16:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 16:49
/// @UpdateRemark: 更新说明
class ChainTransferService {
  /// 京东转链
  static Future<ApiResponse<JdChangeGoods>> jdChangeUrl(
    String? skuId, {
    String? couponLink,
    String? code,
  }) async {
    try {
      var data = {
        "skuId": skuId,
        "couponLink": couponLink,
        "webId": 1,
        "code": code,
      };

      var response = await HttpUtils.post(GoodsApi.jdChangeUrl, data: data);

      BaseResponse<JdChangeGoods> result = BaseResponse.fromJson(
        response,
        (json) => JdChangeGoods.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("jdChangeUrl: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 拼多多转链
  static Future<ApiResponse<PddChangeGoods>> pddChangeUrl(
    String skuId, {
    bool showUrl = true,
  }) async {
    try {
      var data = FormData.fromMap({
        "skuId": skuId,
        "showUrl": showUrl,
      });

      var response = await HttpUtils.post(GoodsApi.pddChangeUrl, data: data);

      debugPrint("pddChangeUrl: $response");

      BaseResponse<PddChangeGoods> result = BaseResponse.fromJson(
        response,
        (json) => PddChangeGoods.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 生成多多进宝频道推广
  static Future<ApiResponse<DdkResourceUrl>> ddkResourceUrl({
    int? resourceType,
    bool generateSchemaUrl = true,
    bool generateWeApp = true,
    String? pid,
    String? url,
  }) async {
    try {
      Map data = {
        "generateSchemaUrl": generateSchemaUrl,
        "generateWeApp": generateWeApp,
      };

      if (resourceType != null) {
        data["resourceType"] = resourceType;
      }

      if (pid != null) {
        data["pid"] = pid;
      }

      if (url != null) {
        data["url"] = url;
      }

      var response = await HttpUtils.post(Api.getDdkResourceUrl, data: data);

      debugPrint("ddkResourceUrl: $response");

      BaseResponse<DdkResourceUrl> result = BaseResponse.fromJson(
        response,
        (json) => DdkResourceUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 获取商城-频道推广链接
  static Future<ApiResponse<DdkCmsUrl>> cmsPromUrlGenerate({
    int? channelType,
    bool generateMobile = true,
    bool generateSchemaUrl = true,
    bool generateShortUrl = true,
    bool generateWeApp = true,
    String? keyword,
    bool multiGroup = true,
    List<String>? pidList,
  }) async {
    try {
      Map data = {
        "channelType": channelType,
        "generateMobile": generateMobile,
        "generateSchemaUrl": generateSchemaUrl,
        "generateShortUrl": generateShortUrl,
        "generateWeApp": generateWeApp,
        "multiGroup": multiGroup,
      };

      if (keyword != null) {
        data["keyword"] = keyword;
      }

      if (pidList != null) {
        data["pidList"] = pidList;
      }

      var response =
          await HttpUtils.post(Api.getDdkCmsPromUrlGenerate, data: data);

      debugPrint("cmsPromUrlGenerate: $response");

      BaseResponse<DdkCmsUrl> result = BaseResponse.fromJson(
        response,
        (json) => DdkCmsUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 生成营销工具推广链接
  static Future<ApiResponse<DdkCmsUrl>> rpPromUrlGenerate({
    int? channelType,
    bool generateQqApp = false,
    bool generateSchemaUrl = true,
    bool generateShortUrl = true,
    bool generateWeApp = true,
    List<String>? pidList,
  }) async {
    try {
      Map data = {
        "channelType": channelType,
        "generateQqApp": generateQqApp,
        "generateSchemaUrl": generateSchemaUrl,
        "generateShortUrl": generateShortUrl,
        "generateWeApp": generateWeApp,
      };

      if (pidList != null) {
        data["pidList"] = pidList;
      }

      var response =
          await HttpUtils.post(Api.getDdkRpPromUrlGenerate, data: data);

      debugPrint("cmsPromUrlGenerate: $response");

      BaseResponse<DdkCmsUrl> result = BaseResponse.fromJson(
        response,
        (json) => DdkCmsUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 唯品会商品转链
  static Future<ApiResponse<WphChangeGoods>> wphChangeUrl(
    String skuId,
    String val, {
    String? adCode,
    String? rid,
  }) async {
    try {
      var data = {
        "skuId": skuId,
        "val": val,
      };

      if (adCode != null) {
        data["adCode"] = adCode;
      }

      if (rid != null) {
        data["rid"] = rid;
      }

      var response = await HttpUtils.get(GoodsApi.wphChangeUrl, params: data);

      debugPrint("wphChangeUrl: $response");

      BaseResponse<WphChangeGoods> result = BaseResponse.fromJson(
        response,
        (json) => WphChangeGoods.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 抖音商品转链
  static Future<ApiResponse<DyChangeGoods>> dyChangeUrl(String itemInfo) async {
    try {
      var data = {
        "itemInfo": itemInfo,
      };

      var response = await HttpUtils.get(GoodsApi.dyChangeUrl, params: data);

      debugPrint("dyChangeUrl: $response");

      BaseResponse<DyChangeGoods> result = BaseResponse.fromJson(
        response,
        (json) => DyChangeGoods.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 饿了么推广官方活动查询
  static Future<ApiResponse<EleActivity>> getPromotionOfficialActivity({
    String? activityId,
    bool includeQrCode = true,
    bool includeWxImg = true,
    String? pid,
  }) async {
    try {
      Map data = {
        "activityId": activityId,
        "includeQrCode": includeQrCode,
        "includeWxImg": includeWxImg,
      };

      if (pid != null) {
        data["pid"] = pid;
      }

      var response =
          await HttpUtils.post(Api.getPromotionOfficialActivity, data: data);

      debugPrint("getPromotionOfficialActivity: $response");

      BaseResponse<EleActivity> result = BaseResponse.fromJson(
        response,
        (json) => EleActivity.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 美团活动转链 - 对应RN中type 20和26的getMtActivityInfo API
  static Future<ApiResponse<dynamic>> getMtActivityInfo({
    String? activity,
    int pageLevel = 2,
    String sourceCode = 'mtmain',
  }) async {
    try {
      Map<String, dynamic> data = {
        "activity": activity,
        "pageLevel": pageLevel,
        "sourceCode": sourceCode,
      };
      var response = await HttpUtils.get(ActivityApi.getMtActivityInfo, params: data);
      debugPrint("getMtActivityInfo: $response");
      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 美团联盟推广链接生成 - 对应RN中type 34的lmGenerateLink API
  static Future<ApiResponse<String>> lmGenerateLink({
    int? actId,
    String code = 'mtwaimai',
    int linkType = 1,
    int shortLink = 1,
  }) async {
    try {
      Map<String, dynamic> data = {
        "actId": actId,
        "code": code,
        "linkType": linkType,
        "shortLink": shortLink,
      };
      var response = await HttpUtils.post(ActivityApi.lmGenerateLink, data: data);
      debugPrint("lmGenerateLink: $response");
      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 美团获取推广链接
  static Future<ApiResponse<String>> getReferralLink({
    String? actId,
    int linkType = 3,
    String? skuViewId,
    String? text,
  }) async {
    try {
      Map data = {
        "actId": actId,
        "linkType": linkType,
      };

      if (skuViewId != null) {
        data["skuViewId"] = skuViewId;
      }

      if (text != null) {
        data["text"] = text;
      }

      var response = await HttpUtils.post(Api.getReferralLink, data: data);

      debugPrint("getReferralLink: $response");

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 淘宝转链
  static Future<ApiResponse<TbChangeUrl>> tbChangeUrl(
    String goodsId, {
    String? code,
  }) async {
    try {
      var data = {
        "goodsId": goodsId,
      };

      if (code != null) {
        data["code"] = code;
      }

      var response = await HttpUtils.post(Api.getPurchaseLink, data: data);

      debugPrint("tbChangeUrl: $response");

      BaseResponse<TbChangeUrl> result = BaseResponse.fromJson(
        response,
        (json) => TbChangeUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 京东活动转链
  static Future<ApiResponse<JdActivityUrl>> jdActivityChangeUrl(
    String activityUrl,
  ) async {
    try {
      var data = {
        "activityUrl": activityUrl,
      };

      var response = await HttpUtils.get(Api.jdActivityChangeUrl, params: data);

      debugPrint("jdActivityChangeUrl: $response");

      BaseResponse<JdActivityUrl> result = BaseResponse.fromJson(
        response,
        (json) => JdActivityUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  /// 淘宝活动转链
  static Future<ApiResponse<TbActivityUrl>> tbActivityChangeUrl(
    String activityId,
  ) async {
    try {
      var data = {
        "activityId": activityId,
      };

      var response = await HttpUtils.get(Api.tbActivityChangeUrl, params: data);

      debugPrint("tbActivityChangeUrl: $response");

      BaseResponse<TbActivityUrl> result = BaseResponse.fromJson(
        response,
        (json) => TbActivityUrl.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 根据活动id或者url获取美团活动链接
  static Future<ApiResponse<dynamic>> mtActivityChangeUrl(
    String activity,
  ) async {
    try {
      var data = {
        "activity": activity,
        "pageLevel": 2,
        "sourceCode": "mtmain",
      };

      var response =
          await HttpUtils.get(ActivityApi.getMtActivityInfo, params: data);

      debugPrint("mtActivityChangeUrl: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 根据活动id或者url获取美团活动链接
  static Future<ApiResponse<dynamic>> getMtReferralLink(
    String actId,
    int linkType,
  ) async {
    try {
      var data = {
        "actId": actId,
        "linkType": linkType,
      };

      var response =
          await HttpUtils.get(ActivityApi.getMtReferralLink, params: data);

      debugPrint("getMtReferralLink: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 根据活动id获取淘宝活动链接
  static Future<ApiResponse<dynamic>> getTbActivityInfo(
    String activityId,
  ) async {
    try {
      var data = FormData.fromMap({
        "activityId": activityId,
        "isShortUrl": true,
      });

      var response =
          await HttpUtils.post(ActivityApi.getTbActivityLink, data: data);

      debugPrint("getTbActivityInfo: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取多麦活动链接
  static Future<ApiResponse<dynamic>> getDuoMaiActivityInfo(
    dynamic adsId,
    dynamic siteId,
  ) async {
    try {
      var data = {
        "adsId": adsId,
        "siteId": siteId,
      };

      var response = await HttpUtils.get(
        ActivityApi.duoMaiCpsLink,
        params: data,
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getDuoMaiActivityInfo: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getDuoMaiActivityInfo-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取多麦活动链接
  static Future<ApiResponse<dynamic>> getJuTuiKeActivityInfo(
      dynamic actId) async {
    try {
      var data = {
        "actId": actId,
      };

      var response = await HttpUtils.get(
        ActivityApi.juTuiKeCpsLink,
        params: data,
      );

      debugPrint("getJuTuiKeActivityInfo: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getJuTuiKeActivityInfo: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getJuTuiKeActivityInfo-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取配置京东活动链接
  static Future<ApiResponse<dynamic>> getJinDonActivityInfo(
    dynamic url,
  ) async {
    try {
      var data = {
        "url": url,
      };

      var response = await HttpUtils.get(
        ActivityApi.getJDActivityUrl,
        params: data,
      );

      debugPrint("getJinDonActivityInfo: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getJinDonActivityInfo: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getJinDonActivityInfo-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取饿了么新零售推广链接
  static Future<ApiResponse<dynamic>> getNewRetailUrl(
    dynamic type,
  ) async {
    try {
      var data = {
        "type": type,
      };

      var response = await HttpUtils.get(
        ActivityApi.getNewRetailUrl,
        params: data,
      );

      debugPrint("getNewRetailUrl: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getNewRetailUrl: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getNewRetailUrl-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取大众点评和美团生活券链接
  static Future<ApiResponse<dynamic>> getLifeCouponUrl(
    dynamic type,
  ) async {
    try {
      var data = {
        "type": type,
      };

      var response = await HttpUtils.get(
        ActivityApi.getLifeCouponUrl,
        params: data,
      );

      debugPrint("getLifeCouponUrl: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getLifeCouponUrl: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getLifeCouponUrl-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取大众点评和美团生活券链接
  static Future<ApiResponse<dynamic>> getDiscountRadarUrl() async {
    try {
      var response = await HttpUtils.get(ActivityApi.getDiscountRadarUrl);

      debugPrint("getDiscountRadarUrl: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getDiscountRadarUrl: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getDiscountRadarUrl-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 唯品会根据url获取推广链接
  static Future<ApiResponse<dynamic>> wphGetUrlByUrl(
    dynamic url,
  ) async {
    try {
      var data = {
        "url": url,
        "isShortUrl": true,
      };

      var response = await HttpUtils.get(
        ActivityApi.wphGetUrlByUrl,
        params: data,
      );

      debugPrint("wphGetUrlByUrl: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("wphGetUrlByUrl: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("wphGetUrlByUrl-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 唯品会根据url获取推广链接
  static Future<ApiResponse<dynamic>> wphGetUrlById(
    dynamic skuId,
    dynamic val,
  ) async {
    try {
      var data = {
        "skuId": skuId,
        "val": val,
      };

      var response = await HttpUtils.get(
        ActivityApi.wphGetUrlById,
        params: data,
      );

      debugPrint("wphGetUrlById: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("wphGetUrlById: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("wphGetUrlByUrl-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取苏宁商城转链链接
  static Future<ApiResponse<dynamic>> getSuningShop(
    dynamic channelId,
  ) async {
    try {
      var data = {
        "channelId": channelId,
      };

      var response = await HttpUtils.get(
        ActivityApi.getSuningShop,
        params: data,
      );

      debugPrint("getSuningShop: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getSuningShop: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getSuningShop-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取滴滴转链
  static Future<ApiResponse<dynamic>> getDiDiActivityInfo(
    dynamic activityId,
    dynamic isMiniApp,
    dynamic promotionId,
  ) async {
    try {
      var data = {
        "activityId": activityId,
        "isMiniApp": isMiniApp,
        "promotionId": promotionId,
      };

      var response = await HttpUtils.get(
        ActivityApi.diGenerateLink,
        params: data,
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getDiDiActivityInfo: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getDiDiActivityInfo-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取滴滴转链
  static Future<ApiResponse<dynamic>> elePromotionLink(
    dynamic activityId,
  ) async {
    try {
      var data = {"activityId": activityId};

      var response = await HttpUtils.get(
        ActivityApi.elePromotionLink,
        params: data,
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("elePromotionLink: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("elePromotionLink-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }
}
