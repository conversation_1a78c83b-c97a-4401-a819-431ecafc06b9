import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:msmds_platform/app/repository/modals/archive/favorite_goods_list.dart';

import '../../../common/http/api_response.dart';
import '../../../common/http/app_exceptions.dart';
import '../../../common/http/base/base_response.dart';
import '../../../common/http/http_utils.dart';
import '../api.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.service
/// @ClassName: archive_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/26 11:40
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/26 11:40
/// @UpdateRemark: 更新说明
class ArchiveService {
  // 收藏商品列表
  static Future<ApiResponse<FavoriteGoodsList>> findUserGoodsCollect(
    int goodsType,
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "goodsType": goodsType,
        "pageNum": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(
        Api.findUserGoodsCollect,
        params: data,
      );

      BaseResponse<FavoriteGoodsList> result = BaseResponse.fromJson(
        response,
        (json) => FavoriteGoodsList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("findUserGoodsCollect: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("findUserGoodsCollect-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 根据ID删除收藏商品列表
  static Future<ApiResponse<dynamic>> removeGoodsCollect(
    List<int> deleteIds,
  ) async {
    try {
      var response = await HttpUtils.post(
        Api.removeGoodsCollect,
        data: jsonEncode(deleteIds),
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("removeGoodsCollect: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("removeGoodsCollect-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 检查商品是否收藏
  static Future<ApiResponse<bool>> checkGoodsCollect(
    String goodsId,
    int goodsType,
  ) async {
    try {
      var data = {
        "goodsId": goodsId,
        "goodsType": goodsType,
      };

      var response = await HttpUtils.get(
        Api.checkGoodsCollect,
        params: data,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("checkGoodsCollect: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("checkGoodsCollect-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 删除收藏商品
  static Future<ApiResponse<dynamic>> removeGoodsCollectGoods(
    List<String?> deleteGoodsIds,
    int? goodsType,
  ) async {
    try {
      var data = FormData.fromMap({
        "deleteGoodsIds": deleteGoodsIds,
        "goodsType": goodsType,
      });

      var response = await HttpUtils.post(
        Api.removeGoodsCollectGoods,
        data: data,
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("removeGoodsCollectGoods: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("removeGoodsCollectGoods-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 添加收藏商品
  static Future<ApiResponse<dynamic>> saveGoodsCollect(
    String goodsId,
    int? goodsType,
  ) async {
    try {
      var data = FormData.fromMap({
        "goodsId": goodsId,
        "goodsType": goodsType,
      });

      var response = await HttpUtils.post(
        Api.saveGoodsCollect,
        data: data,
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("saveGoodsCollect: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("saveGoodsCollect-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 浏览商品列表
  static Future<ApiResponse<FavoriteGoodsList>> findGoodsBrowsingRecord(
    int goodsType,
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "goodsType": goodsType,
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(
        Api.findGoodsBrowsingRecord,
        params: data,
      );

      BaseResponse<FavoriteGoodsList> result = BaseResponse.fromJson(
        response,
        (json) => FavoriteGoodsList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("findGoodsBrowsingRecord: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("findGoodsBrowsingRecord-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 根据ID删除浏览商品列表
  static Future<ApiResponse<dynamic>> deleteGoodsBrowsingRecord(
    List<int> deleteIds,
  ) async {
    try {
      var response = await HttpUtils.post(
        Api.deleteGoodsBrowsingRecord,
        data: jsonEncode(deleteIds),
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("deleteGoodsBrowsingRecord: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("deleteGoodsBrowsingRecord-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 添加浏览足迹
  static Future<ApiResponse<dynamic>> saveGoodsBrowsingRecord(
    String goodsId,
    int? goodsType,
  ) async {
    try {
      var data = FormData.fromMap({
        "goodsId": goodsId,
        "goodsType": goodsType,
      });

      var response = await HttpUtils.post(
        Api.saveGoodsBrowsingRecord,
        data: data,
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("saveGoodsBrowsingRecord: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("saveGoodsBrowsingRecord-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }
}
