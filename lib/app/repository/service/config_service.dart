import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/modals/config/home_tab_pkg.dart';
import 'package:msmds_platform/app/repository/modals/config/home_tab_pkg_list.dart';
import 'package:msmds_platform/app/repository/modals/popularize/popularize.dart';
import 'package:msmds_platform/app/repository/modals/sign/exchange_item.dart';
import 'package:msmds_platform/app/repository/modals/sign/exchange_list.dart';
import 'package:msmds_platform/app/repository/modals/sign/integral_detail_list.dart';
import 'package:msmds_platform/app/repository/modals/sign/luck_gift_info.dart';
import 'package:msmds_platform/app/repository/modals/sign/luck_gift_item.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout_ball_item.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout_ball_list.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_record.dart';
import 'package:msmds_platform/app/repository/modals/sign/user_integral.dart';
import 'package:msmds_platform/utils/platform_util.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../common/http/api_response.dart';
import '../../../common/http/app_exceptions.dart';
import '../../../common/http/base/base_response.dart';
import '../../../common/http/http_utils.dart';
import '../api.dart';
import '../modals/config/back_config.dart';
import '../modals/config/icon_config.dart';
import '../modals/config/icon_config_list.dart';
import '../modals/popularize/popularize_list.dart';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: config_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/8 11:14
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/8 11:14
/// @UpdateRemark: 更新说明
class ConfigService {
  /// 获取icon配置
  static Future<ApiResponse<List<IconConfig>>> getIconConfig(
    int iconType,
    String version,
  ) async {
    try {
      var platformType = 0;
      if (PlatformUtils.isAndroid) {
        platformType = 1;
      } else if (PlatformUtils.isIOS) {
        platformType = 2;
      } else {
        platformType = 3;
      }
      var data = {
        "type": iconType,
        "version": version,
        "platformType": platformType,
      };

      var response = await HttpUtils.post(
        ConfigApi.findAllOfEffective,
        params: data,
      );

      BaseResponse<IconConfigList> result = BaseResponse.fromJson(
        response,
        (json) => IconConfigList.fromJson(response),
      );

      debugPrint("getIconConfig: ${result.data}");

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getIconConfig-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 首页tab bar配置
  static Future<ApiResponse<List<HomeTabPkg>>> getHomeTabPkg() async {
    try {
      var response = await HttpUtils.get(
        ConfigApi.homePagePkgConfig,
      );

      debugPrint("getHomeTabPkg-response: $response");

      BaseResponse<HomeTabPkgList> result = BaseResponse.fromJson(
        response,
        (json) => HomeTabPkgList.fromJson(response),
      );

      debugPrint("getHomeTabPkg: ${result.data}");

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getHomeTabPkg-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取活动配置
  static Future<ApiResponse<List<Popularize>>> getPopularizeConfig(
    int type,
    String version,
  ) async {
    try {
      var data = {
        "type": type,
        "version": version,
        "platformType": PlatformUtils.isIOS ? 2 : 1,
      };

      var response = await HttpUtils.post(
        ConfigApi.popularizeList,
        params: data,
      );

      BaseResponse<PopularizeList> result = BaseResponse.fromJson(
        response,
        (json) => PopularizeList.fromJson(response),
      );

      debugPrint("getPopularizeConfig: ${result.data}");

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getIconConfig-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 签到背景图片
  static Future<ApiResponse<BackConfig>> getSignBackImg(String version) async {
    try {
      var response = await HttpUtils.get(ConfigApi.signBackImg, params: {
        "version": version,
        "platformType": PlatformUtils.isIOS ? 1 : 2,
      });

      BaseResponse<BackConfig> result = BaseResponse.fromJson(
        response,
        (json) => BackConfig.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getSignBackImg-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 签到记录获取
  static Future<ApiResponse<SignRecord>> getSignRecord() async {
    try {
      var response = await HttpUtils.post(
        ConfigApi.signRecord,
        data: FormData.fromMap({"version": "v4"}),
      );

      BaseResponse<SignRecord> result = BaseResponse.fromJson(
        response,
        (json) => SignRecord.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getSignRecord-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 用户签到
  static Future<ApiResponse<dynamic>> userSign() async {
    try {
      var response = await HttpUtils.post(
        ConfigApi.signAdd,
        data: FormData.fromMap({"version": "v4"}),
      );

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("userSign-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 获取签到配置内容
  static Future<ApiResponse<SignLayout>> getSignLayout(
    String version,
    int layoutType,
  ) async {
    try {
      var data = {
        "layoutType": layoutType,
        "version": version,
        "platformType": PlatformUtils.isIOS ? 2 : 1,
      };

      var response = await HttpUtils.get(
        ConfigApi.getSignLayout,
        params: data,
      );

      BaseResponse<SignLayout> result = BaseResponse.fromJson(
        response,
        (json) => SignLayout.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getSignLayout-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 获取签到积分气泡
  static Future<ApiResponse<List<SignLayoutBallItem>>> getSignBalls() async {
    try {
      var response = await HttpUtils.post(ConfigApi.getAllIntegralInfo);

      BaseResponse<SignLayoutBallList> result = BaseResponse.fromJson(
        response,
        (json) => SignLayoutBallList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("getSignBalls-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 获取指定积分
  static Future<ApiResponse<String>> getIntegralById() async {
    try {
      var response = await HttpUtils.post(ConfigApi.getIntegral);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getIntegralById-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 获取用户积分
  static Future<ApiResponse<UserIntegral>> getUserIntegral() async {
    try {
      var response = await HttpUtils.post(ConfigApi.getUserIntegral);

      BaseResponse<UserIntegral> result = BaseResponse.fromJson(
        response,
        (json) => UserIntegral.fromJson(response),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getUserIntegral-e: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getUserIntegral-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }

  // 获取会员兑换商品列表
  static Future<ApiResponse<List<ExchangeItem>>> findVipGoodsList() async {
    try {
      var response = await HttpUtils.get(ConfigApi.findVipGoodsList);

      BaseResponse<ExchangeList> result = BaseResponse.fromJson(
        response,
        (json) => ExchangeList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("findVipGoodsList-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 获取会员积分兑换列表
  static Future<ApiResponse<List<ExchangeItem>>> getIntegralExchangeList(
    int exchangeType,
  ) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var data = {
        "version": packageInfo.version,
        "platformType": PlatformUtils.isIOS ? 1 : 2,
        "exchangeType": exchangeType,
      };
      var response = await HttpUtils.get(
        ConfigApi.getIntegralExchangeList,
        params: data,
      );

      BaseResponse<ExchangeList> result = BaseResponse.fromJson(
        response,
        (json) => ExchangeList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      debugPrint("findVipGoodsList-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 会员积分兑换指定商品
  static Future<ApiResponse<String>> getGoodsV4(
    int? goodsId,
    int exchangeType,
  ) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var data = {
        "version": packageInfo.version,
        "platformType": PlatformUtils.isIOS ? 1 : 2,
        "exchangeType": exchangeType,
        "goodsId": goodsId,
      };
      var response = await HttpUtils.get(
        ConfigApi.getGoodsV4,
        params: data,
      );

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getGoodsV4-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 用户积分明细
  static Future<ApiResponse<IntegralDetailList>> getUserIntegralDetail(
    int pageNo,
    int pageSize,
    String addOrSubtract,
  ) async {
    try {
      var data = FormData.fromMap({
        "pageNo": pageNo,
        "pageSize": pageSize,
        "addOrSubtract": addOrSubtract,
      });
      var response = await HttpUtils.post(
        ConfigApi.getIntegralDetail,
        data: data,
      );

      BaseResponse<IntegralDetailList> result = BaseResponse.fromJson(
        response,
        (json) => IntegralDetailList.fromJson(response),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getUserIntegralDetail-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 锦鲤红包列表
  static Future<ApiResponse<LuckGiftInfo>> luckyGiftInfo() async {
    try {
      var response = await HttpUtils.get(ConfigApi.luckyGiftInfo);

      BaseResponse<LuckGiftInfo> result = BaseResponse.fromJson(
        response,
        (json) => LuckGiftInfo.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("luckyGiftInfo-e: $e");
      return ApiResponse.error(e.error as AppException?);
    }
  }

  // 领取指定锦鲤红包
  static Future<ApiResponse<LuckGiftItem>> receiveLuckyGift(
    int? index,
    bool isUsePoint,
  ) async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var data = {
        "index": index,
        "isUsePoint": isUsePoint,
        "version": packageInfo.version,
      };

      var response = await HttpUtils.get(
        ConfigApi.receiveLuckyGift,
        params: data,
      );

      BaseResponse<LuckGiftItem> result = BaseResponse.fromJson(
        response,
        (json) => LuckGiftItem.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("receiveLuckyGift-e: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("receiveLuckyGift-err: $e");
      return ApiResponse.error(e as AppException?);
    }
  }

  // 获取配置开屏广告数据
  static Future<ApiResponse<dynamic>> getSplashAd() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var platformType = 0;
      if (PlatformUtils.isAndroid) {
        platformType = 2;
      } else if (PlatformUtils.isIOS) {
        platformType = 1;
      } else {
        platformType = 3;
      }
      var data = {
        "platformType": platformType,
        "version": packageInfo.version,
      };

      var response = await HttpUtils.post(
        ConfigApi.splashConfigAd,
        data: data,
      );

      debugPrint("getSplashAd: $response");

      BaseResponse<dynamic> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      debugPrint("getSplashAd: $e");
      return ApiResponse.error(e.error as AppException?);
    } catch (e) {
      debugPrint("getSplashAd-e: $e");
      return ApiResponse.error(AppException(-1, "unknown error", -1));
    }
  }
}
