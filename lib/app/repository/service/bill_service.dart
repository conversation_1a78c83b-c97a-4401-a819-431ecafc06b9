import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/api.dart';
import 'package:msmds_platform/app/repository/modals/bill/bill_list.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/common/http/app_exceptions.dart';
import 'package:msmds_platform/common/http/base/base_response.dart';
import 'package:msmds_platform/common/http/http_utils.dart';

import '../modals/bill/withdrawn_bill_list.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: bill_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/12 15:02
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/12 15:02
/// @UpdateRemark: 更新说明

class BillService {
  /// 获取用户已提现列表
  static Future<ApiResponse<WithdrawnBillList>> listUserWithdrawnBill(
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(
        BillApi.listWithdrawalRecord,
        params: data,
      );

      debugPrint("listUserWithdrawnBill: $response");

      BaseResponse<WithdrawnBillList> result = BaseResponse.fromJson(
        response,
        (json) => WithdrawnBillList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取用户钱包账单明细
  static Future<ApiResponse<BillList>> listUserWallerBill(
    int status,
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "status": status,
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(
        BillApi.listPageBill,
        params: data,
      );

      debugPrint("listUserWallerBill: $response");

      BaseResponse<BillList> result = BaseResponse.fromJson(
        response,
        (json) => BillList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
