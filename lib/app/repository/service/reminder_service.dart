import 'package:dio/dio.dart';
import 'package:msmds_platform/app/repository/modals/reminder/reminder_info.dart';

import '../../../common/http/api_response.dart';
import '../../../common/http/app_exceptions.dart';
import '../../../common/http/base/base_response.dart';
import '../../../common/http/http_utils.dart';
import '../api.dart';
import '../modals/convert/convert_goods.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: reminder_service
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 14:20
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 14:20
/// @UpdateRemark: 更新说明
class ReminderService {
  /// 获取商品订阅列表
  static Future<ApiResponse<ReminderInfo>> listReminder(
    int pageNo,
    int pageSize,
  ) async {
    try {
      var data = {
        "pageNo": pageNo,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(
        Api.goodsReminderList,
        params: data,
      );

      BaseResponse<ReminderInfo> result = BaseResponse.fromJson(
        response,
        (json) => ReminderInfo.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 保存订阅商品
  static Future<ApiResponse<bool>> saveReminderGoods(
      ConvertGoods? goods) async {
    // try {
    //   var data = {
    //     "completeGoodsId": goods?.completeGoodsId,
    //     "couponInfo": goods?.couponInfo,
    //     "couponPrice": goods?.couponPrice,
    //     "cover": goods?.cover,
    //     "goodsId": goods?.goodsId,
    //     "goodsName": goods?.goodsName,
    //     "goodsType": goods?.goodsType,
    //     "originalPrice": goods?.originalPrice,
    //     "receivedPrice": goods?.receivedPrice,
    //     "userCommission": goods?.userCommission,
    //   };
    //
    //   var response = await HttpUtils.post(
    //     Api.goodsReminderSave,
    //     data: data,
    //   );
    //
    //   BaseResponse<bool> result = BaseResponse.fromJson(
    //     response,
    //     (json) => json,
    //   );
    //
    //   return ApiResponse.completed(result.data);
    // } on DioException catch (e) {
    //   return ApiResponse.error(e.error as AppException?);
    // }
    return ApiResponse.completed(false);
  }

  /// 删除单个订阅
  static Future<ApiResponse<bool>> deleteReminder(
    int id,
  ) async {
    try {
      var data = {"id": id};

      var response = await HttpUtils.get(
        Api.deleteReminder,
        params: data,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 删除所有订阅
  static Future<ApiResponse<bool>> deleteReminderAll() async {
    try {
      var response = await HttpUtils.get(
        Api.deleteReminderAll,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 开启或者关闭商品通知
  static Future<ApiResponse<bool>> toggleReminderNotice(int state) async {
    try {
      var data = {
        "goodsOrderRemind": state,
      };

      var response = await HttpUtils.get(
        Api.toggleReminder,
        params: data,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
