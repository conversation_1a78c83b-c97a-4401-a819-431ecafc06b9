import 'package:json_annotation/json_annotation.dart';

part 'ele_activity.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: ele_activity
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/22 11:15
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/22 11:15
/// @UpdateRemark: 更新说明
@JsonSerializable()
class EleActivity {
  String? alipayMiniUrl;
  String? eleSchemeUrl;
  String? h5ShortLink;
  String? h5Url;
  String? miniQrcode;
  String? picture;
  String? tbMiniQrcode;
  String? tbQrCode;
  String? wxAppid;
  String? wxPath;

  EleActivity();

  factory EleActivity.fromJson(Map<String, dynamic> json) =>
      _$EleActivityFromJson(json);

  Map<String, dynamic> toJson() => _$EleActivityToJson(this);
}
