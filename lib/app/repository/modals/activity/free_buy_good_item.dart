import 'package:json_annotation/json_annotation.dart';

part 'free_buy_good_item.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: free_buy_good_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/28 17:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 17:23
/// @UpdateRemark: 更新说明
@JsonSerializable()
class FreeBuyGoodItem {
  String? sku;
  String? gift;
  num? discountRate;
  num? giftCount0;
  num? giftCount1;
  num? commissionRate;
  num? priceAfterCoupon;
  int? isPinGou;
  String? shopName;
  num? discount0;
  String? cover;
  List<String?>? swiperList;
  String? detailPics;
  num? couponAmount;
  num? couponLimit;
  num? priceUseGift;
  num? noVipPriceUseGift;
  int? hasCoupon;
  num? pingouPrice;
  num? wlPrice;
  num? lowestPrice;
  num? discount1;
  num? commission;
  num? noVipCommission;
  num? useGiftRateSort;
  String? couponTips;
  String? couponUrl;
  num? useGiftRate;
  num? priceAfterReceive;
  num? noVipPriceAfterReceive;
  num? extraCommission;
  String? name;
  String? logoName;
  String? skuId;
  String? tbItemId;
  String? title;
  String? volume;
  String? venderName;

  FreeBuyGoodItem();

  factory FreeBuyGoodItem.fromJson(Map<String, dynamic> json) =>
      _$FreeBuyGoodItemFromJson(json);

  Map<String, dynamic> toJson() => _$FreeBuyGoodItemToJson(this);
}
