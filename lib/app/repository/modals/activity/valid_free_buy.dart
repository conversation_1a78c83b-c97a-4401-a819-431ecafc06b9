import 'package:json_annotation/json_annotation.dart';

part 'valid_free_buy.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: valid_free_buy
/// @Description: 0元购资格
/// @Author: frankylee
/// @CreateDate: 2024/3/28 14:24
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 14:24
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ValidFreeBuy {
  int? activityType;
  String? createTime;
  String? description;
  String? expiryDate;
  String? goodsId;
  int? id;
  int? state;
  num? subsidyAmount;
  int? subsidyType;
  String? updateTime;
  String? useTime;
  int? userId;

  ValidFreeBuy();

  factory ValidFreeBuy.fromJson(Map<String, dynamic> json) =>
      _$ValidFreeBuyFromJson(json);

  Map<String, dynamic> toJson() => _$ValidFreeBuyToJson(this);
}
