import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/activity/free_buy_good_item.dart';

part 'free_buy_good.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: free_buy_good
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/28 17:21
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 17:21
/// @UpdateRemark: 更新说明
@JsonSerializable()
class FreeBuyGood {
  String? result;
  int? total;
  int? code;
  List<FreeBuyGoodItem?>? data;
  bool? success;

  FreeBuyGood();

  factory FreeBuyGood.fromJson(Map<String, dynamic> json) =>
      _$FreeBuyGoodFromJson(json);

  Map<String, dynamic> toJson() => _$FreeBuyGoodToJson(this);
}
