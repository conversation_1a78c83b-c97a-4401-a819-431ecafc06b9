// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'valid_free_buy.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ValidFreeBuy _$ValidFreeBuyFromJson(Map<String, dynamic> json) => ValidFreeBuy()
  ..activityType = json['activityType'] as int?
  ..createTime = json['createTime'] as String?
  ..description = json['description'] as String?
  ..expiryDate = json['expiryDate'] as String?
  ..goodsId = json['goodsId'] as String?
  ..id = json['id'] as int?
  ..state = json['state'] as int?
  ..subsidyAmount = json['subsidyAmount'] as num?
  ..subsidyType = json['subsidyType'] as int?
  ..updateTime = json['updateTime'] as String?
  ..useTime = json['useTime'] as String?
  ..userId = json['userId'] as int?;

Map<String, dynamic> _$ValidFreeBuyToJson(ValidFreeBuy instance) =>
    <String, dynamic>{
      'activityType': instance.activityType,
      'createTime': instance.createTime,
      'description': instance.description,
      'expiryDate': instance.expiryDate,
      'goodsId': instance.goodsId,
      'id': instance.id,
      'state': instance.state,
      'subsidyAmount': instance.subsidyAmount,
      'subsidyType': instance.subsidyType,
      'updateTime': instance.updateTime,
      'useTime': instance.useTime,
      'userId': instance.userId,
    };
