// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ele_activity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EleActivity _$EleActivityFromJson(Map<String, dynamic> json) => EleActivity()
  ..alipayMiniUrl = json['alipayMiniUrl'] as String?
  ..eleSchemeUrl = json['eleSchemeUrl'] as String?
  ..h5ShortLink = json['h5ShortLink'] as String?
  ..h5Url = json['h5Url'] as String?
  ..miniQrcode = json['miniQrcode'] as String?
  ..picture = json['picture'] as String?
  ..tbMiniQrcode = json['tbMiniQrcode'] as String?
  ..tbQrCode = json['tbQrCode'] as String?
  ..wxAppid = json['wxAppid'] as String?
  ..wxPath = json['wxPath'] as String?;

Map<String, dynamic> _$EleActivityToJson(EleActivity instance) =>
    <String, dynamic>{
      'alipayMiniUrl': instance.alipayMiniUrl,
      'eleSchemeUrl': instance.eleSchemeUrl,
      'h5ShortLink': instance.h5ShortLink,
      'h5Url': instance.h5Url,
      'miniQrcode': instance.miniQrcode,
      'picture': instance.picture,
      'tbMiniQrcode': instance.tbMiniQrcode,
      'tbQrCode': instance.tbQrCode,
      'wxAppid': instance.wxAppid,
      'wxPath': instance.wxPath,
    };
