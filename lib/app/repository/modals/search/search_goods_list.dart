import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/search/search_goods.dart';

part 'search_goods_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_goods_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 10:38
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 10:38
/// @UpdateRemark: 更新说明
@JsonSerializable()
class SearchGoodsList {
  String? bigTitle;
  List<SearchGoods>? goodsInfoList;
  SearchGoodsList();

  factory SearchGoodsList.fromJson(Map<String, dynamic> json) =>
      _$SearchGoodsListFromJson(json);

  Map<String, dynamic> toJson() => _$SearchGoodsListToJson(this);
}
