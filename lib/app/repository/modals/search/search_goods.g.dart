// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_goods.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SearchGoods _$SearchGoodsFromJson(Map<String, dynamic> json) => SearchGoods()
  ..goodsId = json['goodsId'] as String?
  ..title = json['title'] as String?
  ..coupon = json['coupon'] as String?
  ..couponAmount = json['couponAmount'] as num?
  ..commission = json['commission'] as num?
  ..redPacketAmount = json['redPacketAmount'] as num?
  ..priceAfterReceive = json['priceAfterReceive'] as num?
  ..priceAfterCoupon = json['priceAfterCoupon'] as num?
  ..originalPrice = json['originalPrice'] as num?
  ..price = json['price'] as num?
  ..platformTag = json['platformTag'] as String?
  ..platformType = json['platformType'] as int?
  ..shopName = json['shopName'] as String?
  ..salesVolume = json['salesVolume'] as String?
  ..salesVolumeNum = json['salesVolumeNum'] as int?
  ..couponUrl = json['couponUrl'] as String?
  ..imgUrl = json['imgUrl'] as String?;

Map<String, dynamic> _$SearchGoodsToJson(SearchGoods instance) =>
    <String, dynamic>{
      'goodsId': instance.goodsId,
      'title': instance.title,
      'coupon': instance.coupon,
      'couponAmount': instance.couponAmount,
      'commission': instance.commission,
      'redPacketAmount': instance.redPacketAmount,
      'priceAfterReceive': instance.priceAfterReceive,
      'priceAfterCoupon': instance.priceAfterCoupon,
      'originalPrice': instance.originalPrice,
      'price': instance.price,
      'platformTag': instance.platformTag,
      'platformType': instance.platformType,
      'shopName': instance.shopName,
      'salesVolume': instance.salesVolume,
      'salesVolumeNum': instance.salesVolumeNum,
      'couponUrl': instance.couponUrl,
      'imgUrl': instance.imgUrl,
    };
