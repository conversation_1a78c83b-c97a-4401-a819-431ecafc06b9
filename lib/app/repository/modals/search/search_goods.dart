import 'package:json_annotation/json_annotation.dart';

part 'search_goods.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: search_goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 10:34
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 10:34
/// @UpdateRemark: 更新说明
@JsonSerializable()
class SearchGoods {
  String? goodsId;
  String? title;
  String? coupon;
  num? couponAmount;
  num? commission;
  num? redPacketAmount;
  num? priceAfterReceive;
  num? priceAfterCoupon;
  num? originalPrice;
  num? price;
  String? platformTag;
  int? platformType;
  String? shopName;
  String? salesVolume;
  int? salesVolumeNum;
  String? couponUrl;
  String? imgUrl;

  SearchGoods();

  factory SearchGoods.fromJson(Map<String, dynamic> json) =>
      _$SearchGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$SearchGoodsToJson(this);
}
