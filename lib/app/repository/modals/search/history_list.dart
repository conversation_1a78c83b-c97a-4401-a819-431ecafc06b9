import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/search/history.dart';

part 'history_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: history_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/21 10:47
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/21 10:47
/// @UpdateRemark: 更新说明
@JsonSerializable()
class HistoryList {
  List<History>? data;

  HistoryList();

  factory HistoryList.fromJson(Map<String, dynamic> json) =>
      _$HistoryListFromJson(json);

  Map<String, dynamic> toJson() => _$HistoryListToJson(this);
}
