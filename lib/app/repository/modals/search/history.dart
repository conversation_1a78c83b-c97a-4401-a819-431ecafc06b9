import 'package:json_annotation/json_annotation.dart';

part 'history.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: history
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/21 10:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/21 10:45
/// @UpdateRemark: 更新说明
@JsonSerializable()
class History {
  int? id;
  String? searchTerm;
  int? state;

  History();

  factory History.fromJson(Map<String, dynamic> json) =>
      _$HistoryFromJson(json);

  Map<String, dynamic> toJson() => _$HistoryTo<PERSON>son(this);
}
