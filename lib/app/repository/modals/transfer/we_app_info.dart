import 'package:json_annotation/json_annotation.dart';

part 'we_app_info.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: we_app_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/15 14:21
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/15 14:21
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WeAppInfo {
  String? appId;
  String? bannerUrl;
  String? desc;
  String? pagePath;
  String? sourceDisplayName;
  String? title;
  String? userName;
  String? weAppIconUrl;

  WeAppInfo();

  factory WeAppInfo.fromJson(Map<String, dynamic> json) =>
      _$WeAppInfoFromJson(json);

  Map<String, dynamic> toJson() => _$WeAppInfoToJson(this);
}
