import 'package:json_annotation/json_annotation.dart';

part 'ddk_cms_single_url_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: ddk_cms_single_url_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:43
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:43
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkCmsSingleUrlList {
  String? mobileShortUrl;
  String? mobileUrl;
  String? schemaUrl;
  String? shortUrl;
  String? tzSchemaUrl;
  String? url;

  DdkCmsSingleUrlList();

  factory DdkCmsSingleUrlList.fromJson(Map<String, dynamic> json) =>
      _$DdkCmsSingleUrlListFromJson(json);

  Map<String, dynamic> toJson() => _$DdkCmsSingleUrlListToJson(this);
}
