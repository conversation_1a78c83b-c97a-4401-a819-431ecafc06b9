import 'package:json_annotation/json_annotation.dart';

part 'qq_app_info.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: qq_app_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/15 14:21
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/15 14:21
/// @UpdateRemark: 更新说明
@JsonSerializable()
class QqAppInfo {
  String? appId;
  String? bannerUrl;
  String? desc;
  String? pagePath;
  String? qqAppIconUrl;
  String? sourceDisplayName;
  String? title;
  String? userName;

  QqAppInfo();

  factory QqAppInfo.fromJson(Map<String, dynamic> json) =>
      _$QqAppInfoFromJson(json);

  Map<String, dynamic> toJson() => _$QqAppInfoToJson(this);
}
