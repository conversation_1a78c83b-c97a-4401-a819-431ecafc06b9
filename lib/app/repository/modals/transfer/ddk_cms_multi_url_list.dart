import 'package:json_annotation/json_annotation.dart';

part 'ddk_cms_multi_url_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: ddk_cms_multi_url_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:41
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:41
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkCmsMultiUrlList {
  String? mobileShortUrl;
  String? mobileUrl;
  String? schemaUrl;
  String? shortUrl;
  String? tzSchemaUrl;
  String? url;

  DdkCmsMultiUrlList();

  factory DdkCmsMultiUrlList.fromJson(Map<String, dynamic> json) =>
      _$DdkCmsMultiUrlListFromJson(json);

  Map<String, dynamic> toJson() => _$DdkCmsMultiUrlListToJson(this);
}
