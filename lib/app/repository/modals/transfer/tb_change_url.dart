import 'package:json_annotation/json_annotation.dart';

part 'tb_change_url.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: tb_change_url
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/26 10:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/26 10:46
/// @UpdateRemark: 更新说明
@JsonSerializable()
class TbChangeUrl {
  String? couponClickUrl;
  String? couponShortUrl;
  String? globalTbkPwd;
  String? iosTbkPwd;
  String? numIid;
  String? sclickUrl;
  String? tbkPwd;

  TbChangeUrl();

  factory TbChangeUrl.fromJson(Map<String, dynamic> json) =>
      _$TbChangeUrlFromJson(json);

  Map<String, dynamic> toJson() => _$TbChangeUrlToJson(this);
}
