import 'package:json_annotation/json_annotation.dart';

part 'tb_activity_url.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: tb_activity_url
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/9 14:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/9 14:35
/// @UpdateRemark: 更新说明
@JsonSerializable()
class TbActivityUrl {
  String? clickUrl;

  TbActivityUrl();

  factory TbActivityUrl.fromJson(Map<String, dynamic> json) =>
      _$TbActivityUrlFromJson(json);

  Map<String, dynamic> toJson() => _$TbActivityUrlToJson(this);
}
