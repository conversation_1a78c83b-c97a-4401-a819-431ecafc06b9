import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_cms_multi_url_list.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_cms_single_url_list.dart';
import 'package:msmds_platform/app/repository/modals/transfer/we_app_info.dart';

part 'ddk_cms_url_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: ddk_cms_url_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:39
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:39
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkCmsUrlList {
  String? mobileShortUrl;
  String? mobileUrl;
  String? multiGroupMobileShortUrl;
  String? multiGroupMobileUrl;
  String? multiGroupShortUrl;
  String? multiGroupUrl;
  DdkCmsMultiUrlList? multiUrlList;
  String? schemaUrl;
  String? shortUrl;
  String? sign;
  DdkCmsSingleUrlList? singleUrlList;
  String? url;
  WeAppInfo? weAppInfo;

  DdkCmsUrlList();

  factory DdkCmsUrlList.fromJson(Map<String, dynamic> json) =>
      _$DdkCmsUrlListFromJson(json);

  Map<String, dynamic> toJson() => _$DdkCmsUrlListToJson(this);
}
