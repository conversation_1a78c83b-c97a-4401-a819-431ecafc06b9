// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ddk_resource_url.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DdkResourceUrl _$DdkResourceUrlFromJson(Map<String, dynamic> json) =>
    DdkResourceUrl()
      ..multiUrlList = json['multiUrlList'] == null
          ? null
          : DdkResourceMultiUrlList.fromJson(
              json['multiUrlList'] as Map<String, dynamic>)
      ..sign = json['sign'] as String?
      ..singleUrlList = json['singleUrlList'] == null
          ? null
          : DdkResourceSingleUrlList.fromJson(
              json['singleUrlList'] as Map<String, dynamic>)
      ..weAppInfo = json['weAppInfo'] == null
          ? null
          : WeAppInfo.fromJson(json['weAppInfo'] as Map<String, dynamic>);

Map<String, dynamic> _$DdkResourceUrlToJson(DdkResourceUrl instance) =>
    <String, dynamic>{
      'multiUrlList': instance.multiUrlList,
      'sign': instance.sign,
      'singleUrlList': instance.singleUrlList,
      'weAppInfo': instance.weAppInfo,
    };
