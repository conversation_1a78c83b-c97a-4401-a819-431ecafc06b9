// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ddk_cms_url_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DdkCmsUrlList _$DdkCmsUrlListFromJson(Map<String, dynamic> json) =>
    DdkCmsUrlList()
      ..mobileShortUrl = json['mobileShortUrl'] as String?
      ..mobileUrl = json['mobileUrl'] as String?
      ..multiGroupMobileShortUrl = json['multiGroupMobileShortUrl'] as String?
      ..multiGroupMobileUrl = json['multiGroupMobileUrl'] as String?
      ..multiGroupShortUrl = json['multiGroupShortUrl'] as String?
      ..multiGroupUrl = json['multiGroupUrl'] as String?
      ..multiUrlList = json['multiUrlList'] == null
          ? null
          : DdkCmsMultiUrlList.fromJson(
              json['multiUrlList'] as Map<String, dynamic>)
      ..schemaUrl = json['schemaUrl'] as String?
      ..shortUrl = json['shortUrl'] as String?
      ..sign = json['sign'] as String?
      ..singleUrlList = json['singleUrlList'] == null
          ? null
          : DdkCmsSingleUrlList.fromJson(
              json['singleUrlList'] as Map<String, dynamic>)
      ..url = json['url'] as String?
      ..weAppInfo = json['weAppInfo'] == null
          ? null
          : WeAppInfo.fromJson(json['weAppInfo'] as Map<String, dynamic>);

Map<String, dynamic> _$DdkCmsUrlListToJson(DdkCmsUrlList instance) =>
    <String, dynamic>{
      'mobileShortUrl': instance.mobileShortUrl,
      'mobileUrl': instance.mobileUrl,
      'multiGroupMobileShortUrl': instance.multiGroupMobileShortUrl,
      'multiGroupMobileUrl': instance.multiGroupMobileUrl,
      'multiGroupShortUrl': instance.multiGroupShortUrl,
      'multiGroupUrl': instance.multiGroupUrl,
      'multiUrlList': instance.multiUrlList,
      'schemaUrl': instance.schemaUrl,
      'shortUrl': instance.shortUrl,
      'sign': instance.sign,
      'singleUrlList': instance.singleUrlList,
      'url': instance.url,
      'weAppInfo': instance.weAppInfo,
    };
