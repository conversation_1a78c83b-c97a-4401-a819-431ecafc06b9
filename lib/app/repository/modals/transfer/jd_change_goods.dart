import 'package:json_annotation/json_annotation.dart';

part 'jd_change_goods.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.transfer
/// @ClassName: jd_change_goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/10 16:15
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/10 16:15
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdChangeGoods {
  String? path;
  String? url;

  JdChangeGoods();

  factory JdChangeGoods.fromJson(Map<String, dynamic> json) =>
      _$JdChangeGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$JdChangeGoodsToJson(this);
}
