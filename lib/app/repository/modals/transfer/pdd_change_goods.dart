import 'package:json_annotation/json_annotation.dart';

part 'pdd_change_goods.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.transfer
/// @ClassName: pdd_change_goods
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/14 14:18
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/14 14:18
/// @UpdateRemark: 更新说明
@JsonSerializable()
class PddChangeGoods {
  String? wxCouponUrl;
  String? appShortCouponUrl;
  String? goods_id;
  String? couponUrl;
  String? appCouponUrl;
  String? miniUrl;
  String? wxShortCouponUrl;

  PddChangeGoods();

  factory PddChangeGoods.fromJson(Map<String, dynamic> json) =>
      _$PddChangeGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$PddChangeGoodsToJson(this);
}
