import 'package:json_annotation/json_annotation.dart';

part 'jd_activity_url.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: jd_activity_url
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/9 14:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/9 14:35
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdActivityUrl {
  String? shortUrl;

  JdActivityUrl();

  factory JdActivityUrl.fromJson(Map<String, dynamic> json) =>
      _$JdActivityUrlFromJson(json);

  Map<String, dynamic> toJson() => _$JdActivityUrlToJson(this);
}
