// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pdd_change_url.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PddChangeUrl _$PddChangeUrlFromJson(Map<String, dynamic> json) => PddChangeUrl()
  ..mobileShortUrl = json['mobileShortUrl'] as String?
  ..mobileUrl = json['mobileUrl'] as String?
  ..qqAppInfo = json['qqAppInfo'] == null
      ? null
      : QqAppInfo.fromJson(json['qqAppInfo'] as Map<String, dynamic>)
  ..schemaUrl = json['schemaUrl'] as String?
  ..shareImageUrl = json['shareImageUrl'] as String?
  ..shortUrl = json['shortUrl'] as String?
  ..tzSchemaUrl = json['tzSchemaUrl'] as String?
  ..url = json['url'] as String?
  ..weAppInfo = json['weAppInfo'] == null
      ? null
      : WeAppInfo.fromJson(json['weAppInfo'] as Map<String, dynamic>)
  ..weixinCode = json['weixinCode'] as String?
  ..weixinShortLink = json['weixinShortLink'] as String?;

Map<String, dynamic> _$PddChangeUrlToJson(PddChangeUrl instance) =>
    <String, dynamic>{
      'mobileShortUrl': instance.mobileShortUrl,
      'mobileUrl': instance.mobileUrl,
      'qqAppInfo': instance.qqAppInfo,
      'schemaUrl': instance.schemaUrl,
      'shareImageUrl': instance.shareImageUrl,
      'shortUrl': instance.shortUrl,
      'tzSchemaUrl': instance.tzSchemaUrl,
      'url': instance.url,
      'weAppInfo': instance.weAppInfo,
      'weixinCode': instance.weixinCode,
      'weixinShortLink': instance.weixinShortLink,
    };
