// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'qq_app_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QqAppInfo _$QqAppInfoFromJson(Map<String, dynamic> json) => QqAppInfo()
  ..appId = json['appId'] as String?
  ..bannerUrl = json['bannerUrl'] as String?
  ..desc = json['desc'] as String?
  ..pagePath = json['pagePath'] as String?
  ..qqAppIconUrl = json['qqAppIconUrl'] as String?
  ..sourceDisplayName = json['sourceDisplayName'] as String?
  ..title = json['title'] as String?
  ..userName = json['userName'] as String?;

Map<String, dynamic> _$QqAppInfoToJson(QqAppInfo instance) => <String, dynamic>{
      'appId': instance.appId,
      'bannerUrl': instance.bannerUrl,
      'desc': instance.desc,
      'pagePath': instance.pagePath,
      'qqAppIconUrl': instance.qqAppIconUrl,
      'sourceDisplayName': instance.sourceDisplayName,
      'title': instance.title,
      'userName': instance.userName,
    };
