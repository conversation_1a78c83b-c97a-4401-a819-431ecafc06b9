import 'package:json_annotation/json_annotation.dart';

part 'dy_change_goods.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.transfer
/// @ClassName: dy_change_goods
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/14 15:14
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/14 15:14
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DyChangeGoods {
  String? dy_deeplink;
  String? dy_zlink;

  DyChangeGoods();

  factory DyChangeGoods.fromJson(Map<String, dynamic> json) =>
      _$DyChangeGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$DyChangeGoodsToJson(this);
}

