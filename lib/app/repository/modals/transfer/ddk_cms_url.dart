import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_cms_url_list.dart';

part 'ddk_cms_url.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: ddk_cms_url
/// @Description: 获取商城-频道推广链接
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:37
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:37
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkCmsUrl {
  int? total;
  List<DdkCmsUrlList?>? urlList;

  DdkCmsUrl();

  factory DdkCmsUrl.fromJson(Map<String, dynamic> json) =>
      _$DdkCmsUrlFromJson(json);

  Map<String, dynamic> toJson() => _$DdkCmsUrlToJson(this);
}
