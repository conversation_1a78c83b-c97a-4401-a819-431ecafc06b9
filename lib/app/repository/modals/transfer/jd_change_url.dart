import 'package:json_annotation/json_annotation.dart';

part 'jd_change_url.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: jd_change_url
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 16:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 16:46
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdChangeUrl {
  String? clickURL;
  String? jcommand;
  String? jshortCommand;
  String? shortURL;
  String? weChatShortLink;

  JdChangeUrl();

  factory JdChangeUrl.fromJson(Map<String, dynamic> json) =>
      _$JdChangeUrlFromJson(json);

  Map<String, dynamic> toJson() => _$JdChangeUrlToJson(this);
}
