import 'package:json_annotation/json_annotation.dart';

part 'ddk_resource_single_url_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: ddk_resource_single_url_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:49
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkResourceSingleUrlList {
  String? shortUrl;
  String? url;

  DdkResourceSingleUrlList();

  factory DdkResourceSingleUrlList.fromJson(Map<String, dynamic> json) =>
      _$DdkResourceSingleUrlListFromJson(json);

  Map<String, dynamic> toJson() => _$DdkResourceSingleUrlListToJson(this);
}
