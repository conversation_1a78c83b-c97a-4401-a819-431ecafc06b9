import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/transfer/qq_app_info.dart';
import 'package:msmds_platform/app/repository/modals/transfer/we_app_info.dart';

part 'pdd_change_url.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: pdd_change_url
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/15 14:20
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/15 14:20
/// @UpdateRemark: 更新说明
@JsonSerializable()
class PddChangeUrl {
  String? mobileShortUrl;
  String? mobileUrl;
  QqAppInfo? qqAppInfo;
  String? schemaUrl;
  String? shareImageUrl;
  String? shortUrl;
  String? tzSchemaUrl;
  String? url;
  WeAppInfo? weAppInfo;
  String? weixinCode;
  String? weixinShortLink;

  PddChangeUrl();

  factory PddChangeUrl.fromJson(Map<String, dynamic> json) =>
      _$PddChangeUrlFromJson(json);

  Map<String, dynamic> toJson() => _$PddChangeUrlToJson(this);
}
