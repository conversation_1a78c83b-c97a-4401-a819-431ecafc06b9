import 'package:json_annotation/json_annotation.dart';

part 'wph_change_goods.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.transfer
/// @ClassName: wph_change_goods
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/14 14:36
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/14 14:36
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WphChangeGoods {
  String? deeplinkUrl;
  String? url;

  WphChangeGoods();

  factory WphChangeGoods.fromJson(Map<String, dynamic> json) =>
      _$WphChangeGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$WphChangeGoodsToJson(this);
}

