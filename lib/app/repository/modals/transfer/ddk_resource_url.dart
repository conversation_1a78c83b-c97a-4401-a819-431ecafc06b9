import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_resource_multi_url_list.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_resource_single_url_list.dart';
import 'package:msmds_platform/app/repository/modals/transfer/we_app_info.dart';

part 'ddk_resource_url.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: ddk_resource_url
/// @Description: 生成多多进宝频道推广
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:47
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:47
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkResourceUrl {
  DdkResourceMultiUrlList? multiUrlList;
  String? sign;
  DdkResourceSingleUrlList? singleUrlList;
  WeAppInfo? weAppInfo;

  DdkResourceUrl();

  factory DdkResourceUrl.fromJson(Map<String, dynamic> json) =>
      _$DdkResourceUrlFromJson(json);

  Map<String, dynamic> toJson() => _$DdkResourceUrlToJson(this);
}
