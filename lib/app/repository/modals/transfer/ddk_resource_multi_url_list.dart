import 'package:json_annotation/json_annotation.dart';

part 'ddk_resource_multi_url_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: ddk_resource_multi_url_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 10:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 10:49
/// @UpdateRemark: 更新说明
@JsonSerializable()
class DdkResourceMultiUrlList {
  String? shortUrl;
  String? url;

  DdkResourceMultiUrlList();

  factory DdkResourceMultiUrlList.fromJson(Map<String, dynamic> json) =>
      _$DdkResourceMultiUrlListFromJson(json);

  Map<String, dynamic> toJson() => _$DdkResourceMultiUrlListToJson(this);
}
