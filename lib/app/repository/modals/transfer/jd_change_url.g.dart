// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jd_change_url.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JdChangeUrl _$JdChangeUrlFromJson(Map<String, dynamic> json) => JdChangeUrl()
  ..clickURL = json['clickURL'] as String?
  ..jcommand = json['jcommand'] as String?
  ..jshortCommand = json['jshortCommand'] as String?
  ..shortURL = json['shortURL'] as String?
  ..weChatShortLink = json['weChatShortLink'] as String?;

Map<String, dynamic> _$JdChangeUrlToJson(JdChangeUrl instance) =>
    <String, dynamic>{
      'clickURL': instance.clickURL,
      'jcommand': instance.jcommand,
      'jshortCommand': instance.jshortCommand,
      'shortURL': instance.shortURL,
      'weChatShortLink': instance.weChatShortLink,
    };
