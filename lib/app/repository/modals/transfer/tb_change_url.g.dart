// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tb_change_url.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TbChangeUrl _$TbChangeUrlFromJson(Map<String, dynamic> json) => TbChangeUrl()
  ..couponClickUrl = json['couponClickUrl'] as String?
  ..couponShortUrl = json['couponShortUrl'] as String?
  ..globalTbkPwd = json['globalTbkPwd'] as String?
  ..iosTbkPwd = json['iosTbkPwd'] as String?
  ..numIid = json['numIid'] as String?
  ..sclickUrl = json['sclickUrl'] as String?
  ..tbkPwd = json['tbkPwd'] as String?;

Map<String, dynamic> _$TbChangeUrlToJson(TbChangeUrl instance) =>
    <String, dynamic>{
      'couponClickUrl': instance.couponClickUrl,
      'couponShortUrl': instance.couponShortUrl,
      'globalTbkPwd': instance.globalTbkPwd,
      'iosTbkPwd': instance.iosTbkPwd,
      'numIid': instance.numIid,
      'sclickUrl': instance.sclickUrl,
      'tbkPwd': instance.tbkPwd,
    };
