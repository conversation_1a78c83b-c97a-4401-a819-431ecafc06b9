import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/convert/convert_goods.dart';

part 'convert_data.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: convert_goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/1 11:10
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/1 11:10
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ConvertData {
  // 全网搜索商品信息（未识别到商品则进行全网搜索）
  ConvertGoods? aggregateSearchGoods;
  // 卡片文案
  String? cardText;
  // 是否比价
  bool? comparePrice;
  // 顶部标题
  String? dialogTitle;
  // 用户粘贴板内容
  String? displaySearchWord;
  // 截取的搜索词
  String? displayStandardSearchWord;
  // 商品信息
  ConvertGoods? goods;
  // 识别成功数量
  int? goodsCount;
  // 是否展示粘贴板
  bool? isShow;
  // ture为选中“全网比价”tab，false为选中“搜优惠券”tab
  bool? isUrlStart;
  // 左按钮文案
  String? leftButtonText;
  // 左按钮提示
  String? leftButtonTips;
  // 右按钮文案
  String? rightButtonText;
  // 右按钮提示
  String? rightButtonTips;
  // 搜索按钮文案
  String? searchBtn;
  // 2为纯文字类型，1为带链接或带淘口令类型
  int? searchType;
  // 搜索词
  String? searchWord;

  ConvertData();

  factory ConvertData.fromJson(Map<String, dynamic> json) =>
      _$ConvertDataFromJson(json);

  Map<String, dynamic> toJson() => _$ConvertDataToJson(this);
}
