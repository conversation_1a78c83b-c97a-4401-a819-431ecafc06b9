import 'package:json_annotation/json_annotation.dart';

part 'jd_can_use_label.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.convert
/// @ClassName: jd_can_use_label
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/10 10:37
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/10 10:37
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdCanUseLabel {
  // 结束时间
  int? endTime;
  // 标签名字
  String? labelName;
  // 促销标签
  String? promotionLabel;
  // 促销标签id
  int? promotionLabelId;
  // 开始时间
  int? startTime;

  JdCanUseLabel();

  factory JdCanUseLabel.fromJson(Map<String, dynamic> json) =>
      _$JdCanUseLabelFromJson(json);

  Map<String, dynamic> toJson() => _$JdCanUseLabelToJson(this);
}

