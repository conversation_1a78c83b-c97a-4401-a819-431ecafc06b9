import 'package:json_annotation/json_annotation.dart';

part 'goods_coupon.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: goods_coupon
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/1 11:11
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/1 11:11
/// @UpdateRemark: 更新说明
@JsonSerializable()
class GoodsCoupon {
  num? couponAmount;
  String? couponAmountInfo;
  String? couponInfo;
  num? couponLimit;
  int? couponType;
  String? couponUrl;
  String? endTime;
  int? platformType;
  int? remainNum;
  String? startTime;
  int? totalNum;

  GoodsCoupon();

  factory GoodsCoupon.fromJson(Map<String, dynamic> json) =>
      _$GoodsCouponFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsCouponToJson(this);
}
