import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/convert/jd_can_use_coupons.dart';
import 'package:msmds_platform/app/repository/modals/convert/jd_can_use_label.dart';

part 'convert_goods.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: convert_goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/1 11:10
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/1 11:10
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ConvertGoods {
  // 唯品会adCode标识获取推广物料的来源，从物料输出接口获取，如当前转链的物料不是从联盟物料接口获取，则传默认值adCode(工具商接口传vendoapi，渠道商接口传unionapi)
  String? adCode;
  // 场景id
  String? bizSceneId;
  // 是否比价
  bool? comparePrice;
  // 优惠券金额
  num? couponAmount;
  // 优惠券金额具体信息 （券： ¥30 折扣：9.5折）
  String? couponAmountInfo;
  // 优惠券信息
  String? couponInfo;
  // 兼容淘宝无返利商品app跳转异常字段
  String? couponUrl;
  // 商品id
  String? goodsId;
  // 商品图片
  String? goodsImg;
  // 商品名字
  String? goodsName;
  // 商品唯一id
  String? goodsUniqueId;
  // jd可使用优惠券集合
  List<JdCanUseCoupons>? jdCanUseCoupons;
  // jd可使用标签信息集合
  List<JdCanUseLabel>? jdCanUseLabelInfoList;
  // 本地生活商品(抖音才有)
  bool? lifeGoods;
  // 非会员佣金
  num? noVipCommission;
  // 平台类型： 2、京东 6、淘宝 10、拼多多
  int? platformType;
  // 商品价格
  num? price;
  // 商品券后价
  num? priceAfterCoupon;
  // 到手价(券后价-佣金)
  num? priceAfterReceive;
  // 红包金额
  num? redPacketAmount;
  // 会员佣金
  num? vipCommission;
  // 唯品会通过解析链接获取商品ID、原始链接等参数，再入参生成跟单链接的方式实现链接转链功能,使用解析接口解析现有链接，以获取RID参数。在新链接中入参已获取的RID并生成新的跟单链接。
  String? vipRid;

  ConvertGoods();

  factory ConvertGoods.fromJson(Map<String, dynamic> json) =>
      _$ConvertGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$ConvertGoodsToJson(this);
}
