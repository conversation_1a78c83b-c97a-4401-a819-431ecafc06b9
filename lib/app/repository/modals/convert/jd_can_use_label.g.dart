// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jd_can_use_label.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JdCanUseLabel _$JdCanUseLabelFromJson(Map<String, dynamic> json) =>
    JdCanUseLabel()
      ..endTime = json['endTime'] as int?
      ..labelName = json['labelName'] as String?
      ..promotionLabel = json['promotionLabel'] as String?
      ..promotionLabelId = json['promotionLabelId'] as int?
      ..startTime = json['startTime'] as int?;

Map<String, dynamic> _$JdCanUseLabelToJson(JdCanUseLabel instance) =>
    <String, dynamic>{
      'endTime': instance.endTime,
      'labelName': instance.labelName,
      'promotionLabel': instance.promotionLabel,
      'promotionLabelId': instance.promotionLabelId,
      'startTime': instance.startTime,
    };
