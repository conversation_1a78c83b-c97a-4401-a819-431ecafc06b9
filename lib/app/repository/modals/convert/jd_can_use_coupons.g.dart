// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jd_can_use_coupons.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JdCanUseCoupons _$JdCanUseCouponsFromJson(Map<String, dynamic> json) =>
    JdCanUseCoupons()
      ..couponAmount = json['couponAmount'] as num?
      ..couponAmountInfo = json['couponAmountInfo'] as String?
      ..couponInfo = json['couponInfo'] as String?
      ..couponLimit = json['couponLimit'] as num?
      ..couponStyle = json['couponStyle'] as int?
      ..couponType = json['couponType'] as int?
      ..couponUrl = json['couponUrl'] as String?
      ..endTime = json['endTime'] as String?
      ..platformType = json['platformType'] as int?
      ..remainNum = json['remainNum'] as int?
      ..startTime = json['startTime'] as String?
      ..totalNum = json['totalNum'] as int?;

Map<String, dynamic> _$JdCanUseCouponsToJson(JdCanUseCoupons instance) =>
    <String, dynamic>{
      'couponAmount': instance.couponAmount,
      'couponAmountInfo': instance.couponAmountInfo,
      'couponInfo': instance.couponInfo,
      'couponLimit': instance.couponLimit,
      'couponStyle': instance.couponStyle,
      'couponType': instance.couponType,
      'couponUrl': instance.couponUrl,
      'endTime': instance.endTime,
      'platformType': instance.platformType,
      'remainNum': instance.remainNum,
      'startTime': instance.startTime,
      'totalNum': instance.totalNum,
    };
