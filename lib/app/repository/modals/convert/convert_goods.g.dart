// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'convert_goods.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConvertGoods _$ConvertGoodsFromJson(Map<String, dynamic> json) => ConvertGoods()
  ..adCode = json['adCode'] as String?
  ..bizSceneId = json['bizSceneId'] as String?
  ..comparePrice = json['comparePrice'] as bool?
  ..couponAmount = json['couponAmount'] as num?
  ..couponAmountInfo = json['couponAmountInfo'] as String?
  ..couponInfo = json['couponInfo'] as String?
  ..couponUrl = json['couponUrl'] as String?
  ..goodsId = json['goodsId'] as String?
  ..goodsImg = json['goodsImg'] as String?
  ..goodsName = json['goodsName'] as String?
  ..goodsUniqueId = json['goodsUniqueId'] as String?
  ..jdCanUseCoupons = (json['jdCanUseCoupons'] as List<dynamic>?)
      ?.map((e) => JdCanUseCoupons.fromJson(e as Map<String, dynamic>))
      .toList()
  ..jdCanUseLabelInfoList = (json['jdCanUseLabelInfoList'] as List<dynamic>?)
      ?.map((e) => JdCanUseLabel.fromJson(e as Map<String, dynamic>))
      .toList()
  ..lifeGoods = json['lifeGoods'] as bool?
  ..noVipCommission = json['noVipCommission'] as num?
  ..platformType = json['platformType'] as int?
  ..price = json['price'] as num?
  ..priceAfterCoupon = json['priceAfterCoupon'] as num?
  ..priceAfterReceive = json['priceAfterReceive'] as num?
  ..redPacketAmount = json['redPacketAmount'] as num?
  ..vipCommission = json['vipCommission'] as num?
  ..vipRid = json['vipRid'] as String?;

Map<String, dynamic> _$ConvertGoodsToJson(ConvertGoods instance) =>
    <String, dynamic>{
      'adCode': instance.adCode,
      'bizSceneId': instance.bizSceneId,
      'comparePrice': instance.comparePrice,
      'couponAmount': instance.couponAmount,
      'couponAmountInfo': instance.couponAmountInfo,
      'couponInfo': instance.couponInfo,
      'couponUrl': instance.couponUrl,
      'goodsId': instance.goodsId,
      'goodsImg': instance.goodsImg,
      'goodsName': instance.goodsName,
      'goodsUniqueId': instance.goodsUniqueId,
      'jdCanUseCoupons': instance.jdCanUseCoupons,
      'jdCanUseLabelInfoList': instance.jdCanUseLabelInfoList,
      'lifeGoods': instance.lifeGoods,
      'noVipCommission': instance.noVipCommission,
      'platformType': instance.platformType,
      'price': instance.price,
      'priceAfterCoupon': instance.priceAfterCoupon,
      'priceAfterReceive': instance.priceAfterReceive,
      'redPacketAmount': instance.redPacketAmount,
      'vipCommission': instance.vipCommission,
      'vipRid': instance.vipRid,
    };
