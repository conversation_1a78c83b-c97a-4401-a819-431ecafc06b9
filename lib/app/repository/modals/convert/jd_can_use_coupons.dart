import 'package:json_annotation/json_annotation.dart';

part 'jd_can_use_coupons.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.convert
/// @ClassName: jd_can_use_coupons
/// @Description: jd可使用优惠券集合
/// @Author: frankylee
/// @CreateDate: 2024/9/10 10:31
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/10 10:31
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdCanUseCoupons {
  // 优惠券金额
  num? couponAmount;
  // 优惠券金额信息 （券：2元 折扣：9.5折）
  String? couponAmountInfo;
  // 优惠券满减信息
  String? couponInfo;
  // 优惠券使用门槛
  num? couponLimit;
  // 优惠券样式 1.默认商品券(满3减2元) 3.折扣券（满3元9.5折）
  int? couponStyle;
  // 优惠券类型
  int? couponType;
  // 优惠券链接
  String? couponUrl;
  // 优惠券结束时间
  String? endTime;
  // 优惠券平台类型
  int? platformType;
  // 优惠券剩余数量
  int? remainNum;
  // 优惠券开始时间
  String? startTime;
  // 优惠券总量
  int? totalNum;

  JdCanUseCoupons();

  factory JdCanUseCoupons.fromJson(Map<String, dynamic> json) =>
      _$JdCanUseCouponsFromJson(json);

  Map<String, dynamic> toJson() => _$JdCanUseCouponsToJson(this);
}

