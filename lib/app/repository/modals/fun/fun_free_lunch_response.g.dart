// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_free_lunch_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreeLunchResponse _$FreeLunchResponseFromJson(Map<String, dynamic> json) =>
    FreeLunchResponse(
      pageId: json['pageId'] as String?,
      meituanPvId: json['meituanPvId'] as String?,
      details: (json['details'] as List<dynamic>?)
          ?.map((e) => FreeLunchDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageInfo: json['pageInfo'] == null
          ? null
          : FreeLunchPageInfo.fromJson(
              json['pageInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FreeLunchResponseToJson(FreeLunchResponse instance) =>
    <String, dynamic>{
      'pageId': instance.pageId,
      'meituanPvId': instance.meituanPvId,
      'details': instance.details?.map((e) => e.toJson()).toList(),
      'pageInfo': instance.pageInfo?.toJson(),
    };

FreeLunchDetail _$FreeLunchDetailFromJson(Map<String, dynamic> json) =>
    FreeLunchDetail(
      type: json['type'] as int?,
      shopId: json['shopId'] as String?,
      name: json['name'] as String?,
      picture: json['picture'] as String?,
      deliveryTimeTip: json['deliveryTimeTip'] as String?,
      deliveryDistance: json['deliveryDistance'] as String?,
      activityList: (json['activityList'] as List<dynamic>?)
          ?.map((e) => FreeLunchActivity.fromJson(e as Map<String, dynamic>))
          .toList(),
      actionUrl: json['actionUrl'] == null
          ? null
          : FreeLunchActionUrl.fromJson(
              json['actionUrl'] as Map<String, dynamic>),
      goodsList: (json['goodsList'] as List<dynamic>?)
          ?.map((e) => FreeLunchGoods.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$FreeLunchDetailToJson(FreeLunchDetail instance) =>
    <String, dynamic>{
      'type': instance.type,
      'shopId': instance.shopId,
      'name': instance.name,
      'picture': instance.picture,
      'deliveryTimeTip': instance.deliveryTimeTip,
      'deliveryDistance': instance.deliveryDistance,
      'activityList': instance.activityList?.map((e) => e.toJson()).toList(),
      'actionUrl': instance.actionUrl?.toJson(),
      'goodsList': instance.goodsList?.map((e) => e.toJson()).toList(),
    };

FreeLunchActivity _$FreeLunchActivityFromJson(Map<String, dynamic> json) =>
    FreeLunchActivity(
      commission: _toDouble(json['commission']),
      commissionThresholdCent: _toDouble(json['commissionThresholdCent']),
      ratio: _toDouble(json['ratio']),
      maxCommission: _toDouble(json['maxCommission']),
      officialRate: _toDouble(json['officialRate']),
      officialCommission: _toDouble(json['officialCommission']),
      officialMaxCommission: _toDouble(json['officialMaxCommission']),
      inventory: _toInt(json['inventory']),
      totalInventory: _toInt(json['totalInventory']),
      activityId: json['activityId'] as String?,
      planActivityType: json['planActivityType'] as int?,
      redPacket: json['redPacket'],
    );

Map<String, dynamic> _$FreeLunchActivityToJson(FreeLunchActivity instance) =>
    <String, dynamic>{
      'commission': instance.commission,
      'commissionThresholdCent': instance.commissionThresholdCent,
      'ratio': instance.ratio,
      'maxCommission': instance.maxCommission,
      'officialRate': instance.officialRate,
      'officialCommission': instance.officialCommission,
      'officialMaxCommission': instance.officialMaxCommission,
      'inventory': instance.inventory,
      'totalInventory': instance.totalInventory,
      'activityId': instance.activityId,
      'planActivityType': instance.planActivityType,
      'redPacket': instance.redPacket,
    };

FreeLunchActionUrl _$FreeLunchActionUrlFromJson(Map<String, dynamic> json) =>
    FreeLunchActionUrl(
      dpUrl: json['dpUrl'] as String?,
      h5Url: json['h5Url'] as String?,
      wxPath: json['wxPath'] as String?,
      wxAppId: json['wxAppId'] as String?,
      wxAppOrgId: json['wxAppOrgId'] as String?,
    );

Map<String, dynamic> _$FreeLunchActionUrlToJson(FreeLunchActionUrl instance) =>
    <String, dynamic>{
      'dpUrl': instance.dpUrl,
      'h5Url': instance.h5Url,
      'wxPath': instance.wxPath,
      'wxAppId': instance.wxAppId,
      'wxAppOrgId': instance.wxAppOrgId,
    };

FreeLunchGoods _$FreeLunchGoodsFromJson(Map<String, dynamic> json) =>
    FreeLunchGoods(
      name: json['name'] as String?,
      picUrl: json['picUrl'] as String?,
      actPrice: json['actPrice'] as String?,
      oriPrice: json['oriPrice'] as String?,
    );

Map<String, dynamic> _$FreeLunchGoodsToJson(FreeLunchGoods instance) =>
    <String, dynamic>{
      'name': instance.name,
      'picUrl': instance.picUrl,
      'actPrice': instance.actPrice,
      'oriPrice': instance.oriPrice,
    };

FreeLunchPageInfo _$FreeLunchPageInfoFromJson(Map<String, dynamic> json) =>
    FreeLunchPageInfo(
      mtPageId: json['mtPageId'] as String?,
      elmPageId: json['elmPageId'] as String?,
    );

Map<String, dynamic> _$FreeLunchPageInfoToJson(FreeLunchPageInfo instance) =>
    <String, dynamic>{
      'mtPageId': instance.mtPageId,
      'elmPageId': instance.elmPageId,
    };
