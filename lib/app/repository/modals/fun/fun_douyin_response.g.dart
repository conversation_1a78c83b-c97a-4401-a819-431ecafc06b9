// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_douyin_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DouyinLifeResponse _$DouyinLifeResponseFromJson(Map<String, dynamic> json) =>
    DouyinLifeResponse(
      nextPage: json['nextPage'] as int?,
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => DouyinLifeItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DouyinLifeResponseToJson(DouyinLifeResponse instance) =>
    <String, dynamic>{
      'nextPage': instance.nextPage,
      'list': instance.list?.map((e) => e.toJson()).toList(),
    };

DouyinLifeItem _$DouyinLifeItemFromJson(Map<String, dynamic> json) =>
    DouyinLifeItem(
      goodsId: json['goodsId'] as String?,
      imgUrl: json['imgUrl'] as String?,
      distance: json['distance'] as String?,
      shopName: json['shopName'] as String?,
      couponNum: _toInt(json['couponNum']),
      title: json['title'] as String?,
      price: _toDouble(json['price']),
      originalPrice: _toDouble(json['originalPrice']),
      commission: _toDouble(json['commission']),
    );

Map<String, dynamic> _$DouyinLifeItemToJson(DouyinLifeItem instance) =>
    <String, dynamic>{
      'goodsId': instance.goodsId,
      'imgUrl': instance.imgUrl,
      'distance': instance.distance,
      'shopName': instance.shopName,
      'couponNum': instance.couponNum,
      'title': instance.title,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'commission': instance.commission,
    };
