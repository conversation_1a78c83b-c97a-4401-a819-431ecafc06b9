import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'fun_promotion.g.dart';

@JsonSerializable()
class FunPromotionCollection {
  int? type;
  String? typeName;

  @_TypeDataListConverter()
  List<TypeData>? typeData;

  FunPromotionCollection();

  factory FunPromotionCollection.fromJson(Map<String, dynamic> json) =>
      _$FunPromotionCollectionFromJson(json);

  get isTitle => null;
  Map<String, dynamic> toJson() => _$FunPromotionCollectionToJson(this);
}

@JsonSerializable(explicitToJson: true)
class TypeData {
  final String? url;
  final Jump? jump;

  TypeData({this.url, this.jump});

  factory TypeData.fromJson(Map<String, dynamic> json) =>
      _$TypeDataFromJson(json);

  Map<String, dynamic> toJson() => _$TypeDataToJson(this);
}

@JsonSerializable()
class Jump {
  final int? type;
  final bool? needLogin;
  final String? profile;
  final String? title;
  final String? desc;

  Jump({this.type, this.needLogin, this.profile, this.title, this.desc});

  factory Jump.fromJson(Map<String, dynamic> json) => _$JumpFromJson(json);

  Map<String, dynamic> toJson() => _$JumpToJson(this);
}

class _TypeDataListConverter
    implements JsonConverter<List<TypeData>?, List<dynamic>?> {
  const _TypeDataListConverter();

  @override
  List<TypeData>? fromJson(List<dynamic>? json) {
    if (json == null) return null;
    return json.map((e) {
      // e 是字符串，需要再 jsonDecode
      return TypeData.fromJson(jsonDecode(e as String));
    }).toList();
  }

  @override
  List<dynamic>? toJson(List<TypeData>? object) {
    if (object == null) return null;
    return object.map((e) => jsonEncode(e.toJson())).toList();
  }
}