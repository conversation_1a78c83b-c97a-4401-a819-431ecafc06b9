// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_tab_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunTabConfig _$FunTabConfigFromJson(Map<String, dynamic> json) => FunTabConfig(
      name: json['name'] as String?,
      code: json['code'] as String?,
      iconUrl: json['icon_url'] as String?,
      tabName: json['tabName'] as String?,
      title: json['title'] as String?,
      platform: json['platform'] as int?,
      listTopiId: json['listTopiId'] as int?,
      bizLine: json['bizLine'] as int?,
      val: json['val'] as int?,
      icon: json['icon'] as String?,
      category: (json['category'] as List<dynamic>?)
          ?.map((e) => FunTabConfig.fromJson(e as Map<String, dynamic>))
          .toList(),
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => FunTabConfig.fromJson(e as Map<String, dynamic>))
          .toList(),
      sort: (json['sort'] as List<dynamic>?)
          ?.map((e) => FunSortOption.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$FunTabConfigToJson(FunTabConfig instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      'icon_url': instance.iconUrl,
      'tabName': instance.tabName,
      'title': instance.title,
      'platform': instance.platform,
      'listTopiId': instance.listTopiId,
      'bizLine': instance.bizLine,
      'val': instance.val,
      'icon': instance.icon,
      'category': instance.category?.map((e) => e.toJson()).toList(),
      'children': instance.children?.map((e) => e.toJson()).toList(),
      'sort': instance.sort?.map((e) => e.toJson()).toList(),
    };

FunSortOption _$FunSortOptionFromJson(Map<String, dynamic> json) =>
    FunSortOption(
      sortName: json['sortName'] as String?,
      sortField: json['sortField'] as int?,
      ascDescOrder: json['ascDescOrder'] as int?,
      sortValue: json['sortValue'] as int?,
    );

Map<String, dynamic> _$FunSortOptionToJson(FunSortOption instance) =>
    <String, dynamic>{
      'sortName': instance.sortName,
      'sortField': instance.sortField,
      'ascDescOrder': instance.ascDescOrder,
      'sortValue': instance.sortValue,
    };
