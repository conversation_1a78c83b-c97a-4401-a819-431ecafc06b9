import 'package:json_annotation/json_annotation.dart';

part 'fun_meituan_response.g.dart';

@JsonSerializable(explicitToJson: true)
class MeituanCouponResponse {
  final int? total;
  final List<MeituanCouponItem>? list;
  final int? pageNum;
  final int? pageSize;
  final bool? hasNextPage;
  final int? nextPage;

  const MeituanCouponResponse({
    this.total,
    this.list,
    this.pageNum,
    this.pageSize,
    this.hasNextPage,
    this.nextPage,
  });

  bool get hasMore => (hasNextPage ?? false) || ((nextPage ?? 0) > (pageNum ?? 0));

  factory MeituanCouponResponse.fromJson(Map<String, dynamic> json) =>
      _$MeituanCouponResponseFromJson(json);

  Map<String, dynamic> toJson() => _$MeituanCouponResponseToJson(this);
}

@JsonSerializable()
class MeituanCouponItem {
  final int? availablePoiNum;
  final String? brandName;
  final String? brandLogoUrl;
  @JsonKey(fromJson: _toDouble)
  final double? commissionRate;
  @JsonKey(fromJson: _toDouble)
  final double? commission;
  final String? goodsName;
  final String? skuViewId;
  final String? specification;
  @JsonKey(fromJson: _toInt)
  final int? couponNum;
  @JsonKey(fromJson: _toInt)
  final int? validTime;
  final String? headUrl;
  final String? saleVolume;
  final String? startTime;
  final String? endTime;
  @JsonKey(fromJson: _toBool)
  final bool? saleStatus;
  @JsonKey(fromJson: _toDouble)
  final double? originalPrice;
  @JsonKey(fromJson: _toDouble)
  final double? sellPrice;
  final int? platform;
  final int? bizLine;
  final String? poiName;
  final String? poiLogoUrl;
  final String? deliveryDistance;
  final String? distributionCost;
  final String? deliveryDuration;
  final String? lastDeliveryFee;
  @JsonKey(fromJson: _toInt)
  final int? singleDayPurchaseLimit;
  final int? couponValidTimeType;
  final int? couponValidDay;
  final String? couponValidSTime;
  final String? couponValidETime;

  const MeituanCouponItem({
    this.availablePoiNum,
    this.brandName,
    this.brandLogoUrl,
    this.commissionRate,
    this.commission,
    this.goodsName,
    this.skuViewId,
    this.specification,
    this.couponNum,
    this.validTime,
    this.headUrl,
    this.saleVolume,
    this.startTime,
    this.endTime,
    this.saleStatus,
    this.originalPrice,
    this.sellPrice,
    this.platform,
    this.bizLine,
    this.poiName,
    this.poiLogoUrl,
    this.deliveryDistance,
    this.distributionCost,
    this.deliveryDuration,
    this.lastDeliveryFee,
    this.singleDayPurchaseLimit,
    this.couponValidTimeType,
    this.couponValidDay,
    this.couponValidSTime,
    this.couponValidETime,
  });

  factory MeituanCouponItem.fromJson(Map<String, dynamic> json) =>
      _$MeituanCouponItemFromJson(json);

  Map<String, dynamic> toJson() => _$MeituanCouponItemToJson(this);
}

double? _toDouble(dynamic value) {
  if (value == null) return null;
  if (value is num) return value.toDouble();
  if (value is String && value.isNotEmpty) {
    return double.tryParse(value);
  }
  return null;
}

int? _toInt(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is num) return value.toInt();
  if (value is String && value.isNotEmpty) {
    return int.tryParse(value);
  }
  return null;
}

bool? _toBool(dynamic value) {
  if (value == null) return null;
  if (value is bool) return value;
  if (value is num) return value != 0;
  if (value is String) {
    if (value == 'true' || value == '1') return true;
    if (value == 'false' || value == '0') return false;
  }
  return null;
}
