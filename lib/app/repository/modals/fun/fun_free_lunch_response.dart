import 'package:json_annotation/json_annotation.dart';

part 'fun_free_lunch_response.g.dart';

@JsonSerializable(explicitToJson: true)
class FreeLunchResponse {
  final String? pageId;
  final String? meituanPvId;
  final List<FreeLunchDetail>? details;
  final FreeLunchPageInfo? pageInfo;

  const FreeLunchResponse({
    this.pageId,
    this.meituanPvId,
    this.details,
    this.pageInfo,
  });

  factory FreeLunchResponse.fromJson(Map<String, dynamic> json) =>
      _$FreeLunchResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FreeLunchResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FreeLunchDetail {
  final int? type;
  final String? shopId;
  final String? name;
  final String? picture;
  final String? deliveryTimeTip;
  final String? deliveryDistance;
  final List<FreeLunchActivity>? activityList;
  final FreeLunchActionUrl? actionUrl;
  final List<FreeLunchGoods>? goodsList;

  const FreeLunchDetail({
    this.type,
    this.shopId,
    this.name,
    this.picture,
    this.deliveryTimeTip,
    this.deliveryDistance,
    this.activityList,
    this.actionUrl,
    this.goodsList,
  });

  factory FreeLunchDetail.fromJson(Map<String, dynamic> json) =>
      _$FreeLunchDetailFromJson(json);

  Map<String, dynamic> toJson() => _$FreeLunchDetailToJson(this);
}

@JsonSerializable()
class FreeLunchActivity {
  @JsonKey(fromJson: _toDouble)
  final double? commission;
  @JsonKey(fromJson: _toDouble)
  final double? commissionThresholdCent;
  @JsonKey(fromJson: _toDouble)
  final double? ratio;
  @JsonKey(fromJson: _toDouble)
  final double? maxCommission;
  @JsonKey(fromJson: _toDouble)
  final double? officialRate;
  @JsonKey(fromJson: _toDouble)
  final double? officialCommission;
  @JsonKey(fromJson: _toDouble)
  final double? officialMaxCommission;
  @JsonKey(fromJson: _toInt)
  final int? inventory;
  @JsonKey(fromJson: _toInt)
  final int? totalInventory;
  final String? activityId;
  final int? planActivityType;
  final dynamic redPacket;

  const FreeLunchActivity({
    this.commission,
    this.commissionThresholdCent,
    this.ratio,
    this.maxCommission,
    this.officialRate,
    this.officialCommission,
    this.officialMaxCommission,
    this.inventory,
    this.totalInventory,
    this.activityId,
    this.planActivityType,
    this.redPacket,
  });

  factory FreeLunchActivity.fromJson(Map<String, dynamic> json) =>
      _$FreeLunchActivityFromJson(json);

  Map<String, dynamic> toJson() => _$FreeLunchActivityToJson(this);
}

@JsonSerializable()
class FreeLunchActionUrl {
  final String? dpUrl;
  final String? h5Url;
  final String? wxPath;
  final String? wxAppId;
  final String? wxAppOrgId;

  const FreeLunchActionUrl({
    this.dpUrl,
    this.h5Url,
    this.wxPath,
    this.wxAppId,
    this.wxAppOrgId,
  });

  factory FreeLunchActionUrl.fromJson(Map<String, dynamic> json) =>
      _$FreeLunchActionUrlFromJson(json);

  Map<String, dynamic> toJson() => _$FreeLunchActionUrlToJson(this);
}

@JsonSerializable()
class FreeLunchGoods {
  final String? name;
  final String? picUrl;
  final String? actPrice;
  final String? oriPrice;

  const FreeLunchGoods({
    this.name,
    this.picUrl,
    this.actPrice,
    this.oriPrice,
  });

  factory FreeLunchGoods.fromJson(Map<String, dynamic> json) =>
      _$FreeLunchGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$FreeLunchGoodsToJson(this);
}

@JsonSerializable()
class FreeLunchPageInfo {
  final String? mtPageId;
  final String? elmPageId;

  const FreeLunchPageInfo({
    this.mtPageId,
    this.elmPageId,
  });

  factory FreeLunchPageInfo.fromJson(Map<String, dynamic> json) =>
      _$FreeLunchPageInfoFromJson(json);

  Map<String, dynamic> toJson() => _$FreeLunchPageInfoToJson(this);
}

double? _toDouble(dynamic value) {
  if (value == null) return null;
  if (value is num) return value.toDouble();
  if (value is String && value.isNotEmpty) {
    return double.tryParse(value);
  }
  return null;
}

int? _toInt(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is num) return value.toInt();
  if (value is String && value.isNotEmpty) {
    return int.tryParse(value);
  }
  return null;
}
