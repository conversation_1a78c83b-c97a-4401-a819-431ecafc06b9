import 'package:json_annotation/json_annotation.dart';

part 'fun_douyin_response.g.dart';

@JsonSerializable(explicitToJson: true)
class DouyinLifeResponse {
  final int? nextPage;
  final List<DouyinLifeItem>? list;

  const DouyinLifeResponse({
    this.nextPage,
    this.list,
  });

  bool get hasMore => nextPage != null;

  factory DouyinLifeResponse.fromJson(Map<String, dynamic> json) =>
      _$DouyinLifeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DouyinLifeResponseToJson(this);
}

@JsonSerializable()
class DouyinLifeItem {
  final String? goodsId;
  final String? imgUrl;
  final String? distance;
  final String? shopName;
  @JsonKey(fromJson: _toInt)
  final int? couponNum;
  final String? title;
  @JsonKey(fromJson: _toDouble)
  final double? price;
  @J<PERSON><PERSON>ey(fromJson: _toDouble)
  final double? originalPrice;
  @J<PERSON><PERSON>ey(fromJson: _toDouble)
  final double? commission;

  const DouyinLifeItem({
    this.goodsId,
    this.imgUrl,
    this.distance,
    this.shopName,
    this.couponNum,
    this.title,
    this.price,
    this.originalPrice,
    this.commission,
  });

  factory DouyinLifeItem.fromJson(Map<String, dynamic> json) =>
      _$DouyinLifeItemFromJson(json);

  Map<String, dynamic> toJson() => _$DouyinLifeItemToJson(this);
}

double? _toDouble(dynamic value) {
  if (value == null) return null;
  if (value is num) return value.toDouble();
  if (value is String && value.isNotEmpty) {
    return double.tryParse(value);
  }
  return null;
}

int? _toInt(dynamic value) {
  if (value == null) return null;
  if (value is int) return value;
  if (value is num) return value.toInt();
  if (value is String && value.isNotEmpty) {
    return int.tryParse(value);
  }
  return null;
}
