// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_meituan_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MeituanCouponResponse _$MeituanCouponResponseFromJson(
        Map<String, dynamic> json) =>
    MeituanCouponResponse(
      total: json['total'] as int?,
      list: (json['list'] as List<dynamic>?)
          ?.map((e) => MeituanCouponItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      pageNum: json['pageNum'] as int?,
      pageSize: json['pageSize'] as int?,
      hasNextPage: json['hasNextPage'] as bool?,
      nextPage: json['nextPage'] as int?,
    );

Map<String, dynamic> _$MeituanCouponResponseToJson(
        MeituanCouponResponse instance) =>
    <String, dynamic>{
      'total': instance.total,
      'list': instance.list?.map((e) => e.toJson()).toList(),
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'hasNextPage': instance.hasNextPage,
      'nextPage': instance.nextPage,
    };

MeituanCouponItem _$MeituanCouponItemFromJson(Map<String, dynamic> json) =>
    MeituanCouponItem(
      availablePoiNum: json['availablePoiNum'] as int?,
      brandName: json['brandName'] as String?,
      brandLogoUrl: json['brandLogoUrl'] as String?,
      commissionRate: _toDouble(json['commissionRate']),
      commission: _toDouble(json['commission']),
      goodsName: json['goodsName'] as String?,
      skuViewId: json['skuViewId'] as String?,
      specification: json['specification'] as String?,
      couponNum: _toInt(json['couponNum']),
      validTime: _toInt(json['validTime']),
      headUrl: json['headUrl'] as String?,
      saleVolume: json['saleVolume'] as String?,
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      saleStatus: _toBool(json['saleStatus']),
      originalPrice: _toDouble(json['originalPrice']),
      sellPrice: _toDouble(json['sellPrice']),
      platform: json['platform'] as int?,
      bizLine: json['bizLine'] as int?,
      poiName: json['poiName'] as String?,
      poiLogoUrl: json['poiLogoUrl'] as String?,
      deliveryDistance: json['deliveryDistance'] as String?,
      distributionCost: json['distributionCost'] as String?,
      deliveryDuration: json['deliveryDuration'] as String?,
      lastDeliveryFee: json['lastDeliveryFee'] as String?,
      singleDayPurchaseLimit: _toInt(json['singleDayPurchaseLimit']),
      couponValidTimeType: json['couponValidTimeType'] as int?,
      couponValidDay: json['couponValidDay'] as int?,
      couponValidSTime: json['couponValidSTime'] as String?,
      couponValidETime: json['couponValidETime'] as String?,
    );

Map<String, dynamic> _$MeituanCouponItemToJson(MeituanCouponItem instance) =>
    <String, dynamic>{
      'availablePoiNum': instance.availablePoiNum,
      'brandName': instance.brandName,
      'brandLogoUrl': instance.brandLogoUrl,
      'commissionRate': instance.commissionRate,
      'commission': instance.commission,
      'goodsName': instance.goodsName,
      'skuViewId': instance.skuViewId,
      'specification': instance.specification,
      'couponNum': instance.couponNum,
      'validTime': instance.validTime,
      'headUrl': instance.headUrl,
      'saleVolume': instance.saleVolume,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'saleStatus': instance.saleStatus,
      'originalPrice': instance.originalPrice,
      'sellPrice': instance.sellPrice,
      'platform': instance.platform,
      'bizLine': instance.bizLine,
      'poiName': instance.poiName,
      'poiLogoUrl': instance.poiLogoUrl,
      'deliveryDistance': instance.deliveryDistance,
      'distributionCost': instance.distributionCost,
      'deliveryDuration': instance.deliveryDuration,
      'lastDeliveryFee': instance.lastDeliveryFee,
      'singleDayPurchaseLimit': instance.singleDayPurchaseLimit,
      'couponValidTimeType': instance.couponValidTimeType,
      'couponValidDay': instance.couponValidDay,
      'couponValidSTime': instance.couponValidSTime,
      'couponValidETime': instance.couponValidETime,
    };
