import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/reminder/reminder.dart';

part 'reminder_list.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: reminder_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 14:16
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 14:16
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ReminderList {
  bool? hasNextPage;
  List<Reminder>? list;
  int? total;

  ReminderList();

  factory ReminderList.fromJson(Map<String, dynamic> json) =>
      _$ReminderListFromJson(json);

  Map<String, dynamic> toJson() => _$ReminderListToJson(this);
}
