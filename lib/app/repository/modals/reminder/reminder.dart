import 'package:json_annotation/json_annotation.dart';

part 'reminder.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: reminder
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 14:16
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 14:16
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Reminder {
  String? completeGoodsId;
  String? couponInfo;
  num? couponPrice;
  String? cover;
  String? createTime;
  String? goodsId;
  String? goodsName;
  int? goodsType;
  int? id;
  num? originalPrice;
  num? receivedPrice;
  int? sendNotice;
  String? updateTime;
  num? userCommission;
  int? userId;

  Reminder();

  factory Reminder.fromJson(Map<String, dynamic> json) =>
      _$ReminderFromJson(json);

  Map<String, dynamic> toJson() => _$ReminderTo<PERSON>son(this);
}
