import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/reminder/reminder_list.dart';

part 'reminder_info.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: reminder_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 14:52
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 14:52
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ReminderInfo {
  int? goodsNum;
  int? goodsOrderRemind;
  ReminderList? pageInfo;

  ReminderInfo();

  factory ReminderInfo.fromJson(Map<String, dynamic> json) =>
      _$ReminderInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ReminderInfoToJson(this);
}
