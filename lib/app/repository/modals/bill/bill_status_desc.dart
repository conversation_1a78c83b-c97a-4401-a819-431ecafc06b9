import 'package:json_annotation/json_annotation.dart';

part 'bill_status_desc.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.bill
/// @ClassName: bill_status_desc
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/12 11:28
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/12 11:28
/// @UpdateRemark: 更新说明
@JsonSerializable()
class BillStatusDesc {
  List<String>? statusTitle;
  int? statusSchedule;
  num? remainingRatio;
  String? desc;
  String? subscript;
  String? subscriptDesc;
  String? subscriptTitle;

  BillStatusDesc();

  factory BillStatusDesc.fromJson(Map<String, dynamic> json) =>
      _$BillStatusDescFromJson(json);

  Map<String, dynamic> toJson() => _$BillStatusDescToJson(this);
}
