import 'package:json_annotation/json_annotation.dart';

import 'withdrawal_amount_item.dart';

part 'withdrawal_amount.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.bill
/// @ClassName: withdrawal_amount
/// @Description: 系统预设提现金额信息
/// @Author: frankylee
/// @CreateDate: 2024/9/12 17:50
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/12 17:50
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalAmount {
 int? userWithdrawCount;
 String? vipRights;
 List<WithdrawalAmountItem>? data;

  WithdrawalAmount();

  factory WithdrawalAmount.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalAmountFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalAmountToJson(this);
}

