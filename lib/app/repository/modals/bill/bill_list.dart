import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/bill/bill.dart';

part 'bill_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: bill_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/12 14:52
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/12 14:52
/// @UpdateRemark: 更新说明
@JsonSerializable()
class BillList {
  bool? hasNextPage;
  List<Bill>? list;
  int? total;

  BillList();

  factory BillList.fromJson(Map<String, dynamic> json) =>
      _$BillListFromJson(json);

  Map<String, dynamic> toJson() => _$BillListToJson(this);
}
