import 'package:json_annotation/json_annotation.dart';

part 'withdrawn_bill.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.bill
/// @ClassName: withdrawn_bill
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/11 18:14
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/11 18:14
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawnBill {
  num? amount;
  int? withdrawType;
  String? createTime;

  WithdrawnBill();

  factory WithdrawnBill.fromJson(Map<String, dynamic> json) =>
      _$WithdrawnBillFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawnBillToJson(this);
}
