// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bill_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BillList _$BillListFrom<PERSON>son(Map<String, dynamic> json) => BillList()
  ..hasNextPage = json['hasNextPage'] as bool?
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) => Bill.fromJson(e as Map<String, dynamic>))
      .toList()
  ..total = json['total'] as int?;

Map<String, dynamic> _$BillListToJson(BillList instance) => <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
