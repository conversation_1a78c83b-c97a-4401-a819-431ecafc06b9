import 'package:json_annotation/json_annotation.dart';

import 'bill_status_desc.dart';

part 'bill.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: bill
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/12 14:50
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/12 14:50
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Bill {
  int? billType;
  String? goodsImgUrl;
  String? billTitle;
  String? billTime;
  num? orderAmount;
  num? redPacketAmount;
  BillStatusDesc? orderStatusDescVO;

  Bill();

  factory Bill.fromJson(Map<String, dynamic> json) => _$BillFromJson(json);

  Map<String, dynamic> toJson() => _$BillToJson(this);
}
