import 'package:json_annotation/json_annotation.dart';

part 'withdrawal_amount_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.bill
/// @ClassName: withdrawal_amount_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/12 17:50
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/12 17:50
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalAmountItem {
  String? tipsDesc;
  String? money;
  String? withdrawType;
  String? bgImg;
  String? desc;

  WithdrawalAmountItem();

  factory WithdrawalAmountItem.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalAmountItemFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalAmountItemToJson(this);
}

