import 'package:json_annotation/json_annotation.dart';

part 'goods_tb_pkg.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: goods_tb_pkg
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/8/26 18:07
/// @UpdateUser: frankylee
/// @UpdateData: 2024/8/26 18:07
/// @UpdateRemark: 更新说明
@JsonSerializable()
class GoodsTbPkg {
  num? twoHourSale;
  String? title;
  String? tbItemId;
  String? purchaseDescription;
  String? cover;
  String? goodsImg;
  num? priceAfterReceive;
  num? commission;
  num? noVipCommission;
  String? couponUrl;
  String? goodsId;
  String? bizSceneId;
  String? goodsUrl;

  GoodsTbPkg();

  factory GoodsTbPkg.fromJson(Map<String, dynamic> json) => _$GoodsTbPkgFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsTbPkgToJson(this);
}

