// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jd_use_label.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JdUseLabel _$JdUseLabelFromJson(Map<String, dynamic> json) => JdUseLabel()
  ..promotionLabel = json['promotionLabel'] as String?
  ..labelName = json['labelName'] as String?;

Map<String, dynamic> _$JdUseLabelToJson(JdUseLabel instance) =>
    <String, dynamic>{
      'promotionLabel': instance.promotionLabel,
      'labelName': instance.labelName,
    };
