// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'temp_goods.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TempGoods _$TempGoodsFromJson(Map<String, dynamic> json) => TempGoods()
  ..logoName = json['logoName'] as String?
  ..discountRate = json['discountRate'] as String?
  ..giftCount0 = json['giftCount0'] as String?
  ..giftCount1 = json['giftCount1'] as String?
  ..priceAfterCoupon = json['priceAfterCoupon'] as num?
  ..purchasePrice = json['purchasePrice'] as num?
  ..totalCommission = json['totalCommission'] as num?
  ..noVipGiftCount1 = json['noVipGiftCount1'] as num?
  ..noVipGiftCount0 = json['noVipGiftCount0'] as num?
  ..points = json['points'] as num?
  ..cover = json['cover'] as String?
  ..couponAmount = json['couponAmount'] as num?
  ..priceUseGift = json['priceUseGift'] as num?
  ..wlPrice = json['wlPrice'] as num?
  ..sku = json['sku'] as String?
  ..commentsNum = json['commentsNum'] as num?
  ..skuId = json['skuId'] as String?
  ..noVipDiscount0 = json['noVipDiscount0'] as num?
  ..noVipDiscount1 = json['noVipDiscount1'] as num?
  ..couponTips = json['couponTips'] as String?
  ..volumeNum = json['volumeNum'] as num?
  ..couponUrl = json['couponUrl'] as String?
  ..pict_url = json['pict_url'] as String?
  ..useGiftRate = json['useGiftRate'] as String?
  ..volume = json['volume'] as String?
  ..aboutSavings = json['aboutSavings'] as num?
  ..name = json['name'] as String?
  ..commissionRate = json['commissionRate'] as num?
  ..lowestPrice = json['lowestPrice'] as num?
  ..noVipCommission = json['noVipCommission'] as num?
  ..goodsId = json['goodsId'] as String?
  ..couponLimit = json['couponLimit'] as num?
  ..discount0 = json['discount0'] as String?
  ..noVipPriceAfterReceive = json['noVipPriceAfterReceive'] as num?
  ..shopName = json['shopName'] as String?
  ..title = json['title'] as String?
  ..noVipAboutSavings = json['noVipAboutSavings'] as num?
  ..hasCoupon = json['hasCoupon'] as int?
  ..discount1 = json['discount1'] as String?
  ..commission = json['commission'] as num?
  ..priceAfterReceive = json['priceAfterReceive'] as num?;

Map<String, dynamic> _$TempGoodsToJson(TempGoods instance) => <String, dynamic>{
      'logoName': instance.logoName,
      'discountRate': instance.discountRate,
      'giftCount0': instance.giftCount0,
      'giftCount1': instance.giftCount1,
      'priceAfterCoupon': instance.priceAfterCoupon,
      'purchasePrice': instance.purchasePrice,
      'totalCommission': instance.totalCommission,
      'noVipGiftCount1': instance.noVipGiftCount1,
      'noVipGiftCount0': instance.noVipGiftCount0,
      'points': instance.points,
      'cover': instance.cover,
      'couponAmount': instance.couponAmount,
      'priceUseGift': instance.priceUseGift,
      'wlPrice': instance.wlPrice,
      'sku': instance.sku,
      'commentsNum': instance.commentsNum,
      'skuId': instance.skuId,
      'noVipDiscount0': instance.noVipDiscount0,
      'noVipDiscount1': instance.noVipDiscount1,
      'couponTips': instance.couponTips,
      'volumeNum': instance.volumeNum,
      'couponUrl': instance.couponUrl,
      'pict_url': instance.pict_url,
      'useGiftRate': instance.useGiftRate,
      'volume': instance.volume,
      'aboutSavings': instance.aboutSavings,
      'name': instance.name,
      'commissionRate': instance.commissionRate,
      'lowestPrice': instance.lowestPrice,
      'noVipCommission': instance.noVipCommission,
      'goodsId': instance.goodsId,
      'couponLimit': instance.couponLimit,
      'discount0': instance.discount0,
      'noVipPriceAfterReceive': instance.noVipPriceAfterReceive,
      'shopName': instance.shopName,
      'title': instance.title,
      'noVipAboutSavings': instance.noVipAboutSavings,
      'hasCoupon': instance.hasCoupon,
      'discount1': instance.discount1,
      'commission': instance.commission,
      'priceAfterReceive': instance.priceAfterReceive,
    };
