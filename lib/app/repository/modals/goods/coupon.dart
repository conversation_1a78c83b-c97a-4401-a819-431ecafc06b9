import 'package:json_annotation/json_annotation.dart';

part 'coupon.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: coupon
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/28 11:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/28 11:46
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Coupon {
  num? couponAmount;
  String? couponAmountInfo;
  String? couponInfo;
  num? couponLimit;
  int? couponType;
  String? couponUrl;
  int? platformType;
  int? remainNum;
  int? totalNum;

  Coupon();

  factory Coupon.fromJson(Map<String, dynamic> json) => _$CouponFromJson(json);

  Map<String, dynamic> toJson() => _$CouponToJson(this);
}
