import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/goods/coupon.dart';

part 'goods.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/28 11:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/28 11:45
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Goods {
  String? comments;
  int? commentsNum;
  num? commissionRate;
  String? completeGoodsId;
  num? couponPrice;
  String? cover;
  String? createTime;
  num? discountRate;
  String? extendInfo;
  List<Coupon>? goodsCoupon;
  String? goodsId;
  String? goodsName;
  int? goodsType;
  String? goodsUrl;
  int? id;
  num? originalPrice;
  num? receivedPrice;
  int? saleVolume;
  String? shopName;
  List<String>? smallPicList;
  int? subGoodsType;
  num? totalCommission;
  String? updateTime;
  num? userCommission;

  Goods();

  factory Goods.fromJson(Map<String, dynamic> json) => _$GoodsFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsToJson(this);
}
