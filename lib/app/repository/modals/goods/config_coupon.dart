import 'package:json_annotation/json_annotation.dart';

import 'config_coupon_info.dart';

part 'config_coupon.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: config_coupon
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/19 12:08
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/19 12:08
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ConfigCoupon {
  String? couponName;
  ConfigCouponInfo? couponInfo;

  ConfigCoupon();

  factory ConfigCoupon.fromJson(Map<String, dynamic> json) => _$ConfigCouponFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigCouponToJson(this);
}

