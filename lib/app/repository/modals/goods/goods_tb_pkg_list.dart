import 'package:json_annotation/json_annotation.dart';

import 'goods_tb_pkg.dart';

part 'goods_tb_pkg_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: goods_tb_pkg_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/8/26 18:17
/// @UpdateUser: frankylee
/// @UpdateData: 2024/8/26 18:17
/// @UpdateRemark: 更新说明
@JsonSerializable()
class GoodsTbPkgList {
  int? total;
  List<GoodsTbPkg>? data;
  GoodsTbPkgList();

  factory GoodsTbPkgList.fromJson(Map<String, dynamic> json) =>
      _$GoodsTbPkgListFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsTbPkgListToJson(this);
}

