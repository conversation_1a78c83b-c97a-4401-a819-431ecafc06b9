import 'package:json_annotation/json_annotation.dart';

part 'jd_use_label.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: jd_use_label
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/19 12:01
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/19 12:01
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdUseLabel {
  String? promotionLabel;
  String? labelName;

  JdUseLabel();

  factory JdUseLabel.fromJson(Map<String, dynamic> json) => _$JdUseLabelFromJson(json);

  Map<String, dynamic> toJson() => _$JdUseLabelToJson(this);
}

