import 'package:json_annotation/json_annotation.dart';

part 'goods_pkg.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: goods_pkg
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 10:34
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 10:34
/// @UpdateRemark: 更新说明
@JsonSerializable()
class GoodsPkg {
  String? createTime;
  String? displayArea;
  int? goodsType;
  int? id;
  String? pkgName;
  int? sort;
  int? state;
  String? updateTime;

  GoodsPkg();

  factory GoodsPkg.fromJson(Map<String, dynamic> json) =>
      _$GoodsPkgFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsPkgToJson(this);
}
