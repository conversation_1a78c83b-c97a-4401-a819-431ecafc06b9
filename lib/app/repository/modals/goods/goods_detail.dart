import 'package:json_annotation/json_annotation.dart';

import 'config_coupon.dart';
import 'jd_use_coupon.dart';
import 'jd_use_label.dart';

part 'goods_detail.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: goods_detail
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/18 15:07
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 15:07
/// @UpdateRemark: 更新说明
@JsonSerializable()
class GoodsDetail {
  String? goodsUniqueId;
  String? goodsId;
  String? goodsName;
  String? goodsImg;
  List<String>? smallPicList;
  String? goodsUrl;
  String? couponUrl;
  num? originalPrice;
  num? price;
  num? priceAfterCoupon;
  num? priceAfterReceive;
  num? subtractRate;
  String? saleVolume;
  num? redPacketAmount;
  String? couponInfo;
  num? couponAmount;
  String? couponAmountInfo;
  num? vipCommission;
  num? noVipCommission;
  String? shopName;
  String? platformTag;
  int? platformType;
  List<ConfigCoupon>? appCouponConfigList;
  String? bizSceneId;
  num? subtractAmount;
  List<JdUseCoupon>? jdCanUseCoupons;
  List<JdUseLabel>? jdCanUseLabelInfoList;

  GoodsDetail();

  factory GoodsDetail.fromJson(Map<String, dynamic> json) => _$GoodsDetailFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsDetailToJson(this);
}

