import 'package:json_annotation/json_annotation.dart';

import 'temp_goods.dart';

part 'temp_goods_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: temp_goods_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/10 14:47
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/10 14:47
/// @UpdateRemark: 更新说明
@JsonSerializable()
class TempGoodsList {
  int? total;
  List<TempGoods>? data;
  TempGoodsList();

  factory TempGoodsList.fromJson(Map<String, dynamic> json) =>
      _$TempGoodsListFromJson(json);

  Map<String, dynamic> toJson() => _$TempGoodsListToJson(this);
}