import 'package:json_annotation/json_annotation.dart';

part 'jd_use_coupon.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: jd_use_coupon
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/19 12:01
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/19 12:01
/// @UpdateRemark: 更新说明
@JsonSerializable()
class JdUseCoupon {
  int? bindType;
  num? discount;
  String? link;
  int? platformType;
  num? quota;
  int? couponStyle;
  String? couponInfo;
  String? discountInfo;

  JdUseCoupon();

  factory JdUseCoupon.fromJson(Map<String, dynamic> json) => _$JdUseCouponFromJson(json);

  Map<String, dynamic> toJson() => _$JdUseCouponToJson(this);
}

