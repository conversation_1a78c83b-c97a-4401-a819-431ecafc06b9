// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jd_use_coupon.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JdUseCoupon _$JdUseCouponFromJson(Map<String, dynamic> json) => JdUseCoupon()
  ..bindType = json['bindType'] as int?
  ..discount = json['discount'] as num?
  ..link = json['link'] as String?
  ..platformType = json['platformType'] as int?
  ..quota = json['quota'] as num?
  ..couponStyle = json['couponStyle'] as int?
  ..couponInfo = json['couponInfo'] as String?
  ..discountInfo = json['discountInfo'] as String?;

Map<String, dynamic> _$JdUseCouponToJson(JdUseCoupon instance) =>
    <String, dynamic>{
      'bindType': instance.bindType,
      'discount': instance.discount,
      'link': instance.link,
      'platformType': instance.platformType,
      'quota': instance.quota,
      'couponStyle': instance.couponStyle,
      'couponInfo': instance.couponInfo,
      'discountInfo': instance.discountInfo,
    };
