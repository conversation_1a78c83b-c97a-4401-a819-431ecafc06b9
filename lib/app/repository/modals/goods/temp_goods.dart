import 'package:json_annotation/json_annotation.dart';

part 'temp_goods.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: temp_goods
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/10 14:49
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/10 14:49
/// @UpdateRemark: 更新说明
@JsonSerializable()
class TempGoods {
  String? logoName;
  String? discountRate;
  String? giftCount0;
  String? giftCount1;
  num? priceAfterCoupon;
  num? purchasePrice;
  num? totalCommission;
  num? noVipGiftCount1;
  num? noVipGiftCount0;
  num? points;
  String? cover;
  num? couponAmount;
  num? priceUseGift;
  num? wlPrice;
  String? sku;
  num? commentsNum;
  String? skuId;
  num? noVipDiscount0;
  num? noVipDiscount1;
  String? couponTips;
  num? volumeNum;
  String? couponUrl;
  String? pict_url;
  String? useGiftRate;
  String? volume;
  num? aboutSavings;
  String? name;
  num? commissionRate;
  num? lowestPrice;
  num? noVipCommission;
  String? goodsId;
  num? couponLimit;
  String? discount0;
  num? noVipPriceAfterReceive;
  String? shopName;
  String? title;
  num? noVipAboutSavings;
  int? hasCoupon;
  String? discount1;
  num? commission;
  num? priceAfterReceive;

  TempGoods();

  factory TempGoods.fromJson(Map<String, dynamic> json) => _$TempGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$TempGoodsToJson(this);
}

