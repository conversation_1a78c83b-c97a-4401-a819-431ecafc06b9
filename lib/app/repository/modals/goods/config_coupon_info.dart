import 'package:json_annotation/json_annotation.dart';

part 'config_coupon_info.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.goods
/// @ClassName: config_coupon_info
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/19 12:09
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/19 12:09
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ConfigCouponInfo {
  num? amount;

  ConfigCouponInfo();

  factory ConfigCouponInfo.fromJson(Map<String, dynamic> json) => _$ConfigCouponInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigCouponInfoToJson(this);
}

