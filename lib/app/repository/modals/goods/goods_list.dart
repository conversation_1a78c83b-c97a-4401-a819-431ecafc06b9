import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods.dart';

part 'goods_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: goods_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/28 11:51
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/28 11:51
/// @UpdateRemark: 更新说明
@JsonSerializable()
class GoodsList {
  bool? hasNextPage;
  List<Goods>? list;
  int? total;

  GoodsList();

  factory GoodsList.fromJson(Map<String, dynamic> json) =>
      _$GoodsListFromJson(json);

  Map<String, dynamic> toJson() => _$GoodsListToJson(this);
}
