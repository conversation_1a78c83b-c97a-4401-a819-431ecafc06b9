import 'package:json_annotation/json_annotation.dart';

part 'favorite_goods.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.archive
/// @ClassName: favorite_goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/26 11:21
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/26 11:21
/// @UpdateRemark: 更新说明
@JsonSerializable()
class FavoriteGoods {
  int? id;
  int? goodsType;
  String? goodsId;
  String? intactGoodsId;
  String? goodsName;
  String? goodsImg;
  dynamic goodsInfo;
  String? createTime;

  FavoriteGoods();

  factory FavoriteGoods.fromJson(Map<String, dynamic> json) =>
      _$FavoriteGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$FavoriteGoodsToJson(this);
}
