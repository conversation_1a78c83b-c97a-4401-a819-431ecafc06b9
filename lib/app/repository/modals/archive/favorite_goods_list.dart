import 'package:json_annotation/json_annotation.dart';

import 'favorite_goods.dart';

part 'favorite_goods_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.archive
/// @ClassName: favorite_goods_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/26 11:21
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/26 11:21
/// @UpdateRemark: 更新说明
@JsonSerializable()
class FavoriteGoodsList {
  List<FavoriteGoods>? list;
  int? total;
  bool? hasNextPage;

  FavoriteGoodsList();

  factory FavoriteGoodsList.fromJson(Map<String, dynamic> json) =>
      _$FavoriteGoodsListFromJson(json);

  Map<String, dynamic> toJson() => _$FavoriteGoodsListToJson(this);
}
