import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';

part 'order_detail_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.order
/// @ClassName: order_detail_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/11 15:08
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/11 15:08
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderDetailList {
  List<OrderDetail>? data;

  OrderDetailList();

  factory OrderDetailList.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailListFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDetailListToJson(this);
}

