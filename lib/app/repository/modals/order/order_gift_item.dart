import 'package:json_annotation/json_annotation.dart';

part 'order_gift_item.g.dart';
/// Copyright (C), 2021-2025, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.order
/// @ClassName: order_gift_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2025/4/10 15:17
/// @UpdateUser: frankylee
/// @UpdateData: 2025/4/10 15:17
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderGiftItem {
  String? activationTime;
  String? createTime;
  int? duration;
  String? endTime;
  int? forwardUserId;
  int? fromOrderId;
  int? giftId;
  String? invalidTime;
  bool? isUse;
  int? money;
  int? moneyLimit;
  int? orderId;
  String? orderNo;
  String? orderStatusDesc;
  bool? overDue;
  bool? qlFissionSubsidyRedPacket;
  int? shopId;
  String? skuId;
  int? status;
  String? typeDesc;
  int? typeId;
  String? updateTime;
  String? useTime;
  int? useType;
  int? userId;

  OrderGiftItem();

  factory OrderGiftItem.fromJson(Map<String, dynamic> json) =>
      _$OrderGiftItemFromJson(json);

  Map<String, dynamic> toJson() => _$OrderGiftItemToJson(this);
}

