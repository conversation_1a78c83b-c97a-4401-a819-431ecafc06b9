// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderDetail _$OrderDetailFromJson(Map<String, dynamic> json) => OrderDetail()
  ..orderId = json['orderId'] as int?
  ..amount = json['amount'] as num?
  ..orderNo = json['orderNo'] as String?
  ..createTime = json['createTime'] as String?
  ..realCommission = json['realCommission'] as num?
  ..status = json['status'] as int?
  ..shopId = json['shopId'] as String?
  ..shopName = json['shopName'] as String?
  ..shopImg = json['shopImg'] as String?
  ..refundTag = json['refundTag'] as int?
  ..logoName = json['logoName'] as String?
  ..redPacketAmount = json['redPacketAmount'] as num?
  ..commission = json['commission'] as num?
  ..finishDate = json['finishDate'] as String?
  ..orderType = json['orderType'] as int?
  ..refundDesc = json['refundDesc'] as String?
  ..plusRemind = json['plusRemind'] as bool?
  ..statusDesc = json['statusDesc'] as String?
  ..point = json['point'] as int?
  ..settlementTime = json['settlementTime'] as String?
  ..refundTime = json['refundTime'] as String?
  ..canUseRedPacket = json['canUseRedPacket'] as bool?;

Map<String, dynamic> _$OrderDetailToJson(OrderDetail instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'amount': instance.amount,
      'orderNo': instance.orderNo,
      'createTime': instance.createTime,
      'realCommission': instance.realCommission,
      'status': instance.status,
      'shopId': instance.shopId,
      'shopName': instance.shopName,
      'shopImg': instance.shopImg,
      'refundTag': instance.refundTag,
      'logoName': instance.logoName,
      'redPacketAmount': instance.redPacketAmount,
      'commission': instance.commission,
      'finishDate': instance.finishDate,
      'orderType': instance.orderType,
      'refundDesc': instance.refundDesc,
      'plusRemind': instance.plusRemind,
      'statusDesc': instance.statusDesc,
      'point': instance.point,
      'settlementTime': instance.settlementTime,
      'refundTime': instance.refundTime,
      'canUseRedPacket': instance.canUseRedPacket,
    };
