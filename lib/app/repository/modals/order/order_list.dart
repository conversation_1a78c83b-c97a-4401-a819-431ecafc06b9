import 'package:json_annotation/json_annotation.dart';

import 'order_detail.dart';

part 'order_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 11:30
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 11:30
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderList {
  bool? hasNextPage;
  List<OrderDetail>? list;
  int? total;

  OrderList();

  factory OrderList.fromJson(Map<String, dynamic> json) =>
      _$OrderListFromJson(json);

  Map<String, dynamic> toJson() => _$OrderListToJson(this);
}
