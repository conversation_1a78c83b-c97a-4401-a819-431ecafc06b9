// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_gift_exchange_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderGiftExchangeItem _$OrderGiftExchangeItemFromJson(
        Map<String, dynamic> json) =>
    OrderGiftExchangeItem()
      ..amount = json['amount'] as num?
      ..exchangeNum = json['exchangeNum'] as int?
      ..goodsShowConfig = json['goodsShowConfig'] as String?
      ..goodsType = json['goodsType'] as int?
      ..id = json['id'] as int?
      ..integral = json['integral'] as int?
      ..integralStr = json['integralStr'] as String?
      ..logoName = json['logoName'] as String?
      ..name = json['name'] as String?
      ..otherCode = json['otherCode'] as String?
      ..pic = json['pic'] as String?
      ..referencePrice = json['referencePrice'] as num?
      ..skuId = json['skuId'] as String?
      ..stock = json['stock'] as int?
      ..titleName = json['titleName'] as String?;

Map<String, dynamic> _$OrderGiftExchangeItemToJson(
        OrderGiftExchangeItem instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'exchangeNum': instance.exchangeNum,
      'goodsShowConfig': instance.goodsShowConfig,
      'goodsType': instance.goodsType,
      'id': instance.id,
      'integral': instance.integral,
      'integralStr': instance.integralStr,
      'logoName': instance.logoName,
      'name': instance.name,
      'otherCode': instance.otherCode,
      'pic': instance.pic,
      'referencePrice': instance.referencePrice,
      'skuId': instance.skuId,
      'stock': instance.stock,
      'titleName': instance.titleName,
    };
