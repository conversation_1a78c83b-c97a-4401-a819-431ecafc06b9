import 'package:json_annotation/json_annotation.dart';

part 'order.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: order
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 11:27
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 11:27
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Order {
  String? orderNo;
  String? goodsImg;
  String? goodsTitle;
  num? userCommission;
  num? redPacketAmount;
  int? orderType;
  String? goodsId;

  Order();

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

  Map<String, dynamic> toJson() => _$OrderToJson(this);
}
