import 'package:json_annotation/json_annotation.dart';

import 'order_gift_exchange_item.dart';
import 'order_gift_item.dart';

part 'order_gift.g.dart';
/// Copyright (C), 2021-2025, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.order
/// @ClassName: order_gift
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2025/4/10 15:14
/// @UpdateUser: frankylee
/// @UpdateData: 2025/4/10 15:14
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderGift {
  List<OrderGiftItem>? giftList;
  List<OrderGiftExchangeItem>? integralGoodsList;
  int? type;

  OrderGift();

  factory OrderGift.fromJson(Map<String, dynamic> json) =>
      _$OrderGiftFromJson(json);

  Map<String, dynamic> toJson() => _$OrderGiftToJson(this);
}