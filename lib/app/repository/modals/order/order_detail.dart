import 'package:json_annotation/json_annotation.dart';

part 'order_detail.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_detail
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 11:31
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 11:31
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderDetail {
  int? orderId;
  num? amount;
  String? orderNo;
  String? createTime;
  num? realCommission;
  int? status;
  String? shopId;
  String? shopName;
  String? shopImg;
  int? refundTag;
  String? logoName;
  num? redPacketAmount;
  num? commission;
  String? finishDate;
  int? orderType;
  String? refundDesc;
  bool? plusRemind;
  String? statusDesc;
  int? point;
  String? settlementTime;
  String? refundTime;
  bool? canUseRedPacket;

  OrderDetail();

  factory OrderDetail.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDetailToJson(this);
}
