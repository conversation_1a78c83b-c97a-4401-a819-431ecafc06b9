import 'package:json_annotation/json_annotation.dart';

part 'order_gift_exchange_item.g.dart';
/// Copyright (C), 2021-2025, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.order
/// @ClassName: order_gift_exchange_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2025/4/10 15:20
/// @UpdateUser: frankylee
/// @UpdateData: 2025/4/10 15:20
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderGiftExchangeItem {
  num? amount;
  int? exchangeNum;
  String? goodsShowConfig;
  int? goodsType;
  int? id;
  int? integral;
  String? integralStr;
  String? logoName;
  String? name;
  String? otherCode;
  String? pic;
  num? referencePrice;
  String? skuId;
  int? stock;
  String? titleName;

  OrderGiftExchangeItem();

  factory OrderGiftExchangeItem.fromJson(Map<String, dynamic> json) =>
      _$OrderGiftExchangeItemFromJson(json);

  Map<String, dynamic> toJson() => _$OrderGiftExchangeItemToJson(this);
}

