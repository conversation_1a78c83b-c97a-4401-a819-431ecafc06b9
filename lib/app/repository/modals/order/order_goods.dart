import 'package:json_annotation/json_annotation.dart';

part 'order_goods.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: order_goods
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 11:31
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 11:31
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderGoods {
  String? completeGoodsId;
  String? createTime;
  String? goodsId;
  String? goodsImg;
  String? goodsName;
  int? goodsNum;
  int? goodsType;
  int? id;
  int? orderId;
  String? shopName;
  String? updateTime;

  OrderGoods();

  factory OrderGoods.fromJson(Map<String, dynamic> json) =>
      _$OrderGoodsFromJson(json);

  Map<String, dynamic> toJson() => _$OrderGoodsToJson(this);
}
