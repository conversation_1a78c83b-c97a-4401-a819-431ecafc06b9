// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_gift_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderGiftItem _$OrderGiftItemFromJson(Map<String, dynamic> json) =>
    OrderGiftItem()
      ..activationTime = json['activationTime'] as String?
      ..createTime = json['createTime'] as String?
      ..duration = json['duration'] as int?
      ..endTime = json['endTime'] as String?
      ..forwardUserId = json['forwardUserId'] as int?
      ..fromOrderId = json['fromOrderId'] as int?
      ..giftId = json['giftId'] as int?
      ..invalidTime = json['invalidTime'] as String?
      ..isUse = json['isUse'] as bool?
      ..money = json['money'] as int?
      ..moneyLimit = json['moneyLimit'] as int?
      ..orderId = json['orderId'] as int?
      ..orderNo = json['orderNo'] as String?
      ..orderStatusDesc = json['orderStatusDesc'] as String?
      ..overDue = json['overDue'] as bool?
      ..qlFissionSubsidyRedPacket = json['qlFissionSubsidyRedPacket'] as bool?
      ..shopId = json['shopId'] as int?
      ..skuId = json['skuId'] as String?
      ..status = json['status'] as int?
      ..typeDesc = json['typeDesc'] as String?
      ..typeId = json['typeId'] as int?
      ..updateTime = json['updateTime'] as String?
      ..useTime = json['useTime'] as String?
      ..useType = json['useType'] as int?
      ..userId = json['userId'] as int?;

Map<String, dynamic> _$OrderGiftItemToJson(OrderGiftItem instance) =>
    <String, dynamic>{
      'activationTime': instance.activationTime,
      'createTime': instance.createTime,
      'duration': instance.duration,
      'endTime': instance.endTime,
      'forwardUserId': instance.forwardUserId,
      'fromOrderId': instance.fromOrderId,
      'giftId': instance.giftId,
      'invalidTime': instance.invalidTime,
      'isUse': instance.isUse,
      'money': instance.money,
      'moneyLimit': instance.moneyLimit,
      'orderId': instance.orderId,
      'orderNo': instance.orderNo,
      'orderStatusDesc': instance.orderStatusDesc,
      'overDue': instance.overDue,
      'qlFissionSubsidyRedPacket': instance.qlFissionSubsidyRedPacket,
      'shopId': instance.shopId,
      'skuId': instance.skuId,
      'status': instance.status,
      'typeDesc': instance.typeDesc,
      'typeId': instance.typeId,
      'updateTime': instance.updateTime,
      'useTime': instance.useTime,
      'useType': instance.useType,
      'userId': instance.userId,
    };
