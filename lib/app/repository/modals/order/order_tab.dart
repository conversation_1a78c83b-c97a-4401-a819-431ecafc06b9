import 'package:json_annotation/json_annotation.dart';

part 'order_tab.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.order
/// @ClassName: order_tab
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/18 11:54
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 11:54
/// @UpdateRemark: 更新说明
@JsonSerializable()
class OrderTab {
  int? id;
  String? title;
  int? sort;

  OrderTab();

  factory OrderTab.fromJson(Map<String, dynamic> json) => _$OrderTabFromJson(json);

  Map<String, dynamic> toJson() => _$OrderTabToJson(this);
}

