import 'package:json_annotation/json_annotation.dart';

part 'exchange_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: exchange_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/14 11:52
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 11:52
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ExchangeItem {
  int? id;
  int? goodsId;
  String? pic;
  String? goodsImg;
  String? goodsShowConfig;
  String? name;
  String? goodsName;
  num? integral;
  num? needIntegral;
  num? stock;
  num? goodsStock;

  ExchangeItem();

  factory ExchangeItem.fromJson(Map<String, dynamic> json) =>
      _$ExchangeItemFromJson(json);

  Map<String, dynamic> toJson() => _$ExchangeItemToJson(this);
}

