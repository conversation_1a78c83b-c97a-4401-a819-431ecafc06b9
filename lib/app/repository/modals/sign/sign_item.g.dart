// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SignItem _$SignItemFromJson(Map<String, dynamic> json) => SignItem()
  ..integralNum = json['integralNum'] as int?
  ..afterUpIntegralNum = json['afterUpIntegralNum'] as int?
  ..integral = json['integral'] as String?
  ..day = json['day'] as String?
  ..dayNum = json['dayNum'] as int?
  ..showPicUrl = json['showPicUrl'] as String?;

Map<String, dynamic> _$SignItemToJson(SignItem instance) => <String, dynamic>{
      'integralNum': instance.integralNum,
      'afterUpIntegralNum': instance.afterUpIntegralNum,
      'integral': instance.integral,
      'day': instance.day,
      'dayNum': instance.dayNum,
      'showPicUrl': instance.showPicUrl,
    };
