import 'package:json_annotation/json_annotation.dart';

part 'integral_detail_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: integral_detail_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/14 15:05
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 15:05
/// @UpdateRemark: 更新说明
@JsonSerializable()
class IntegralDetailItem {
  String? modifyReason;
  String? updateTime;
  String? createTime;
  String? addOrSubtract;
  num? modifyNo;

  IntegralDetailItem();

  factory IntegralDetailItem.fromJson(Map<String, dynamic> json) =>
      _$IntegralDetailItemFromJson(json);

  Map<String, dynamic> toJson() => _$IntegralDetailItemToJson(this);
}

