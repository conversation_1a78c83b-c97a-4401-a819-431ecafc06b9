import 'package:json_annotation/json_annotation.dart';

part 'sign_layout_gift_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: sign_layout_gift_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/13 15:13
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 15:13
/// @UpdateRemark: 更新说明
@JsonSerializable()
class SignLayoutGiftItem {
  String? pic;
  int? integral;

  SignLayoutGiftItem();

  factory SignLayoutGiftItem.fromJson(Map<String, dynamic> json) =>
      _$SignLayoutGiftItemFromJson(json);

  Map<String, dynamic> toJson() => _$SignLayoutGiftItemToJson(this);
}

