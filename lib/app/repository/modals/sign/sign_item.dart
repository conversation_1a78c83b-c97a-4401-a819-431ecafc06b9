import 'package:json_annotation/json_annotation.dart';

part 'sign_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: sign_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/13 11:06
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 11:06
/// @UpdateRemark: 更新说明
//{integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第1天, dayNum: 1, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/saas-channel-file/13/IntegralIcon.png, useNewAward: 0, treasureDay: 3, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}
@JsonSerializable()
class SignItem {
  int? integralNum;
  int? afterUpIntegralNum;
  String? integral;
  String? day;
  int? dayNum;
  String? showPicUrl;

  SignItem();

  factory SignItem.fromJson(Map<String, dynamic> json) =>
      _$SignItemFromJson(json);

  Map<String, dynamic> toJson() => _$SignItemToJson(this);
}

