import 'package:json_annotation/json_annotation.dart';

import 'exchange_item.dart';

part 'exchange_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: exchange_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/14 11:52
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 11:52
/// @UpdateRemark: 更新说明
@JsonSerializable()
class ExchangeList {
  List<ExchangeItem>? data;

  ExchangeList();

  factory ExchangeList.fromJson(Map<String, dynamic> json) =>
      _$ExchangeListFromJson(json);

  Map<String, dynamic> toJson() => _$ExchangeListToJson(this);
}

