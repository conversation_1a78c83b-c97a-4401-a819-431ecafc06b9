import 'package:json_annotation/json_annotation.dart';

part 'luck_gift_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: luck_gift_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/14 16:54
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 16:54
/// @UpdateRemark: 更新说明
@JsonSerializable()
class LuckGiftItem {
  String? createTime;
  int? id;
  String? img;
  String? name;
  int? prizeIndex;
  String? prizes;
  int? type;//1.会员 2.积分 3.返利红包
  int? userId;
  // 非服务器返回的，本地计算使用
  int? localIndex;

  LuckGiftItem();

  factory LuckGiftItem.fromJson(Map<String, dynamic> json) =>
      _$LuckGiftItemFromJson(json);

  Map<String, dynamic> toJson() => _$LuckGiftItemToJson(this);
}

