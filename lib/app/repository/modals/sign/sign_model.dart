import 'package:json_annotation/json_annotation.dart';

part 'sign_model.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: sign_model
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/13 11:04
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 11:04
/// @UpdateRemark: 更新说明
// signModel: {signId: 7676, signTime: 2024-11-12, userId: 683038, signCount: 2, haveSign: false},
@JsonSerializable()
class SignModel {
  int? signId;
  String? signTime;
  int? userId;
  int? signCount;
  String? haveSign;

  SignModel();

  factory SignModel.fromJson(Map<String, dynamic> json) =>
      _$SignModelFromJson(json);

  Map<String, dynamic> toJson() => _$SignModelToJson(this);
}

