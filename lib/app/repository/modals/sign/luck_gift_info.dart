import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/sign/luck_gift_item.dart';

part 'luck_gift_info.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: luck_gift_info
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/14 16:44
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 16:44
/// @UpdateRemark: 更新说明
@JsonSerializable()
class LuckGiftInfo {
  List<LuckGiftItem?>? luckyGifts;
  int? maxNumber;
  int? showNumber;
  int? surplusNumber;

  LuckGiftInfo();

  factory LuckGiftInfo.fromJson(Map<String, dynamic> json) =>
      _$LuckGiftInfoFromJson(json);

  Map<String, dynamic> toJson() => _$LuckGiftInfoToJson(this);
}

