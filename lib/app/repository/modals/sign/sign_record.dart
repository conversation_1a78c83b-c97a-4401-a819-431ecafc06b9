import 'package:json_annotation/json_annotation.dart';

import 'sign_item.dart';
import 'sign_model.dart';

part 'sign_record.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: sign_record
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/13 11:02
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 11:02
/// @UpdateRemark: 更新说明
//{success: true, code: 2000, result: 请求成功,
// data: {
// signModel: {signId: 7676, signTime: 2024-11-12, userId: 683038, signCount: 2, haveSign: false},
// signList: [{integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第1天, dayNum: 1, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/saas-channel-file/13/IntegralIcon.png, useNewAward: 0, treasureDay: 3, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 120, afterUpIntegralNum: 240, integral: +120积分, day: 第2天, dayNum: 2, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 2, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 120, afterUpIntegralNum: 240, integral: +120积分, day: 第3天, dayNum: 3, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/saas-channel-file/13/编组@2x.png, useNewAward: 0, treasureDay: 1, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 80, afterUpIntegralNum: 160, integral: +80积分, day: 第4天, dayNum: 4, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/saas-channel-file/13/编组@2x.png, useNewAward: 1, treasureDay: 3, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第5天, dayNum: 5, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 2, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第6天, dayNum: 6, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 1, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第7天, dayNum: 7, gift: 7天省钱卡, existCard: 0, showPicUrl: https://alicdn.msmds.cn/saas-channel-file/13/编组@2x.png, useNewAward: 1, treasureDay: 7, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第8天, dayNum: 8, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 6, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第9天, dayNum: 9, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 5, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第10天, dayNum: 10, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 4, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第11天, dayNum: 11, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 3, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum: 60, afterUpIntegralNum: 120, integral: +60积分, day: 第12天, dayNum: 12, gift: , existCard: 0, showPicUrl: https://alicdn.msmds.cn/api-test/10/IntegralIcon.png, useNewAward: 0, treasureDay: 2, signGoodsDay: null, useSignGoodsState: null, signGoodsInfo: null}, {integralNum
@JsonSerializable()
class SignRecord {
  SignModel? signModel;
  List<SignItem>? signList;

  SignRecord();

  factory SignRecord.fromJson(Map<String, dynamic> json) =>
      _$SignRecordFromJson(json);

  Map<String, dynamic> toJson() => _$SignRecordToJson(this);
}
