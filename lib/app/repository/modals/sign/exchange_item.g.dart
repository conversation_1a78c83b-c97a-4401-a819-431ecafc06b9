// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'exchange_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExchangeItem _$ExchangeItemFromJson(Map<String, dynamic> json) => ExchangeItem()
  ..id = json['id'] as int?
  ..goodsId = json['goodsId'] as int?
  ..pic = json['pic'] as String?
  ..goodsImg = json['goodsImg'] as String?
  ..goodsShowConfig = json['goodsShowConfig'] as String?
  ..name = json['name'] as String?
  ..goodsName = json['goodsName'] as String?
  ..integral = json['integral'] as num?
  ..needIntegral = json['needIntegral'] as num?
  ..stock = json['stock'] as num?
  ..goodsStock = json['goodsStock'] as num?;

Map<String, dynamic> _$ExchangeItemToJson(ExchangeItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'goodsId': instance.goodsId,
      'pic': instance.pic,
      'goodsImg': instance.goodsImg,
      'goodsShowConfig': instance.goodsShowConfig,
      'name': instance.name,
      'goodsName': instance.goodsName,
      'integral': instance.integral,
      'needIntegral': instance.needIntegral,
      'stock': instance.stock,
      'goodsStock': instance.goodsStock,
    };
