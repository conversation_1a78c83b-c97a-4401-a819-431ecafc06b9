import 'package:json_annotation/json_annotation.dart';

import 'integral_detail_item.dart';

part 'integral_detail_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: integral_detail_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/14 15:05
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 15:05
/// @UpdateRemark: 更新说明
@JsonSerializable()
class IntegralDetailList {
  List<IntegralDetailItem>? data;
  int? total;

  IntegralDetailList();

  factory IntegralDetailList.fromJson(Map<String, dynamic> json) =>
      _$IntegralDetailListFromJson(json);

  Map<String, dynamic> toJson() => _$IntegralDetailListToJson(this);
}

