// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'integral_detail_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IntegralDetailList _$IntegralDetailListFromJson(Map<String, dynamic> json) =>
    IntegralDetailList()
      ..data = (json['data'] as List<dynamic>?)
          ?.map((e) => IntegralDetailItem.fromJson(e as Map<String, dynamic>))
          .toList()
      ..total = json['total'] as int?;

Map<String, dynamic> _$IntegralDetailListToJson(IntegralDetailList instance) =>
    <String, dynamic>{
      'data': instance.data,
      'total': instance.total,
    };
