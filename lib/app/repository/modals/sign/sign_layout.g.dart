// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sign_layout.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SignLayout _$SignLayoutFromJson(Map<String, dynamic> json) => SignLayout()
  ..redPacketList = (json['redPacketList'] as List<dynamic>?)
      ?.map((e) => SignLayoutGiftItem.fromJson(e as Map<String, dynamic>))
      .toList()
  ..mainRecoTaskList = (json['mainRecoTaskList'] as List<dynamic>?)
      ?.map((e) => SignLayoutBallItem.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$SignLayoutToJson(SignLayout instance) =>
    <String, dynamic>{
      'redPacketList': instance.redPacketList,
      'mainRecoTaskList': instance.mainRecoTaskList,
    };
