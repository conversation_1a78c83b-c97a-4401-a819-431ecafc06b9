import 'package:json_annotation/json_annotation.dart';

part 'sign_layout_ball_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: sign_layout_ball_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/13 15:58
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 15:58
/// @UpdateRemark: 更新说明
@JsonSerializable()
class SignLayoutBallItem {
  int? collectId;
  num? modifyNo;
  num? integral;
  String? modifyReason;
  String? taskName;
  int? newStatus;

  SignLayoutBallItem();

  factory SignLayoutBallItem.fromJson(Map<String, dynamic> json) =>
      _$SignLayoutBallItemFromJson(json);

  Map<String, dynamic> toJson() => _$SignLayoutBallItemToJson(this);
}

