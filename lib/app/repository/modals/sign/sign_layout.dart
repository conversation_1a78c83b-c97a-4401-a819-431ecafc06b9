import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout_gift_item.dart';

import 'sign_layout_ball_item.dart';

part 'sign_layout.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.sign
/// @ClassName: sign_layout
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/13 15:12
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 15:12
/// @UpdateRemark: 更新说明
@JsonSerializable()
class SignLayout {
  List<SignLayoutGiftItem>? redPacketList;
  List<SignLayoutBallItem>? mainRecoTaskList;

  SignLayout();

  factory SignLayout.fromJson(Map<String, dynamic> json) =>
      _$SignLayoutFromJson(json);

  Map<String, dynamic> toJson() => _$SignLayoutToJson(this);
}

