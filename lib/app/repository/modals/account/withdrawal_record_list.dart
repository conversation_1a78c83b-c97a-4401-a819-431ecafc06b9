import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/account/withdrawal_record.dart';

part 'withdrawal_record_list.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_record_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/14 10:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/14 10:12
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalRecordList {
  bool? hasNextPage;
  List<WithdrawalRecord>? list;
  int? total;

  WithdrawalRecordList();

  factory WithdrawalRecordList.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalRecordListFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalRecordListTo<PERSON><PERSON>(this);
}
