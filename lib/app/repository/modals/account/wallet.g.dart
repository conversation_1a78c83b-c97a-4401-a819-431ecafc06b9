// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wallet.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Wallet _$WalletFromJson(Map<String, dynamic> json) => Wallet()
  ..waitCash = json['waitCash'] as num?
  ..canCash = json['canCash'] as num?
  ..totalEconomize = json['totalEconomize'] as num?
  ..alreadyCash = json['alreadyCash'] as num?;

Map<String, dynamic> _$WalletToJson(Wallet instance) => <String, dynamic>{
      'waitCash': instance.waitCash,
      'canCash': instance.canCash,
      'totalEconomize': instance.totalEconomize,
      'alreadyCash': instance.alreadyCash,
    };
