import 'package:json_annotation/json_annotation.dart';

part 'red_packet.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: red_packet
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/22 14:31
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/22 14:31
/// @UpdateRemark: 更新说明
@JsonSerializable()
class RedPacket {
  int? typeId;
  num? moneyLimit;
  int? useType;
  String? typeDesc;
  String? orderNo;
  String? endTime;
  num? money;

  RedPacket();

  factory RedPacket.fromJson(Map<String, dynamic> json) =>
      _$RedPacketFromJson(json);

  Map<String, dynamic> toJson() => _$RedPacketToJson(this);
}

