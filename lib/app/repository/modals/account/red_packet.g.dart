// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'red_packet.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RedPacket _$RedPacketFromJson(Map<String, dynamic> json) => RedPacket()
  ..typeId = json['typeId'] as int?
  ..moneyLimit = json['moneyLimit'] as num?
  ..useType = json['useType'] as int?
  ..typeDesc = json['typeDesc'] as String?
  ..orderNo = json['orderNo'] as String?
  ..endTime = json['endTime'] as String?
  ..money = json['money'] as num?;

Map<String, dynamic> _$RedPacketToJson(RedPacket instance) => <String, dynamic>{
      'typeId': instance.typeId,
      'moneyLimit': instance.moneyLimit,
      'useType': instance.useType,
      'typeDesc': instance.typeDesc,
      'orderNo': instance.orderNo,
      'endTime': instance.endTime,
      'money': instance.money,
    };
