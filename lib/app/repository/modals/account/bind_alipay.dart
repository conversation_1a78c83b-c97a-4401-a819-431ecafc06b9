import 'package:json_annotation/json_annotation.dart';

part 'bind_alipay.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: bind_alipay
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/9/13 15:33
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/13 15:33
/// @UpdateRemark: 更新说明
@JsonSerializable()
class BindAlipay {
  String? name;
  String? phone;

  BindAlipay();

  factory BindAlipay.fromJson(Map<String, dynamic> json) =>
      _$BindAlipayFromJson(json);

  Map<String, dynamic> toJson() => _$BindAlipayToJson(this);
}

