import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/account/user_fans_item.dart';

part 'user_fans_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: user_fans_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/25 14:25
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/25 14:25
/// @UpdateRemark: 更新说明
@JsonSerializable()
class UserFansList {
  int? contributionNum;
  List<UserFansItem>? fansInfoList;
  bool? hasNextPage;
  int? invitationNum;
  num? totalIncome;

  UserFansList();

  factory UserFansList.fromJson(Map<String, dynamic> json) =>
      _$UserFansListFromJson(json);

  Map<String, dynamic> toJson() => _$UserFansListToJson(this);
}

