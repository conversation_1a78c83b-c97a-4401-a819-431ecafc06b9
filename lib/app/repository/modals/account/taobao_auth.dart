import 'package:json_annotation/json_annotation.dart';

part 'taobao_auth.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: taobao_auth
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/26 10:28
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/26 10:28
/// @UpdateRemark: 更新说明
@JsonSerializable()
class TaoBaoAuth {
  String? accountName;
  String? appKey;
  String? createTime;
  int? id;
  int? relationId;
  int? specialId;
  String? updateTime;
  int? userId;

  TaoBaoAuth();

  factory TaoBaoAuth.fromJson(Map<String, dynamic> json) =>
      _$TaoBaoAuthFromJson(json);

  Map<String, dynamic> toJson() => _$TaoBaoAuthToJson(this);
}
