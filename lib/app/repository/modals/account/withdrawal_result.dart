import 'package:json_annotation/json_annotation.dart';

part 'withdrawal_result.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: withdraw_result
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/13 14:19
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/13 14:19
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalResult {
  String? tradeNo;
  String? paymentNo;
  String? createTime;
  int? money;

  WithdrawalResult();

  factory WithdrawalResult.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalResultFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalResultToJson(this);
}
