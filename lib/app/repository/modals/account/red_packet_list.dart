import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/account/red_packet.dart';

part 'red_packet_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: red_packet_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/22 14:31
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/22 14:31
/// @UpdateRemark: 更新说明
@JsonSerializable()
class RedPacketList {
  List<RedPacket>? data;
  int? total;

  RedPacketList();

  factory RedPacketList.fromJson(Map<String, dynamic> json) =>
      _$RedPacketListFromJson(json);

  Map<String, dynamic> toJson() => _$RedPacketListToJson(this);
}

