import 'package:json_annotation/json_annotation.dart';

part 'withdrawal_record.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: withdrawal_record
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/14 10:10
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/14 10:10
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalRecord {
  num? amount;
  String? createTime;
  int? id;
  String? paymentNo;
  int? platformType;
  String? remake;
  int? state;
  String? tradeNo;
  String? updateTime;
  int? userId;

  WithdrawalRecord();

  factory WithdrawalRecord.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalRecordFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalRecordToJson(this);
}
