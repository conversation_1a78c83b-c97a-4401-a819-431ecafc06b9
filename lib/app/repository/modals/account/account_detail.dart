import 'package:json_annotation/json_annotation.dart';

part 'account_detail.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: account_detail
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/11 11:16
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/11 11:16
/// @UpdateRemark: 更新说明
@JsonSerializable()
class AccountDetail {
  int? userId;
  String? nickName;
  String? avatarUrl;
  String? registTime;
  String? lastLoginTime;
  String? phone;
  int? status;
  int? vipType;
  int? vipLevel;
  bool? bindTbk;
  bool? newUser;
  String? encryptionPhone;
  String? overDueTime;

  AccountDetail();

  factory AccountDetail.fromJson(Map<String, dynamic> json) =>
      _$AccountDetailFromJson(json);

  Map<String, dynamic> toJson() => _$AccountDetailToJson(this);
}
