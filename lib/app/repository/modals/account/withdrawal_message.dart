import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/account/withdrawal_result.dart';

part 'withdrawal_message.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: withdrawal_message
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 16:15
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 16:15
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalMessage {
  bool? status;
  String? msg;
  WithdrawalResult? userWithdraw;

  WithdrawalMessage();

  factory WithdrawalMessage.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalMessageFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalMessageToJson(this);
}
