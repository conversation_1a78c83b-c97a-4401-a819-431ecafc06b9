import 'package:json_annotation/json_annotation.dart';

part 'my_number.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: my_number
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/22 10:59
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/22 10:59
/// @UpdateRemark: 更新说明
@JsonSerializable()
class MyNumber {
  int? giftsNum;

  MyNumber();

  factory MyNumber.fromJson(Map<String, dynamic> json) =>
      _$MyNumberFromJson(json);

  Map<String, dynamic> toJson() => _$MyNumberToJson(this);
}

