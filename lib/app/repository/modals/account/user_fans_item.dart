import 'package:json_annotation/json_annotation.dart';

part 'user_fans_item.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.account
/// @ClassName: user_fans_item
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/25 14:24
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/25 14:24
/// @UpdateRemark: 更新说明
@JsonSerializable()
class UserFansItem {
  String? avatarUrl;
  num? contributionAmount;
  bool? member;
  String? nickname;

  UserFansItem();

  factory UserFansItem.fromJson(Map<String, dynamic> json) =>
      _$UserFansItemFromJson(json);

  Map<String, dynamic> toJson() => _$UserFansItemToJson(this);
}

