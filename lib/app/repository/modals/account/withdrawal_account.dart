import 'package:json_annotation/json_annotation.dart';

part 'withdrawal_account.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: withdrawal_account
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 14:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 14:54
/// @UpdateRemark: 更新说明
@JsonSerializable()
class WithdrawalAccount {
  int? id;
  int? userId;
  String? name;
  String? phone;
  String? createTime;
  String? updateTime;
  bool? canModify;

  WithdrawalAccount();

  factory WithdrawalAccount.fromJson(Map<String, dynamic> json) =>
      _$WithdrawalAccountFromJson(json);

  Map<String, dynamic> toJson() => _$WithdrawalAccountToJson(this);
}
