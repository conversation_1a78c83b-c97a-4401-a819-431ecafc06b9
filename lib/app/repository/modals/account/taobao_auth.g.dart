// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taobao_auth.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaoBaoAuth _$Tao<PERSON>ao<PERSON>rom<PERSON>son(Map<String, dynamic> json) => TaoBaoAuth()
  ..accountName = json['accountName'] as String?
  ..appKey = json['appKey'] as String?
  ..createTime = json['createTime'] as String?
  ..id = json['id'] as int?
  ..relationId = json['relationId'] as int?
  ..specialId = json['specialId'] as int?
  ..updateTime = json['updateTime'] as String?
  ..userId = json['userId'] as int?;

Map<String, dynamic> _$TaoBaoAuthToJson(TaoBaoAuth instance) =>
    <String, dynamic>{
      'accountName': instance.accountName,
      'appKey': instance.appKey,
      'createTime': instance.createTime,
      'id': instance.id,
      'relationId': instance.relationId,
      'specialId': instance.specialId,
      'updateTime': instance.updateTime,
      'userId': instance.userId,
    };
