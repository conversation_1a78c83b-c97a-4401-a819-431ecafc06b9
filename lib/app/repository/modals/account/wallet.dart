import 'package:json_annotation/json_annotation.dart';

part 'wallet.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: wallet
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 16:16
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 16:16
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Wallet {
  num? waitCash;
  num? canCash;
  num? totalEconomize;
  num? alreadyCash; 

  Wallet();

  factory Wallet.fromJson(Map<String, dynamic> json) => _$WalletFromJson(json);

  Map<String, dynamic> toJson() => _$WalletToJson(this);
}
