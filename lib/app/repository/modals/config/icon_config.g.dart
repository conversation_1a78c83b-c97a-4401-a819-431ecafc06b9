// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'icon_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

IconConfig _$IconConfigFromJson(Map<String, dynamic> json) => IconConfig()
  ..advertisingId = json['advertisingId'] as int?
  ..title = json['title'] as String?
  ..pictureUrl = json['pictureUrl'] as String?
  ..type = json['type'] as int?
  ..jumpUrl = json['jumpUrl'] as String?
  ..jumpUrlTitle = json['jumpUrlTitle'] as String?
  ..jumpUrlPicture = json['jumpUrlPicture'] as String?
  ..typeData = json['typeData'] as String?
  ..expirationTime = json['expirationTime'] as String?
  ..sort = json['sort'] as int?
  ..status = json['status'] as int?;

Map<String, dynamic> _$IconConfigToJson(IconConfig instance) =>
    <String, dynamic>{
      'advertisingId': instance.advertisingId,
      'title': instance.title,
      'pictureUrl': instance.pictureUrl,
      'type': instance.type,
      'jumpUrl': instance.jumpUrl,
      'jumpUrlTitle': instance.jumpUrlTitle,
      'jumpUrlPicture': instance.jumpUrlPicture,
      'typeData': instance.typeData,
      'expirationTime': instance.expirationTime,
      'sort': instance.sort,
      'status': instance.status,
    };
