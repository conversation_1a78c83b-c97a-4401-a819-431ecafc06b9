import 'package:json_annotation/json_annotation.dart';

part 'home_tab_pkg.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.config
/// @ClassName: home_tab_pkg
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/8/26 17:07
/// @UpdateUser: frankylee
/// @UpdateData: 2024/8/26 17:07
/// @UpdateRemark: 更新说明
@JsonSerializable()
class HomeTabPkg {
  String? pkgName;
  String? pkgId;
  int? platformType;
  int? pkgType;

  HomeTabPkg();

  factory HomeTabPkg.fromJson(Map<String, dynamic> json) =>
      _$HomeTabPkgFromJson(json);

  Map<String, dynamic> toJson() => _$HomeTabPkgToJson(this);
}

