import 'package:json_annotation/json_annotation.dart';

part 'back_config.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.config
/// @ClassName: back_config
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/11 17:43
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/11 17:43
/// @UpdateRemark: 更新说明
//{id: 3, title:  , backGroundUrl: https://alicdn.msmds.cn/saas-channel-file/13/矩形@3x20220722120056228873307.png, position: null, platformType: 0, status: 1, beginTime: 2022-07-22 12:03:22, endTime: 2026-07-31 00:00:00, remark: , lowVersion: 3.4.0, version: 9.9.9, createTime: 2022-07-22 12:01:34, updateTime: 2022-07-22 12:02:15, now: null}
@JsonSerializable()
class BackConfig {
  int? id;
  String? title;
  String? backGroundUrl;

  BackConfig();

  factory BackConfig.fromJson(Map<String, dynamic> json) =>
      _$BackConfigFromJson(json);

  Map<String, dynamic> toJson() => _$BackConfigToJson(this);
}
