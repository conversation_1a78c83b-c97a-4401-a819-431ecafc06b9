import 'package:json_annotation/json_annotation.dart';
import 'package:msmds_platform/app/repository/modals/config/home_tab_pkg.dart';

part 'home_tab_pkg_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.config
/// @ClassName: home_tab_pkg_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/8/26 17:09
/// @UpdateUser: frankylee
/// @UpdateData: 2024/8/26 17:09
/// @UpdateRemark: 更新说明
@JsonSerializable()
class HomeTabPkgList {
  List<HomeTabPkg>? data;
  HomeTabPkgList();

  factory HomeTabPkgList.fromJson(Map<String, dynamic> json) =>
      _$HomeTabPkgListFromJson(json);

  Map<String, dynamic> toJson() => _$HomeTabPkgListToJson(this);
}

