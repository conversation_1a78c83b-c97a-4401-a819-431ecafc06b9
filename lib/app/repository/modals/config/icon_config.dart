import 'package:json_annotation/json_annotation.dart';

part 'icon_config.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: icon_config
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/8 11:16
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/8 11:16
/// @UpdateRemark: 更新说明
@JsonSerializable()
class IconConfig {
  int? advertisingId;
  String? title;
  String? pictureUrl;
  int? type;
  String? jumpUrl;
  String? jumpUrlTitle;
  String? jumpUrlPicture;
  String? typeData;
  String? expirationTime;
  int? sort;
  int? status;

  IconConfig();

  factory IconConfig.fromJson(Map<String, dynamic> json) =>
      _$IconConfigFromJson(json);

  Map<String, dynamic> toJson() => _$IconConfigToJson(this);

  @override
  String toString() {
    return "IconConfig: jumpUrl:$jumpUrl, typeData: $typeData";
  }
}
