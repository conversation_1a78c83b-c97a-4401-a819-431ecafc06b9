import 'package:json_annotation/json_annotation.dart';

import 'icon_config.dart';

part 'icon_config_list.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: icon_config_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/8 12:22
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/8 12:22
/// @UpdateRemark: 更新说明
@JsonSerializable()
class IconConfigList {
  List<IconConfig>? data;
  IconConfigList();

  factory IconConfigList.fromJson(Map<String, dynamic> json) =>
      _$IconConfigListFromJson(json);

  Map<String, dynamic> toJson() => _$IconConfigListToJson(this);
}
