import 'package:json_annotation/json_annotation.dart';

import 'popularize.dart';

part 'popularize_list.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.popularize
/// @ClassName: popularize_list
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/7 16:03
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/7 16:03
/// @UpdateRemark: 更新说明
@JsonSerializable()
class PopularizeList {
  List<Popularize>? data;

  PopularizeList();

  factory PopularizeList.fromJson(Map<String, dynamic> json) =>
      _$PopularizeListFromJson(json);

  Map<String, dynamic> toJson() => _$PopularizeListToJson(this);
}
