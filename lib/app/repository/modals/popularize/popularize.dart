import 'package:json_annotation/json_annotation.dart';

part 'popularize.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.repository.modals.popularize
/// @ClassName: popularize
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/7 16:03
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/7 16:03
/// @UpdateRemark: 更新说明
@JsonSerializable()
class Popularize {
  int? id;
  String? picture;
  String? url;
  int? sort;
  int? status;
  String? remarks;
  int? type;
  String? data;
  int? platformType;

  Popularize();

  factory Popularize.fromJson(Map<String, dynamic> json) =>
      _$PopularizeFromJson(json);

  Map<String, dynamic> toJson() => _$PopularizeToJson(this);
}

