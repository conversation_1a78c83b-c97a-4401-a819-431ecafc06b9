import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../../utils/router_util.dart';
import '../../../navigation/router.dart';
import '../../../provider/home/<USER>';
import '../../../provider/me/me_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: income_countdown_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/29 11:00
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/29 11:00
/// @UpdateRemark: 更新说明
class IncomeCountdownWidget extends ConsumerWidget {
  const IncomeCountdownWidget({super.key});

  /// 倒计时显示
  Widget _buildCountdownWidget(WidgetRef ref) {
    var difference = ref.watch(validFreeBuyTimerProvider);
    if (difference != null) {
      int days = difference.inDays;
      int hours = difference.inHours % 24;
      int minute = difference.inMinutes % 60;
      int seconds = difference.inSeconds % 60;
      return Row(
        children: [
          _buildCountdownItem(days >= 10 ? "$days" : "0$days", "天"),
          _buildCountdownItem(hours >= 10 ? "$hours" : "0$hours", "时"),
          _buildCountdownItem(minute >= 10 ? "$minute" : "0$minute", "分"),
          _buildCountdownItem(seconds >= 10 ? "$seconds" : "0$seconds", "秒"),
        ],
      );
    }
    return Container();
  }

  /// 倒计时item
  Widget _buildCountdownItem(String value, String title) {
    return Row(
      children: [
        Container(
          width: 17.w,
          height: 17.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: const Color(0xFFFC1937),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: Text(
            value,
            style: TextStyle(fontSize: 11.sp, color: Colors.white),
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 3.w)),
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFFF93324),
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 3.w)),
      ],
    );
  }

  /// 收益item
  Widget _buildInfoItem(BuildContext context, String amount, String title) {
    return InkWell(
      onTap: () {
        RouterUtil.checkLogin(
          context,
          call: () {
            Navigator.pushNamed(context, CsRouter.bill);
          },
          execute: true,
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF999999),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 10.h)),
          Text(
            amount,
            style: TextStyle(
              fontSize: 18.sp,
              color: const Color(0xFF121212),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 80.h,
      color: Colors.white,
      child: OverflowBox(
        alignment: Alignment.bottomCenter,
        maxHeight: 105.h,
        child: Column(
          children: [
            Container(
              height: 70.h,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(20),
                      offset: const Offset(1.0, 5.0),
                      blurRadius: 14.0,
                    )
                  ]),
              margin: EdgeInsets.symmetric(horizontal: 10.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  Expanded(
                    child: IntrinsicHeight(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildInfoItem(
                            context,
                            "${ref.watch(
                              walletInfoProvider.select(
                                (value) => value?.totalEconomize ?? "0.0",
                              ),
                            )}",
                            "累计收益 (元)",
                          ),
                          VerticalDivider(
                            width: 1,
                            indent: 14.h,
                            endIndent: 14.h,
                            color: const Color(0xFFE3E3E2),
                          ),
                          _buildInfoItem(
                            context,
                            "${ref.watch(
                              walletInfoProvider.select(
                                (value) => value?.canCash ?? "0.0",
                              ),
                            )}",
                            "可提现余额 (元)",
                          ),
                          VerticalDivider(
                            width: 1,
                            indent: 14.h,
                            endIndent: 14.h,
                            color: const Color(0xFFE3E3E2),
                          ),
                          _buildInfoItem(
                            context,
                            "${ref.watch(
                              walletInfoProvider.select(
                                (value) => value?.waitCash ?? "0.0",
                              ),
                            )}",
                            "即将入账 (元)",
                          ),
                        ],
                      ),
                    ),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GradientButton(
                        onPress: () {
                          RouterUtil.checkLogin(
                            context,
                            call: () {
                              Navigator.pushNamed(context, CsRouter.bill);
                            },
                            execute: true,
                          );
                        },
                        shadow: false,
                        radius: 25.r,
                        margin: EdgeInsets.only(left: 11.w),
                        padding: EdgeInsets.symmetric(
                          vertical: 9.h,
                          horizontal: 17.w,
                        ),
                        gradient: const LinearGradient(
                          colors: [
                            Color(0xFFFF1864),
                            Color(0xFFFE0D2D),
                          ],
                        ),
                        child: const Text(
                          "去提现",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(padding: EdgeInsets.only(bottom: 12.h)),
            Container(
              padding: EdgeInsets.only(left: 10.w),
              child: Row(
                children: [
                  const Text(
                    "距免单资格过期还有",
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF333333),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(right: 5.w)),
                  _buildCountdownWidget(ref),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
