import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/search/platform_provider.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_header
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/18 12:10
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/18 12:10
/// @UpdateRemark: 更新说明
class ActivitySearchHeader extends StatelessWidget {
  const ActivitySearchHeader({
    Key? key,
    required this.t,
    required this.deltaExtent,
    required this.scaleValue,
    required this.platform,
  }) : super(key: key);

  /// 折叠系数
  final double t;

  /// 最大高度与最小高度的差值
  final double deltaExtent;

  /// 宽度缩放比率
  final double scaleValue;

  /// 平台类型 （1：淘宝，2：京东，3：拼多多）
  final int platform;

  /// 获取标题
  String getTitle() {
    var title = "";
    switch (platform) {
      case 1:
        title = "淘宝返现";
        break;
      case 2:
        title = "京东返现";
        break;
      case 3:
        title = "拼多多返现";
        break;
    }
    return title;
  }

  /// 获取返现图
  String getMidAssets() {
    var assets = "";
    switch (platform) {
      case 1:
        assets = tbActivityMid;
        break;
      case 2:
        assets = jdActivityMid;
        break;
      case 3:
        assets = pddActivityMid;
        break;
    }
    return assets;
  }

  /// 搜索
  Widget _buildSearch() {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 0.h, 10.w, 0.h),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 45.h,
              padding: EdgeInsets.only(left: 12.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25.h),
                  bottomLeft: Radius.circular(25.h),
                ),
              ),
              child: Row(
                children: [
                  Image.asset(
                    searchIcon,
                    width: 15.w,
                    height: 15.w,
                    color: const Color(0xFFFB685D),
                  ),
                  Padding(padding: EdgeInsets.only(right: 7.w)),
                  Text(
                    "搜索商品下单 都能拿返利",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFFB7B7B7),
                    ),
                  )
                ],
              ),
            ),
          ),
          Container(
            height: 45.h,
            width: 71.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFFF5B7),
                  Color(0xFFFFBD0F),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(25.h),
                bottomRight: Radius.circular(25.h),
              ),
            ),
            child: Text(
              "搜索",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFFF81100),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// appbar上的滑动固定搜索
  Widget _buildSliverSearch(BuildContext context) {
    return Row(
      children: [
        _buildBack(),
        SizedBox(
          width: 12.w,
        ),
        Text(
          getTitle(),
          style: TextStyle(
            fontSize: 17.sp,
            color: const Color(0xFFFFFFFF),
          ),
        ),
        Expanded(
          child: Consumer(
            builder: (context, ref, child) {
              return InkWell(
                onTap: () {
                  ref
                      .watch(platformProvider.notifier)
                      .setCurrentPlatform(platform, isSearch: false);
                  Navigator.pushNamed(context, CsRouter.searchPre);
                },
                child: Container(
                  height: 32.h,
                  margin: EdgeInsets.fromLTRB(25.w, 0.h, 2.w, 0.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15.h),
                  ),
                  child: Row(
                    children: [
                      Padding(padding: EdgeInsets.only(right: 12.w)),
                      Image.asset(
                        searchIcon,
                        width: 15.w,
                        height: 15.w,
                        color: const Color(0xFFA9A9A9),
                      ),
                      Padding(padding: EdgeInsets.only(right: 8.w)),
                      Expanded(
                        child: Text(
                          "搜商品 拿返现",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFF999999),
                          ),
                        ),
                      ),
                      Container(
                        height: 26.h,
                        padding: EdgeInsets.symmetric(horizontal: 14.w),
                        margin: EdgeInsets.only(right: 3.w),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFF3F4E),
                              Color(0xFFFA2C1C),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          borderRadius: BorderRadius.circular(15.h),
                        ),
                        child: Text(
                          "搜索",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 返回
  Widget _buildBack() {
    return const Leading(
      color: Colors.white,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF40008).withOpacity(t),
      ),
      padding: EdgeInsets.fromLTRB(
        10,
        MediaQuery.of(context).padding.top,
        10,
        0,
      ),
      child: Stack(
        alignment: Alignment.centerRight,
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 44.h,
              alignment: Alignment.center,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Positioned(
                          left: 0,
                          child: _buildBack(),
                        ),
                        Opacity(
                          opacity: 1 - t,
                          child: Text(
                            getTitle(),
                            style: TextStyle(
                              fontSize: 20.sp,
                              color: const Color(0xFFFFFFFF),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Opacity(
                    opacity: t,
                    child: _buildSliverSearch(context),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 44.h,
            left: 0,
            right: 0,
            child: Opacity(
              opacity: 1 - t,
              child: Image.network(
                getMidAssets(),
                height: 64.h,
                fit: BoxFit.fitHeight,
              ),
            ),
          ),
          Positioned(
            top: 108.h,
            left: 0,
            right: 0,
            child: Opacity(
              opacity: 1 - t,
              child: Consumer(
                builder: (context, ref, child) {
                  return Container(
                    alignment: Alignment.centerLeft,
                    // transform: Transform.translate(
                    //   offset: Offset(0, -t * deltaExtent),
                    // ).transform,
                    height: 45.h,
                    child: LayoutBuilder(
                      builder:
                          (BuildContext context, BoxConstraints constraints) {
                        return InkWell(
                          onTap: () {
                            ref
                                .watch(platformProvider.notifier)
                                .setCurrentPlatform(platform, isSearch: false);
                            Navigator.pushNamed(context, CsRouter.searchPre);
                          },
                          child: _buildSearch(),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
