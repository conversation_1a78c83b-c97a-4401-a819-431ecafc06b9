import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/view/activity/widgets/icon_animal.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: activity_bottom
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/21 14:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/21 14:49
/// @UpdateRemark: 更新说明
class ActivityBottom extends ConsumerWidget {
  const ActivityBottom({
    super.key,
    required this.platform,
  });

  final int platform;

  List<String> getAnimalList() {
    List<String> assets = [];
    switch (platform) {
      case 1:
        assets = const [jdAnimal, pddAnimal, mtAnimal, eleAnimal];
        break;
      case 2:
        assets = const [tbAnimal, pddAnimal, mtAnimal, eleAnimal];
        break;
      case 3:
        assets = const [tbAnimal, jdAnimal, mtAnimal, eleAnimal];
        break;
    }
    return assets;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          //阴影
          BoxShadow(
            color: Colors.black38,
            offset: Offset(1.0, 1.0),
            blurRadius: 1.0,
          )
        ],
      ),
      height: 50.h + MediaQuery.of(context).padding.bottom,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).padding.bottom,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          InkWell(
            onTap: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                Navigator.pushNamed(context, CsRouter.home);
              }
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FrameAnimationImage(
                  assetList: getAnimalList(),
                  width: 22.w,
                  height: 22.w,
                ),
                Padding(padding: EdgeInsets.only(bottom: 2.h)),
                Text(
                  "更多返利",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: const Color(0xFFF93324),
                  ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              ref.read(homeProvider.notifier).jumpToPage(1);
              Navigator.pop(context);
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.network(
                  mySavingIcon,
                  width: 22.w,
                  height: 22.w,
                ),
                Padding(padding: EdgeInsets.only(bottom: 2.h)),
                Text(
                  "我的返现",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
