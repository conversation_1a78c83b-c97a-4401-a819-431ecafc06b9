import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/activity/activity_provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../../provider/config/config_provider.dart';
import '../../../repository/modals/config/icon_config.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: activity_icon
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/18 14:57
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/18 14:57
/// @UpdateRemark: 更新说明

class ActivityIcon extends ConsumerStatefulWidget {
  const ActivityIcon({
    super.key,
    required this.platform,
  });

  final int platform;

  @override
  ActivityIconState createState() => ActivityIconState();
}

class ActivityIconState extends ConsumerState<ActivityIcon> {
  final PageController _pageController = PageController();

  int activeIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController.addListener(_pageScrollListener);
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.removeListener(_pageScrollListener);
    _pageController.dispose();
  }

  void _pageScrollListener() {
    setState(() {
      if (_pageController.page != null) {
        activeIndex = _pageController.page!.round();
      }
    });
  }

  Widget _buildItem(BuildContext context, IconConfig mainIconData) {
    return InkWell(
      onTap: () {
        ref
            .read(configItemClickProvider.notifier)
            .configItemClick(context, mainIconData);
      },
      child: SizedBox(
        width: (MediaQuery.of(context).size.width / 5).floorToDouble(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.network(
              mainIconData.pictureUrl ?? "",
              width: 40.w,
              height: 40.h,
              fit: BoxFit.contain,
            ),
            Text(
              mainIconData.title ?? "",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    int type = 9;
    switch (widget.platform) {
      case 1:
        type = 11;
        break;
      case 2:
        type = 10;
        break;
      case 3:
        type = 9;
        break;
    }
    return ref.watch(fetchActivityIconConfigProvider(type)).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return Container(
          margin: EdgeInsets.only(top: 12.h),
          child: Column(
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: data[activeIndex].length > 5 ? 158.h : 78.h,
                curve: Curves.ease,
                child: PageView.builder(
                  controller: _pageController,
                  itemBuilder: (context, index) {
                    return Wrap(
                      alignment: WrapAlignment.start,
                      runSpacing: 14.h,
                      children: data[index]
                          .map(
                            (e) => _buildItem(context, e),
                          )
                          .toList(),
                    );
                  },
                  itemCount: data.length,
                ),
              ),
              Padding(padding: EdgeInsets.only(top: 8.h)),
              SmoothPageIndicator(
                controller: _pageController,
                count: data.length,
                effect: CustomizableEffect(
                  spacing: 5.w,
                  dotDecoration: DotDecoration(
                    width: 4,
                    height: 4,
                    color: const Color(0xFFEAEAEA),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  activeDotDecoration: DotDecoration(
                    width: 11,
                    height: 4,
                    color: const Color(0xFFFF0E37),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );

    // return Container(
    //   color: Colors.white,
    //   height: widget.platform == 3 ? 172.h : 88.h,
    //   padding: EdgeInsets.fromLTRB(16.w, 7.h, 16.w, 0.h),
    //   child: Wrap(
    //     spacing: 30.w,
    //     runSpacing: 7.h,
    //     alignment: WrapAlignment.spaceAround,
    //     children: icons.map((e) => _buildItem(context, ref, e)).toList(),
    //   ),
    // );
  }
}
