import 'package:flutter/material.dart';
import 'package:msmds_platform/app/view/activity/widgets/search_header.dart';

class ActivityPersistentHeader extends SliverPersistentHeaderDelegate {
  final double max;
  final double min;
  final int platform;

  ActivityPersistentHeader({
    this.max = 50,
    this.min = 40,
    required this.platform,
  }) : assert(max >= min);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    final double deltaExtent = max - min;
    // 0.0 -> Expanded
    // 1.0 -> Collapsed to toolbar
    final double t = (shrinkOffset / deltaExtent).clamp(0.0, 1.0);

    final double scaleValue = Tween<double>(begin: 1, end: 1.12).transform(t);

    return ActivitySearchHeader(
      t: t,
      deltaExtent: deltaExtent,
      scaleValue: scaleValue,
      platform: platform,
    );
  }

  @override
  double get maxExtent => max;

  @override
  double get minExtent => min;

  @override
  bool shouldRebuild(covariant ActivityPersistentHeader oldDelegate) =>
      max != oldDelegate.max || min != oldDelegate.min;
}
