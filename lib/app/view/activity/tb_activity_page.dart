import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/activity/widgets/activity_bottom.dart';
import 'package:msmds_platform/app/view/activity/widgets/activity_icon.dart';

import '../../../common/img/icon_addres.dart';
import '../../../common/widgets/back/back_widget.dart';
import '../../../common/widgets/delegate/persistent_builder.dart';
import '../../../config/global_config.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../../widgets/tabbar/rect_tab_indicator.dart';
import '../../provider/activity/activity_provider.dart';
import '../../repository/modals/goods/goods_pkg.dart';
import '../home/<USER>/skeleton/main_skeleton.dart';
import '../home/<USER>/widgets/main_goods_item.dart';
import 'delegate/activity_persistent_header.dart';

/// Copyright (C), 2021-2023, <PERSON>y Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: tb_activity_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/18 10:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/18 10:54
/// @UpdateRemark: 更新说明

class TbActivityPage extends ConsumerStatefulWidget {
  const TbActivityPage({super.key});

  @override
  TbActivityPageState createState() => TbActivityPageState();
}

class TbActivityPageState extends ConsumerState<TbActivityPage>
    with SingleTickerProviderStateMixin {
  late ScrollController _scrollController;
  TabController? _tabController;

  OverlayEntry? _overlayEntry;

  final ValueNotifier<bool> _isShowOverlayTab = ValueNotifier(false);

  GlobalKey stickyKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) {
        /// 显示配置弹窗
        if (GlobalConfig.account != null) {
          ref
              .read(activityOrdinaryDialogProvider.notifier)
              .showTbActivityDialog();
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _tabController?.dispose();
    _hideOverlayTabBar();
    super.dispose();
  }

  /// 显示tabbar全部内容
  void _showOverlayTabBar(BuildContext context) {
    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return _buildOverTabBar();
      },
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 关闭tabbar全部内容
  void _hideOverlayTabBar() {
    _isShowOverlayTab.value = false;
    if (_overlayEntry != null && _overlayEntry!.mounted) {
      _overlayEntry?.remove();
    }
  }

  /// tabbar滑动到指定位置
  void _scrollToOffset() async {
    // 使用 GlobalKey 获取 TabBar 的位置
    final RenderBox renderBox =
        stickyKey.currentContext?.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // 计算滚动偏移量，使 TabBar 距离屏幕顶部 88 的距离
    final targetOffset = _scrollController.offset +
        position.dy -
        (88.h - MediaQuery.of(context).padding.top);

    await _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    if (mounted) {
      _isShowOverlayTab.value = true;
      _showOverlayTabBar(context);
    }
  }

  /// tab bar overlay
  Widget _buildOverTabBar() {
    var tabs = ref.watch(tbGoodsPkgListProvider);
    var currentPkg = ref.watch(
      goodsByPkgProvider.select((value) => value.pkgId),
    );

    Widget child = Container();
    if (tabs != null && tabs.isNotEmpty) {
      child = Container(
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10),
            bottomRight: Radius.circular(10),
          ),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 13.w,
          vertical: 13.h,
        ),
        child: Wrap(
          spacing: 14.w,
          runSpacing: 16.h,
          children: tabs
              .map(
                (e) => _buildOverlayTabItem(tabs, currentPkg, e),
              )
              .toList(),
        ),
      );
    }

    return InkWell(
      onTap: () {
        _hideOverlayTabBar();
      },
      child: Container(
        margin: EdgeInsets.only(
          top: 88.h + MediaQuery.of(context).padding.top,
        ),
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        color: Colors.black.withAlpha(80),
        child: Column(
          children: [
            child,
          ],
        ),
      ),
    );
  }

  /// overlay tab item
  Widget _buildOverlayTabItem(
    List<GoodsPkg?> list,
    GoodsPkg? currentPkg,
    GoodsPkg? goodsPkg,
  ) {
    return InkWell(
      onTap: () {
        int index = list.indexWhere((element) => element?.id == goodsPkg?.id);
        _tabController?.animateTo(index);
        ref.read(goodsByPkgProvider.notifier).setPkg(goodsPkg);
        _hideOverlayTabBar();
      },
      child: Container(
        decoration: BoxDecoration(
          color: currentPkg?.id == goodsPkg?.id
              ? const Color(0xFFFFECEF)
              : const Color(0xFFF3F3F3),
          borderRadius: BorderRadius.circular(15),
        ),
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 5.h),
        child: Text(
          goodsPkg?.pkgName ?? "",
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: currentPkg?.id == goodsPkg?.id
                ? FontWeight.w500
                : FontWeight.normal,
            color: currentPkg?.id == goodsPkg?.id
                ? const Color(0xFFFF0E38)
                : const Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  /// tab bar
  Widget _buildTabBar() {
    var tabs = ref.watch(tbGoodsPkgListProvider);
    if (tabs == null || tabs.isEmpty) {
      return Container();
    }
    return Row(
      key: stickyKey,
      children: [
        Expanded(
          child: Container(
            color: const Color(0xFFFFFFFF),
            height: 44.h,
            child: TabBar(
              controller: _tabController ??= TabController(
                length: tabs.length,
                vsync: this,
              ),
              indicatorSize: TabBarIndicatorSize.label,
              isScrollable: true,
              indicator: const RectTabIndicator(
                indicatorSize: 4,
                offset: 6,
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Color(0xFFFF0E38),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              unselectedLabelColor: const Color(0xFF666666),
              unselectedLabelStyle:
                  TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
              labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
              labelColor: const Color(0xFFFF0E38),
              tabs: tabs
                  .map(
                    (e) => Tab(text: e?.pkgName ?? ""),
                  )
                  .toList(),
              onTap: (index) {
                var pkg = tabs[index];
                ref.read(goodsByPkgProvider.notifier).setPkg(pkg);
              },
            ),
          ),
        ),
        InkWell(
          onTap: () {
            _scrollToOffset();
          },
          child: Container(
            width: 44.w,
            height: 44.h,
            color: Colors.white,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned(
                  left: 2.w,
                  child: Container(
                    width: 1.w,
                    height: 19.h,
                    color: const Color(0xFFEEEEEE),
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: _isShowOverlayTab,
                  builder: (context, show, child) {
                    return Icon(
                      show
                          ? Icons.keyboard_arrow_up_rounded
                          : Icons.keyboard_arrow_down_rounded,
                      color: const Color(0xFFC1C1C1),
                      size: 22.r,
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (_isShowOverlayTab.value) {
          _hideOverlayTabBar();
          return false;
        }
        Navigator.pop(context);
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: AnnotatedRegion(
          value: const SystemUiOverlayStyle(
            statusBarIconBrightness: Brightness.light,
            statusBarColor: Colors.transparent,
            systemNavigationBarColor: Colors.transparent,
            systemNavigationBarIconBrightness: Brightness.dark,
            systemNavigationBarContrastEnforced: false,
          ),
          child: Stack(
            children: [
              BackWidget(
                scrollController: _scrollController,
                assets: tbActivityBg,
              ),
              CustomListView(
                controller: _scrollController,
                onLoadMore: () async {
                  ref.read(goodsByPkgProvider.notifier).loadMore();
                },
                sliverHeader: [
                  SliverToBoxAdapter(
                    child: Container(),
                  ),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: ActivityPersistentHeader(
                      max: 153.h + MediaQuery.of(context).padding.top,
                      min: 44.h + MediaQuery.of(context).padding.top,
                      platform: 1,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Container(
                      margin: EdgeInsets.only(top: 16.h),
                      padding: EdgeInsets.symmetric(horizontal: 14.w),
                      child: Image.network(
                        tbDescBanner,
                        width: 234.w,
                        height: 143.h,
                      ),
                    ),
                  ),
                  const SliverToBoxAdapter(
                    child: ActivityIcon(
                      platform: 1,
                    ),
                  ),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: PersistentBuilder(
                      max: 44.h,
                      min: 44.h,
                      builder: (_, offset) {
                        return _buildTabBar();
                      },
                    ),
                  ),
                ],
                data: ref.watch(
                  goodsByPkgProvider.select((value) => value.goods),
                ),
                footerState: ref.watch(
                  goodsByPkgProvider.select((value) => value.loadState),
                ),
                renderItem: (context, index, item) {
                  return MainGoodsItem(
                    index: index,
                    goods: item,
                    source: "tb_activity",
                  );
                },
                empty: const MainSkeleton(),
              ),
            ],
          ),
        ),
        bottomNavigationBar: const ActivityBottom(
          platform: 1,
        ),
      ),
    );
  }
}
