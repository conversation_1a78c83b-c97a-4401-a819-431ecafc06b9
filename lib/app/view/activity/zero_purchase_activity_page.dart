import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/activity/widgets/income_countdown_widget.dart';

import '../../../common/img/icon_addres.dart';
import '../../../common/widgets/appbar/leading.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../provider/conversion/link_conversion_provider.dart';
import '../../provider/home/<USER>';
import '../../repository/modals/activity/free_buy_good_item.dart';
import '../home/<USER>/dialog/service_dialog.dart';
import 'dialog/free_buy_rule_dialog.dart';
import 'dialog/ordering_notice_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: zero_purchase_activity_page
/// @Description: 0元购活动
/// @Author: frankylee
/// @CreateDate: 2024/3/28 11:20
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 11:20
/// @UpdateRemark: 更新说明
class ZeroPurchaseActivityPage extends ConsumerWidget {
  const ZeroPurchaseActivityPage({super.key});

  /// 头部组件
  Widget _buildHeaderWidget(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: Column(
        children: [
          Stack(
            children: [
              Image.network(
                freeBuyTitleBg,
                width: MediaQuery.of(context).size.width,
                fit: BoxFit.fitWidth,
                errorBuilder: (context, o, s) {
                  return SizedBox(
                    width: MediaQuery.of(context).size.width,
                    height: 159.h,
                  );
                },
              ),
              Positioned(
                right: 0,
                top: 45.h,
                child: InkWell(
                  onTap: () {
                    FreeBuyRuleDialog.ruleDialog();
                  },
                  child: Container(
                    width: 50.w,
                    height: 22.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: const Color(0x40FFFFFF),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(11.r),
                        bottomLeft: Radius.circular(11.r),
                      ),
                    ),
                    child: const Text(
                      "活动规则",
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              Positioned(
                right: 0,
                top: 73.h,
                child: InkWell(
                  onTap: () {
                    ServiceDialog.showServiceDialog();
                  },
                  child: Container(
                    width: 50.w,
                    height: 22.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: const Color(0x40FFFFFF),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(11.r),
                        bottomLeft: Radius.circular(11.r),
                      ),
                    ),
                    child: const Text(
                      "客服",
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const IncomeCountdownWidget(),
        ],
      ),
    );
  }

  /// 商品item
  Widget _buildGoodsItem(WidgetRef ref, FreeBuyGoodItem? goodItem) {
    return InkWell(
      onTap: () {
        OrderingNoticeDialog.noticeDialog(
          goodItem,
          () async {
            ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
                  1,
                  materialId: goodItem?.tbItemId,
                  couponUrl: goodItem?.couponUrl,
                  goodsSign: goodItem?.tbItemId,
                  goodsId: goodItem?.tbItemId,
                );
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.r),
        ),
        padding: EdgeInsets.symmetric(vertical: 9.h, horizontal: 10.w),
        margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
        child: Row(
          children: [
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Image.network(
                    goodItem?.cover ?? "",
                    width: 112.w,
                    height: 112.h,
                    fit: BoxFit.contain,
                  ),
                ),
                Positioned(
                  top: 0,
                  left: 0,
                  child: Image.network(
                    freeBuyTag,
                    width: 52.w,
                    height: 23.h,
                    fit: BoxFit.fitWidth,
                  ),
                ),
              ],
            ),
            Padding(padding: EdgeInsets.only(right: 11.w)),
            Expanded(
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 1,
                          horizontal: 3,
                        ),
                        margin: EdgeInsets.only(right: 4.w),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: const Color(0xFFFB133C),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                        child: Text(
                          "${goodItem?.logoName}",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: const Color(0xFFFB133C),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          "${goodItem?.title}",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF202123),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 8.h)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "官网价 ￥${goodItem?.wlPrice}",
                        style: TextStyle(
                          decoration: TextDecoration.lineThrough,
                          fontSize: 10.sp,
                          color: const Color(0xFFAFAFAF),
                        ),
                      ),
                      Text(
                        "已售${goodItem?.volume}",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: const Color(0xFFAAAAAA),
                        ),
                      ),
                    ],
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 32.h)),
                  Container(
                    height: 32.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFE7EB),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.r),
                        topRight: Radius.circular(16.r),
                        bottomLeft: Radius.circular(8.r),
                        bottomRight: Radius.circular(16.r),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: const Color(0xFFF93324),
                            ),
                            children: [
                              WidgetSpan(
                                child: Padding(
                                  padding: EdgeInsets.only(left: 10.w),
                                ),
                              ),
                              TextSpan(
                                text: "0",
                                style: TextStyle(fontSize: 28.sp),
                              ),
                              const TextSpan(text: "元"),
                            ],
                          ),
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            // Padding(
                            //   padding: EdgeInsets.only(bottom: 6.h, right: 7.w),
                            //   child: Text(
                            //     "仅剩 34 %",
                            //     style: TextStyle(
                            //       fontSize: 10.sp,
                            //       color: const Color(0xFFF93324),
                            //     ),
                            //   ),
                            // ),
                            Container(
                              height: 32.h,
                              padding: EdgeInsets.symmetric(horizontal: 12.w),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: const Color(0xFFFE4511),
                                borderRadius: BorderRadius.circular(16.r),
                              ),
                              child: Text(
                                "马上抢 >>",
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 44.h,
        title: Text(
          '0元免单',
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        leading: const Leading(),
      ),
      body: CustomListView(
        data: ref.watch(
          freeBuyGoodsListProvider.select((value) => value.goodsList),
        ),
        onLoadMore: () async {
          ref.read(freeBuyGoodsListProvider.notifier).loadMore();
        },
        footerState: ref.watch(
          freeBuyGoodsListProvider.select((value) => value.loadState),
        ),
        sliverHeader: [
          SliverToBoxAdapter(
            child: _buildHeaderWidget(context),
          ),
        ],
        renderItem: (context, index, o) {
          return _buildGoodsItem(ref, o);
        },
      ),
    );
  }
}
