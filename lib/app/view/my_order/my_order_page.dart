import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/order/order_provider.dart';
import 'package:msmds_platform/app/view/my_order/widgets/order_item_widget.dart';
import 'package:msmds_platform/app/view/my_order/widgets/tab_switch_widget.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import 'widgets/tab_slide_widget.dart';

class MyOrderPage extends ConsumerWidget {
  const MyOrderPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 44.h,
        title: Text(
          '我的订单',
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        leading: const Leading(),
      ),
      backgroundColor: const Color(0xFFF5F5F5),
      body: Column(
        children: [
          const TabSlideWidget(),
          const TabSwitchWidget(),
          Expanded(
            child: CustomListView(
              onRefresh: () async {
                ref.read(orderListProvider.notifier).loadGoods();
              },
              onLoadMore: () async {
                ref.read(orderListProvider.notifier).loadMore();
              },
              data: ref.watch(
                orderListProvider.select((value) => value.orderList),
              ),
              footerState: ref.watch(
                orderListProvider.select((value) => value.loadState),
              ),
              sliverHeader: [
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 12.h,
                  ),
                ),
              ],
              empty: Container(
                padding: EdgeInsets.only(top: 69.h),
                alignment: Alignment.center,
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    Image.network(
                      orderEmpty,
                      width: 239.w,
                      fit: BoxFit.contain,
                    ),
                    Positioned(
                      bottom: 15.h,
                      child: Text(
                        "暂无订单",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              renderItem: (context, index, item) {
                return OrderItemWidget(
                  order: item,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
