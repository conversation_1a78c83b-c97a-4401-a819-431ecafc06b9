import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/order/order_provider.dart';

class TabSwitchWidget extends ConsumerWidget {
  const TabSwitchWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: OrderFilter.values.length,
      child: Container(
        margin: EdgeInsets.fromLTRB(12.w, 10.h, 12.w,0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        alignment: Alignment.center,
        height: 44.h,
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          isScrollable: true,
          indicator: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(50),
          ),
          // indicatorPadding: EdgeInsets.symmetric(vertical: 6.h),
          unselectedLabelColor: const Color(0xFF666666),
          unselectedLabelStyle:
          TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
          labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
          labelColor: const Color(0xFFFB133C),
          tabs: OrderFilter.values
              .map(
                (e) => Container(
              margin: EdgeInsets.symmetric(horizontal: 5.w),
              child: Tab(text: e.name),
            ),
          )
              .toList(),
          onTap: (index) {
            ref.read(orderListProvider.notifier).filterOrderState(index);
          },
        ),
      ),
    );
  }
}
