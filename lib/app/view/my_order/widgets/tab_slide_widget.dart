import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/order/order_provider.dart';
import 'package:msmds_platform/widgets/tabbar/rect_tab_indicator.dart';

class TabSlideWidget extends ConsumerStatefulWidget {
  const TabSlideWidget({super.key});

  @override
  TabSlideWidgetState createState() => TabSlideWidgetState();
}

class TabSlideWidgetState extends ConsumerState<TabSlideWidget> {
  @override
  Widget build(BuildContext context) {
    var tabs = ref.watch(orderTabListProvider);
    if (tabs == null || tabs.isEmpty) {
      return const SizedBox();
    }
    return DefaultTabController(
      length: tabs.length,
      child: Container(
        height: 40.h,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          isScrollable: true,
          indicator: RectTabIndicator(
            indicatorSize: 3.h,
            offset: 8,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF666666),
          unselectedLabelStyle:
              TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
          labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
          labelColor: const Color(0xFFFF0E38),
          tabs: tabs.map((e) => Tab(text: e.title)).toList(),
          onTap: (index) {
            var orderType = tabs[index];
            ref.read(orderListProvider.notifier).selectOrderType(orderType);
          },
        ),
      ),
    );
  }
}
