import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../provider/order/order_detail_provider.dart';

class OrderItemWidget extends StatelessWidget {
  const OrderItemWidget({
    super.key,
    required this.order,
  });

  final OrderDetail? order;

  Widget _buildAmountInformation(String title, String amount) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF999999),
          ),
        ),
        SizedBox(
          width: 3.w,
        ),
        Text(
          "¥",
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          amount,
          style: TextStyle(
            fontSize: 18.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildButton(String text, {Function()? onPress}) {
    return InkWell(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 10.w,
          vertical: 6.h,
        ),
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0XFFA7A7A7),
            width: 0.5.w,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  // 退款状态
  Widget _buildRefundStatus() {
    if (order?.refundTag == 0) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.only(bottom: 5.h),
      child: Text(
        "${order?.refundDesc}",
        style: TextStyle(
          fontSize: 9.sp,
          color: const Color(0xFF818181),
        ),
      ),
    );
  }

  // 再次购买按钮
  Widget _buildBuyAgain(BuildContext context) {
    var orderType = order?.orderType;
    if (orderType == null) return const SizedBox();
    var canBuyAgain = (orderType < 12 || orderType == 19 || orderType == 21) &&
        order?.shopId != null;
    if (canBuyAgain || orderType == 70) {
      return _buildButton(
        "再次购买",
        onPress: () {
          if (order?.shopId != null) {
            Navigator.pushNamed(
              context,
              CsRouter.goodsDetailPage,
              arguments: {
                "skuId": order?.shopId,
                "platformType": order?.orderType,
              },
            );
          }
        },
      );
    }
    return const SizedBox();
  }

  // 激活红包按钮
  Widget _buildActiveGift(BuildContext context) {
    if (order?.canUseRedPacket != true) {
      return const SizedBox();
    }
    return Row(
      children: [
        SizedBox(
          width: 10.w,
        ),
        Consumer(builder: (context, ref, child) {
          return GradientButton(
            onPress: () {
              if (order?.orderNo != null) {
                ref
                    .watch(currentOrderProvider.notifier)
                    .setCurrentOrder(order!.orderNo!);
                Navigator.pushNamed(context, CsRouter.orderDetail);
              }
            },
            padding: EdgeInsets.symmetric(
              horizontal: 10.w,
              vertical: 6.h,
            ),
            radius: 30.r,
            shadow: false,
            gradient: const LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [Color(0xFFFF6B2B), Color(0xFFFF1D1D), Color(0xFFFF0F77)],
            ),
            child: Text(
              "激活红包",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFFFFFFFF),
              ),
            ),
          );
        }),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    Color statusColor = const Color(0xFFFE7700);
    if (order?.status == 5) {
      // 已结算
      statusColor = const Color(0xFFFB133C);
    } else if (order?.status == 6 ||
        (order != null && order!.status != null && order!.status! < 0)) {
      // 退款或者无效订单
      statusColor = const Color(0xFFC0C0C0);
    }
    return Container(
      margin: EdgeInsets.fromLTRB(12.w, 0, 12.w, 10.h),
      padding: EdgeInsets.symmetric(
        horizontal: 12.w,
        vertical: 12.h,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Image.network(
                  "${order?.shopImg}",
                  width: 80.w,
                  height: 80.w,
                  fit: BoxFit.cover,
                  errorBuilder: (context, o, s) {
                    return Container(
                      width: 80.w,
                      height: 80.w,
                      color: Colors.grey.withAlpha(60),
                    );
                  },
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        "${order?.shopName}",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF000000),
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(
                      width: 20.w,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        _buildRefundStatus(),
                        Text(
                          "${order?.statusDesc}",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: statusColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 11.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildAmountInformation("实付", "${order?.amount}"),
              SizedBox(
                width: 18.w,
              ),
              _buildAmountInformation(
                "返现",
                "${order?.realCommission}",
              ),
              if (order != null &&
                  order!.redPacketAmount != null &&
                  order!.redPacketAmount! > 0)
                Row(
                  children: [
                    SizedBox(
                      width: 18.w,
                    ),
                    _buildAmountInformation(
                      "激活红包",
                      "${order?.redPacketAmount}",
                    ),
                  ],
                ),
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildBuyAgain(context),
              SizedBox(
                width: 10.w,
              ),
              Consumer(builder: (context, ref, child) {
                return _buildButton("查看详情", onPress: () {
                  if (order?.orderNo != null) {
                    ref
                        .watch(currentOrderProvider.notifier)
                        .setCurrentOrder(order!.orderNo!);
                    Navigator.pushNamed(context, CsRouter.orderDetail);
                  }
                });
              }),
              _buildActiveGift(context),
            ],
          ),
        ],
      ),
    );
  }
}
