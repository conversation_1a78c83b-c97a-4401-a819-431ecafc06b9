import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/goods/history_provider.dart';
import 'package:msmds_platform/app/view/archive/widgets/favorite_item_widget.dart';

import '../../../common/img/icon_addres.dart';
import '../../../common/widgets/appbar/leading.dart';
import '../../../config/constant.dart';
import '../../../widgets/button/gradient_button.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../../widgets/tabbar/rect_tab_indicator.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.archive
/// @ClassName: history_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/27 10:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/27 10:35
/// @UpdateRemark: 更新说明
class HistoryPage extends ConsumerWidget {
  const HistoryPage({super.key});

  Widget _buildTabBar(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: historyTab.length,
      child: Container(
        height: 40.h,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          indicator: RectTabIndicator(
            indicatorSize: 3.h,
            offset: 8,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF666666),
          unselectedLabelStyle:
              TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
          labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
          labelColor: const Color(0xFFFF0E38),
          tabs: historyTab.map((e) => Tab(text: e.name)).toList(),
          onTap: (index) {
            var historyItem = historyTab[index];
            ref.read(historyInfoProvider.notifier).changeTab(historyItem);
          },
        ),
      ),
    );
  }

  // list
  Widget _buildList(WidgetRef ref) {
    return CustomListView(
      onLoadMore: () async {
        ref.read(historyInfoProvider.notifier).loadMore();
      },
      data: ref.watch(
        historyInfoProvider.select((value) => value.hisGoodsList),
      ),
      renderItem: (c, i, o) {
        return FavoriteItemWidget(
          favoriteGoods: o,
          itemType: "history",
        );
      },
      footerState: ref.watch(
        historyInfoProvider.select((value) => value.loadState),
      ),
      empty: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: 97.h),
        child: Column(
          children: [
            Image.network(
              "${Constant.msmdsAliCdn}/appNormal/collectNoneBg.png",
              width: 160.w,
              height: 117.h,
            ),
            Text(
              "暂无浏览足迹",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF9C9C9C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "我的足迹",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        backgroundColor: Colors.white,
        leading: const Leading(),
        actions: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  ref.read(historyManageStateProvider.notifier).changeState();
                },
                child: Row(
                  children: [
                    Image.asset(
                      historyManage,
                      width: 14.w,
                      height: 14.w,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      ref.watch(historyManageStateProvider) ? "完成" : "管理",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(context, ref),
          Expanded(child: _buildList(ref)),
          const DeleteHistoryWidget(),
        ],
      ),
    );
  }
}

class DeleteHistoryWidget extends ConsumerWidget {
  const DeleteHistoryWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var isAllSelect = ref.watch(historyManageAllSelectProvider);
    var state = ref.watch(historyManageStateProvider);
    var hisList = ref.watch(
      historyInfoProvider.select((value) => value.hisGoodsList),
    );
    if (!state || hisList == null || hisList.isEmpty) return const SizedBox();
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 10.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  ref
                      .read(historyManageDeleteProvider.notifier)
                      .selectAll(isAllSelect);
                },
                child: Row(
                  children: [
                    Container(
                      width: 16.w,
                      height: 16.w,
                      margin: EdgeInsets.only(right: 12.w),
                      decoration: BoxDecoration(
                        color: isAllSelect
                            ? const Color(0xFFFB133C)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8.r),
                        border: isAllSelect
                            ? null
                            : Border.all(
                                color: const Color(0xFFFB133C),
                                width: 0.5,
                              ),
                      ),
                      child: isAllSelect
                          ? Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 14.w,
                            )
                          : null,
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      "全选",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
              ),
              GradientButton(
                onPress: () {
                  ref
                      .read(historyManageDeleteProvider.notifier)
                      .deleteHistory();
                },
                padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 6.h),
                border: Border.all(color: const Color(0xFFF93324), width: 1),
                radius: 18.r,
                gradient: const LinearGradient(
                  colors: [
                    Color(0x10F93324),
                    Color(0x10F93324),
                  ],
                ),
                child: Text(
                  "删除",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFFF93324),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).padding.bottom,
          ),
        ],
      ),
    );
  }
}
