import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/goods/favorites_provider.dart';
import 'package:msmds_platform/app/view/archive/widgets/favorite_item_widget.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../../widgets/tabbar/rect_tab_indicator.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package: app.view.archive
/// @ClassName: favorites_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/26 10:57
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/26 10:57
/// @UpdateRemark: 更新说明
class FavoritesPage extends ConsumerWidget {
  const FavoritesPage({super.key});

  Widget _buildTabBar(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: favoriteTab.length,
      child: Container(
        height: 40.h,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          indicator: RectTabIndicator(
            indicatorSize: 3.h,
            offset: 8,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF666666),
          unselectedLabelStyle:
              TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
          labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
          labelColor: const Color(0xFFFF0E38),
          tabs: favoriteTab.map((e) => Tab(text: e.name)).toList(),
          onTap: (index) {
            var favoriteItem = favoriteTab[index];
            ref.read(favoriteInfoProvider.notifier).changeTab(favoriteItem);
          },
        ),
      ),
    );
  }

  // list
  Widget _buildList(WidgetRef ref) {
    return CustomListView(
      onLoadMore: () async {
        ref.read(favoriteInfoProvider.notifier).loadMore();
      },
      data: ref.watch(
        favoriteInfoProvider.select((value) => value.favGoodsList),
      ),
      renderItem: (c, i, o) {
        return FavoriteItemWidget(favoriteGoods: o);
      },
      footerState: ref.watch(
        favoriteInfoProvider.select((value) => value.loadState),
      ),
      empty: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: 97.h),
        child: Column(
          children: [
            Image.network(
              "${Constant.msmdsAliCdn}/appNormal/collectNoneBg.png",
              width: 160.w,
              height: 117.h,
            ),
            Text(
              "暂无收藏商品",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF9C9C9C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "我的收藏",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        backgroundColor: Colors.white,
        leading: const Leading(),
        actions: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  ref.read(favoriteManageStateProvider.notifier).changeState();
                },
                child: Row(
                  children: [
                    Image.asset(
                      historyManage,
                      width: 14.w,
                      height: 14.w,
                    ),
                    SizedBox(width: 3.w),
                    Text(
                      ref.watch(favoriteManageStateProvider) ? "完成" : "管理",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(context, ref),
          Expanded(child: _buildList(ref)),
          const DeleteFavoriteWidget(),
        ],
      ),
    );
  }
}

class DeleteFavoriteWidget extends ConsumerWidget {
  const DeleteFavoriteWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var isAllSelect = ref.watch(favoriteManageAllSelectProvider);
    var state = ref.watch(favoriteManageStateProvider);
    var favList = ref.watch(
      favoriteInfoProvider.select((value) => value.favGoodsList),
    );
    if (!state || favList == null || favList.isEmpty) return const SizedBox();
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 10.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  ref
                      .read(favoriteManageDeleteProvider.notifier)
                      .selectAll(isAllSelect);
                },
                child: Row(
                  children: [
                    Container(
                      width: 16.w,
                      height: 16.w,
                      margin: EdgeInsets.only(right: 12.w),
                      decoration: BoxDecoration(
                        color: isAllSelect
                            ? const Color(0xFFFB133C)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(8.r),
                        border: isAllSelect
                            ? null
                            : Border.all(
                                color: const Color(0xFFFB133C),
                                width: 0.5,
                              ),
                      ),
                      child: isAllSelect
                          ? Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 14.w,
                            )
                          : null,
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      "全选",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
              ),
              GradientButton(
                onPress: () {
                  ref
                      .read(favoriteManageDeleteProvider.notifier)
                      .deleteFavorite();
                },
                padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 6.h),
                border: Border.all(color: const Color(0xFFF93324), width: 1),
                radius: 18.r,
                gradient: const LinearGradient(
                  colors: [
                    Color(0x10F93324),
                    Color(0x10F93324),
                  ],
                ),
                child: Text(
                  "删除",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFFF93324),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).padding.bottom,
          ),
        ],
      ),
    );
  }
}
