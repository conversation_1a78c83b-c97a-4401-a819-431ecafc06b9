import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/archive/favorite_goods.dart';

import '../../../../config/constant.dart';
import '../../../navigation/router.dart';
import '../../../provider/goods/favorites_provider.dart';
import '../../../provider/goods/history_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.archive
/// @ClassName: favorite_item_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/26 15:48
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/26 15:48
/// @UpdateRemark: 更新说明
class FavoriteItemWidget extends ConsumerWidget {
  const FavoriteItemWidget({
    super.key,
    required this.favoriteGoods,
    this.itemType = "favorite",
  });

  final FavoriteGoods favoriteGoods;

  // item类型，favorite：收藏，history：浏览足迹
  final String? itemType;

  String _getPlatformImg() {
    var platLogo = "";
    if (favoriteGoods.goodsType == 6) {
      platLogo = "${Constant.msmdsAliCdn}/detail/logo.png";
    } else if (favoriteGoods.goodsType == 2) {
      platLogo = "${Constant.msmdsAliCdn}/detail/jdlogo.png";
    } else if (favoriteGoods.goodsType == 10) {
      platLogo = "${Constant.msmdsAliCdn}/detail/pddlogo.png";
    } else if (favoriteGoods.goodsType == 21) {
      platLogo = "${Constant.msmdsAliCdn}/APPSHOW/dylogo.png";
    } else if (favoriteGoods.goodsType == 19) {
      platLogo = "${Constant.msmdsAliCdn}/peiZhiBanner/homeIcon_weipinhui.png";
    }
    return platLogo;
  }

  // 商品信息显示
  Widget _buildGoodsInfo() {
    var goodsImg = favoriteGoods.goodsInfo == null
        ? favoriteGoods.goodsImg
        : favoriteGoods.goodsInfo?["goodsImg"];
    var goodsName = favoriteGoods.goodsInfo == null
        ? favoriteGoods.goodsName
        : favoriteGoods.goodsInfo?["goodsName"];
    // 原价
    var originalPrice = "";
    if (itemType == "favorite") {
      originalPrice = "${favoriteGoods.goodsInfo?["priceAfterCoupon"] ?? ""}";
    } else {
      originalPrice = "${favoriteGoods.goodsInfo?["price"] ?? ""}";
    }
    return Row(
      children: [
        Image.network(
          "$goodsImg",
          width: 88.w,
          height: 88.w,
          errorBuilder: (c, o, s) {
            return Container(
              width: 88.w,
              height: 88.w,
              color: Colors.grey.withAlpha(30),
            );
          },
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (goodsName != null)
                Text(
                  "$goodsName",
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF3A3A3A),
                  ),
                ),
              SizedBox(height: 13.h),
              if (favoriteGoods.goodsInfo != null)
                Text.rich(
                  TextSpan(
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: const Color(0xFFFB133C),
                    ),
                    children: [
                      const TextSpan(text: "¥"),
                      TextSpan(
                        text:
                            "${favoriteGoods.goodsInfo?["priceAfterReceive"] ?? ""}",
                        style: TextStyle(
                          fontSize: 16.sp,
                        ),
                      ),
                      WidgetSpan(child: SizedBox(width: 4.w)),
                      const TextSpan(
                        text: "会员到手价",
                        style: TextStyle(
                          color: Color(0xFF9C9C9C),
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 4.h),
              if (favoriteGoods.goodsInfo != null)
                Text.rich(
                  TextSpan(
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: const Color(0xFF9C9C9C),
                    ),
                    children: [
                      WidgetSpan(
                        child: Image.network(
                          _getPlatformImg(),
                          width: 12.w,
                          height: 12.w,
                          errorBuilder: (c, o, s) {
                            return const SizedBox();
                          },
                        ),
                      ),
                      WidgetSpan(child: SizedBox(width: 4.w)),
                      const TextSpan(text: "¥"),
                      TextSpan(text: originalPrice),
                      WidgetSpan(child: SizedBox(width: 4.w)),
                      const TextSpan(text: "商城价"),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  // 收藏或者浏览时间显示
  Widget _buildTimeAndCommission() {
    // 券
    var couponAmount = favoriteGoods.goodsInfo?["couponAmount"];
    // 收藏或者浏览
    var timeTitle = itemType == "favorite" ? "收藏时间" : "浏览时间";
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "$timeTitle：${favoriteGoods.createTime}",
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFF9C9C9C),
          ),
        ),
        if (favoriteGoods.goodsInfo != null)
          Row(
            children: [
              if (couponAmount != null && couponAmount != 0)
                Container(
                  margin: EdgeInsets.only(right: 6.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 2.w,
                    vertical: 1.h,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEA3931),
                    borderRadius: BorderRadius.circular(2.r),
                  ),
                  child: Text(
                    "券$couponAmount",
                    style: TextStyle(
                      fontSize: 9.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 2.w,
                  vertical: 1.h,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF222119),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(6.r),
                    bottomRight: Radius.circular(6.r),
                  ),
                ),
                child: Text(
                  "会员返${favoriteGoods.goodsInfo?["vipCommission"] ?? ""}",
                  style: TextStyle(
                    fontSize: 9.sp,
                    color: const Color(0xFFEACA8E),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 删除列表
    var deleteList = itemType == "favorite"
        ? ref.watch(favoriteManageDeleteProvider)
        : ref.watch(historyManageDeleteProvider);
    // 查看当前item是否在删除列表中
    var isDelete =
        deleteList.indexWhere((element) => element?.id == favoriteGoods.id) !=
            -1;
    var state = itemType == "favorite"
        ? ref.watch(favoriteManageStateProvider)
        : ref.watch(historyManageStateProvider);

    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: InkWell(
        onTap: () {
          if (state) {
            // 编辑点击
            if (itemType == "favorite") {
              ref
                  .read(favoriteManageDeleteProvider.notifier)
                  .addOrCancelGoods(favoriteGoods);
            } else {
              ref
                  .read(historyManageDeleteProvider.notifier)
                  .addOrCancelGoods(favoriteGoods);
            }
          } else {
            // 商品点击
            Navigator.pushNamed(
              context,
              CsRouter.goodsDetailPage,
              arguments: {
                "skuId": favoriteGoods.intactGoodsId,
                "platformType": favoriteGoods.goodsType,
              },
            );
          }
        },
        child: Row(
          children: [
            if (state)
              Container(
                width: 16.w,
                height: 16.w,
                margin: EdgeInsets.only(right: 12.w),
                decoration: BoxDecoration(
                  color:
                      isDelete ? const Color(0xFFFB133C) : Colors.transparent,
                  borderRadius: BorderRadius.circular(8.r),
                  border: isDelete
                      ? null
                      : Border.all(
                          color: const Color(0xFFFB133C),
                          width: 0.5,
                        ),
                ),
                child: isDelete
                    ? Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14.w,
                      )
                    : null,
              ),
            Expanded(
              child: Column(
                children: [
                  _buildGoodsInfo(),
                  SizedBox(height: 6.h),
                  _buildTimeAndCommission(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
