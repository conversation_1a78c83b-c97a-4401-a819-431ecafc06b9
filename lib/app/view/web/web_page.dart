import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/utils/platform_util.dart';
import 'package:url_launcher/url_launcher.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: web_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/21 11:30
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/21 11:30
/// @UpdateRemark: 更新说明
class WebPage extends StatefulWidget {
  const WebPage({
    super.key,
    this.arguments,
  });

  final Object? arguments;

  @override
  WebPageState createState() => WebPageState();
}

class WebPageState extends State<WebPage> {
  late InAppWebViewController _appWebViewController;

  double _progress = 0.0;

  @override
  Widget build(BuildContext context) {
    String title = (widget.arguments as List)[0];
    String url = (widget.arguments as List)[1];
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 44.h,
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        leading: Leading(
          onBack: () async {
            if (await _appWebViewController.canGoBack()) {
              _appWebViewController.goBack();
            } else {
              if (context.mounted) {
                Navigator.pop(context);
              }
            }
          },
        ),
        actions: [
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              margin: EdgeInsets.only(right: 5.w),
              child: Text(
                "关闭",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
          )
        ],
      ),
      body: Column(
        children: [
          _progress / 100 == 1
              ? const SizedBox()
              : LinearProgressIndicator(
                  color: Colors.blue,
                  minHeight: 2.h,
                  value: _progress / 100,
                ),
          Expanded(
            child: InAppWebView(
              onWebViewCreated: (controller) {
                _appWebViewController = controller;
              },
              initialUrlRequest: URLRequest(url: WebUri(url)),
              onLoadStart: (controller, url) async {},
              initialSettings: InAppWebViewSettings(
                userAgent: PlatformUtils.isIOS
                    ? "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
                    : "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
                useShouldOverrideUrlLoading: true,
                clearCache: true,
              ),
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                var uri = navigationAction.request.url!;
                debugPrint("shouldOverrideUrlLoading: $uri");
                if (![
                  "http",
                  "https",
                  "file",
                  "chrome",
                  "data",
                  "javascript",
                  "about"
                ].contains(uri.scheme)) {
                  if (await canLaunchUrl(uri)) {
                    // Launch the App
                    await launchUrl(
                      uri,
                    );
                    // and cancel the request
                    return NavigationActionPolicy.CANCEL;
                  }
                }

                return NavigationActionPolicy.ALLOW;
              },
              onLoadStop: (controller, url) async {},
              onProgressChanged: (controller, progress) {
                debugPrint("onProgressChanged: $progress");
                setState(() {
                  _progress = progress.toDouble();
                });
              },
              onUpdateVisitedHistory: (controller, url, isReload) {},
              onConsoleMessage: (controller, consoleMessage) {},
            ),
          ),
        ],
      ),
    );
  }
}
