import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/search/platform_provider.dart';
import 'package:msmds_platform/app/view/search/widgets/platform_tab_item.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: platform_tab
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 14:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 14:54
/// @UpdateRemark: 更新说明
class PlatformTab extends ConsumerWidget {
  const PlatformTab({
    super.key,
    this.isSearch = true,
  });

  final bool isSearch;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: ref.watch(platformListProvider).length,
      child: Container(
        height: 44.h,
        color: const Color(0xFFF5F5F5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 30.h,
              child: TabBar(
                isScrollable: true,
                indicator: const BoxDecoration(),
                indicatorSize: TabBarIndicatorSize.label,
                labelPadding: EdgeInsets.symmetric(horizontal: 10.w),
                unselectedLabelColor: const Color(0xFF333333),
                labelColor: const Color(0xFFFF0E38),
                unselectedLabelStyle:
                    TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                          fontSize: 18.r,
                        ),
                labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                      fontSize: 14.r,
                    ),
                tabs: ref
                    .watch(platformListProvider)
                    .map(
                      (e) => PlatformTabItem(
                        storeType: ref.watch(
                          platformProvider.select(
                            (value) => value.storeType,
                          ),
                        ),
                        platformData: e,
                      ),
                    )
                    .toList(),
                onTap: (index) {
                  var value = ref.watch(platformListProvider)[index];
                  ref.read(platformProvider.notifier).setCurrentPlatform(
                        value.storeType,
                        isSearch: isSearch,
                      );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
