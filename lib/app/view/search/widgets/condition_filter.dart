import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/search/filter_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: condition_filter
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 14:58
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 14:58
/// @UpdateRemark: 更新说明
class ConditionFilter extends ConsumerWidget {
  const ConditionFilter({super.key});

  /// 排序item
  Widget _buildSortItem(
    WidgetRef ref,
    String name,
    int sortType,
    int currentSortType, {
    bool sort = false,
  }) {
    Color color = sortType == currentSortType
        ? const Color(0xFFFF0E38)
        : const Color(0xFF666666);
    FontWeight fontWeight =
        sortType == currentSortType ? FontWeight.w900 : FontWeight.normal;

    Widget arrow = Container();
    if (sort) {
      var tag =
          ref.watch(searchFilterProvider.select((value) => value.priceTag));
      arrow = Icon(
        tag == 0 || tag == 1
            ? Icons.arrow_drop_up_rounded
            : Icons.arrow_drop_down_rounded,
        color: color,
        size: 16.r,
      );
    }

    return InkWell(
      onTap: () {
        ref.read(searchFilterProvider.notifier).setSortType(sortType);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            name,
            style: TextStyle(
              fontSize: 14.sp,
              color: color,
              fontWeight: fontWeight,
            ),
          ),
          arrow,
        ],
      ),
    );
  }

  /// 是否有券筛选
  Widget _buildCoupon(WidgetRef ref) {
    var hasCoupon =
        ref.watch(searchFilterProvider.select((value) => value.hasCoupon));

    Color color =
        hasCoupon == 1 ? const Color(0xFFFF0E38) : const Color(0xFF666666);

    FontWeight fontWeight =
        hasCoupon == 1 ? FontWeight.w900 : FontWeight.normal;

    Widget check = Container(
      width: 12.w,
      height: 12.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.w),
        border: Border.all(
          color: const Color(0xFF999999),
        ),
      ),
    );

    if (hasCoupon == 1) {
      check = Container(
        width: 12.w,
        height: 12.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.w),
          color: const Color(0xFFFF0E38),
        ),
        child: Icon(
          Icons.check,
          color: Colors.white,
          size: 10.w,
        ),
      );
    }
    return InkWell(
      onTap: () {
        ref.read(searchFilterProvider.notifier).hasCoupon();
      },
      child: Row(
        children: [
          check,
          Padding(padding: EdgeInsets.only(right: 1.w)),
          Text(
            "有券",
            style: TextStyle(
              fontSize: 14.sp,
              color: color,
              fontWeight: fontWeight,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var sortType =
        ref.watch(searchFilterProvider.select((value) => value.sortType));
    return Container(
      height: 44.h,
      color: const Color(0xFFF5F5F5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSortItem(ref, "综合", -1, sortType),
          _buildSortItem(ref, "价格", 2, sortType, sort: true),
          _buildSortItem(ref, "销量", 1, sortType),
          _buildCoupon(ref),
          // Row(
          //   children: [
          //     Text(
          //       "筛选",
          //       style: TextStyle(
          //         fontSize: 14.sp,
          //         color: const Color(0xFF666666),
          //       ),
          //     ),
          //     Container(
          //       width: 12.w,
          //       height: 12.w,
          //       decoration: BoxDecoration(
          //         border: Border.all(
          //           color: const Color(0xFF999999),
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }
}
