import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/conversion/link_conversion_provider.dart';
import 'package:msmds_platform/app/repository/modals/search/search_goods.dart';
import 'package:msmds_platform/utils/router_util.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: goods_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 15:09
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 15:09
/// @UpdateRemark: 更新说明
class GoodsItem extends ConsumerWidget {
  const GoodsItem({
    super.key,
    required this.index,
    required this.goods,
  });

  final int index;

  final SearchGoods? goods;

  /// 商品点击
  void _goodsItemTap(BuildContext context, WidgetRef ref) {
    Navigator.pushNamed(
      context,
      CsRouter.goodsDetailPage,
      arguments: {
        "skuId": goods?.goodsId,
        "platformType": goods?.platformType,
      },
    );
  }

  /// 商品图片
  Widget _buildCover() {
    if (goods?.imgUrl == null) {
      return Container(
        width: 110.w,
        height: 110.w,
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(60),
          borderRadius: BorderRadius.circular(6),
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Image.network(
        "${goods?.imgUrl}",
        width: 110.w,
        height: 110.w,
        fit: BoxFit.contain,
        errorBuilder: (c, o, s) {
          return Container(
            width: 110.w,
            height: 110.w,
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(60),
              borderRadius: BorderRadius.circular(6),
            ),
          );
        },
      ),
    );
  }

  /// 商品标题
  Widget _buildTitle() {
    var tagColor = const Color(0xFFEA3931);
    if (goods?.platformType == GoodsType.dy.type) {
      tagColor = const Color(0xFF333333);
    } else if (goods?.platformType == GoodsType.wph.type) {
      tagColor = const Color(0xFFF02B98);
    }
    return Text.rich(
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      TextSpan(
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF333333),
          height: 1.2,
        ),
        children: [
          const TextSpan(text: ""),
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Container(
              margin: const EdgeInsets.only(right: 4),
              padding: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: LinearGradient(
                  colors: [
                    tagColor,
                    tagColor,
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: Text(
                "${goods?.platformTag}",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          TextSpan(text: "${goods?.title}"),
        ],
      ),
    );
  }

  /// 优惠券返利信息
  Widget _buildCouponInfo() {
    List<Widget> child = [];
    if (goods != null && goods?.coupon != null && goods!.coupon!.isNotEmpty) {
      child.add(
        Container(
          margin: EdgeInsets.only(right: 4.w),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFF93324)),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                decoration: const BoxDecoration(
                  color: Color(0xFFF93324),
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Text(
                  "券",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 3.w)),
              Text(
                "${goods?.coupon}",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFFF93324),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 5.w)),
            ],
          ),
        ),
      );
    }
    child.add(
      Container(
        margin: EdgeInsets.only(right: 4.w),
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFF93324)),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Text(
          "约返${goods?.commission}元",
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFFF93324),
          ),
        ),
      ),
    );
    return Row(
      children: child,
    );
  }

  /// 价格显示
  Widget _buildPrice() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF999999),
        ),
        children: [
          TextSpan(
            text: "${goods?.priceAfterReceive}元",
            style: TextStyle(
              fontSize: 15.sp,
              color: const Color(0xFFF93324),
            ),
          ),
          WidgetSpan(
            child: Padding(
              padding: EdgeInsets.only(right: 4.w),
            ),
          ),
          TextSpan(
            text: "¥${goods?.price}",
            style: const TextStyle(
              decoration: TextDecoration.lineThrough,
              decorationColor: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 店铺信息
  Widget _buildShop() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            "${goods?.platformTag}｜${goods?.shopName}",
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF999999),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Text(
          "已售${goods?.salesVolume}",
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFF999999),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.fromLTRB(12.w, 0, 12.w, 10.h),
      child: Ink(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: InkWell(
          onTap: () {
            RouterUtil.checkLogin(
              context,
              call: () => _goodsItemTap(context, ref),
            );
          },
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCover(),
                Padding(padding: EdgeInsets.only(right: 10.w)),
                Expanded(
                  child: SizedBox(
                    height: 110.w,
                    child: Column(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _buildTitle(),
                              _buildCouponInfo(),
                              _buildPrice(),
                            ],
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 10.h)),
                        _buildShop(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
