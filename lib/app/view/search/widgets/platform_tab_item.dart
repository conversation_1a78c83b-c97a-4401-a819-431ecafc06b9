import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/search/platform_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: platform_tab
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 12:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 12:12
/// @UpdateRemark: 更新说明
class PlatformTabItem extends StatelessWidget {
  const PlatformTabItem({
    super.key,
    required this.platformData,
    required this.storeType,
  });

  final int storeType;
  final PlatformData platformData;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 20.h,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(
        horizontal: 13.w,
      ),
      decoration: BoxDecoration(
        color: platformData.storeType == storeType
            ? const Color(0x15FF0E38)
            : const Color(0xFFECECEC),
        borderRadius: BorderRadius.circular(15.r),
        border: platformData.storeType == storeType
            ? Border.all(color: const Color(0xFFFF0E38))
            : const Border.fromBorderSide(BorderSide.none),
      ),
      child: Row(
        children: [
          Text(
            platformData.name,
            style: TextStyle(fontSize: 12.sp),
          ),
        ],
      ),
    );
  }
}
