import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_header
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 10:18
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 10:18
/// @UpdateRemark: 更新说明
class SearchHeader extends ConsumerStatefulWidget {
  const SearchHeader({super.key});

  @override
  SearchHeaderState createState() => SearchHeaderState();
}

class SearchHeaderState extends ConsumerState<SearchHeader> {
  final TextEditingController _editingController = TextEditingController();

  @override
  void dispose() {
    _editingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 32.h,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                color: const Color(0xFFFF0E38),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(16.h),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Padding(padding: EdgeInsets.only(right: 10.w)),
                      Image.asset(
                        searchIcon,
                        width: 16.w,
                        height: 16.w,
                      ),
                      Padding(padding: EdgeInsets.only(right: 8.w)),
                      Expanded(
                        child: Consumer(
                          builder: (context, ref, child) {
                            return TextField(
                              autofocus: ref.watch(searchAutoFocusProvider),
                              focusNode: ref.watch(searchFocusNodeProvider),
                              controller: _editingController
                                ..text = ref.watch(searchKeywordProvider) ?? "",
                              onTapOutside: (e) {
                                ref.read(searchFocusNodeProvider).unfocus();
                              },
                              onSubmitted: (value) {
                                if (ref.exists(searchKeywordProvider)) {
                                  ref
                                      .read(searchKeywordProvider.notifier)
                                      .setKeyword(value);
                                } else {
                                  ref
                                      .watch(searchKeywordProvider.notifier)
                                      .setKeyword(value);
                                }
                                if (value.isNotEmpty) {
                                  Navigator.pushNamed(context, CsRouter.search);
                                }
                              },
                              textInputAction: TextInputAction.search,
                              decoration: InputDecoration(
                                contentPadding: EdgeInsets.zero,
                                border: const OutlineInputBorder(
                                  borderSide: BorderSide.none,
                                ),
                                hintText: "请输入商品标题或关键词",
                                hintStyle: TextStyle(
                                  fontSize: 12.sp,
                                  color: const Color(0xFF999999),
                                ),
                                suffixIconConstraints: BoxConstraints(
                                  maxWidth: 40.w,
                                  minWidth: 40.w,
                                ),
                                suffixIcon: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        _editingController.clear();
                                      },
                                      child: Image.network(
                                        delete,
                                        width: 16.w,
                                        height: 16.w,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              cursorColor: const Color(0xFFFF0E38),
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: const Color(0xFF333333),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        GradientButton(
          margin: EdgeInsets.symmetric(horizontal: 12.w),
          onPress: () {
            if (ref.exists(searchKeywordProvider)) {
              ref
                  .read(searchKeywordProvider.notifier)
                  .setKeyword(_editingController.text);
            } else {
              ref
                  .watch(searchKeywordProvider.notifier)
                  .setKeyword(_editingController.text);
            }
            if (_editingController.text.isNotEmpty) {
              Navigator.pushNamed(context, CsRouter.search);
            }
          },
          radius: 15.h,
          shadow: false,
          gradient: const LinearGradient(
            colors: [
              Color(0xFFFF3F4E),
              Color(0xFFFA2C1C),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          child: Container(
            width: 63.w,
            height: 30.h,
            alignment: Alignment.center,
            child: Text(
              "搜索",
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
