import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/search/search_history_provider.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/widgets/custom_wrap/extended_warp.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_history
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/20 17:27
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/20 17:27
/// @UpdateRemark: 更新说明

typedef OnItemTap = Function(String value);

class SearchHistory extends ConsumerStatefulWidget {
  const SearchHistory({
    super.key,
    required this.onTap,
  });

  final OnItemTap onTap;

  @override
  SearchHistoryState createState() => SearchHistoryState();
}

class SearchHistoryState extends ConsumerState<SearchHistory> {
  int minLines = 2;
  int maxLines = 2;
  bool isExtended = false;

  /// 搜索历史item
  Widget _buildHistoryItem(String value) {
    return InkWell(
      onTap: () {
        widget.onTap.call(value);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
        ),
        child: Text(
          value,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  /// overflowWidget
  Widget _buildOverflow() {
    double angle = -1.5;
    if (isExtended) {
      angle = 1.5;
    }
    Widget arrow = Transform.rotate(
      angle: angle,
      child: Image.asset(back, width: 12.w, height: 12.w),
    );
    return InkWell(
      onTap: () {
        setState(() {
          isExtended = !isExtended;
          maxLines = isExtended ? 6 : 2;
        });
      },
      child: Container(
        width: 22.w,
        height: 22.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [arrow],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var historyList = ref.watch(searchKeywordHistoryProvider);
    if (historyList?.data == null ||
        (historyList != null &&
            historyList.data != null &&
            historyList.data!.isEmpty)) {
      return Container();
    }
    return ExtendedWrap(
      spacing: 10,
      runSpacing: 10,
      maxLines: maxLines,
      minLines: minLines,
      overflowWidget: _buildOverflow(),
      children: historyList!.data!
          .map(
            (e) => _buildHistoryItem(e.searchTerm ?? ""),
          )
          .toList(),
    );
  }
}
