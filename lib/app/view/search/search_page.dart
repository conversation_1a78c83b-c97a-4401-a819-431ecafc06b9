import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:msmds_platform/app/view/search/widgets/condition_filter.dart';
import 'package:msmds_platform/app/view/search/widgets/goods_item.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/delegate/persistent_builder.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import 'widgets/platform_tab.dart';

/// Copyright (C), 2021-2023, <PERSON>y Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 10:04
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 10:04
/// @UpdateRemark: 更新说明
class SearchPage extends ConsumerStatefulWidget {
  const SearchPage({super.key});

  @override
  SearchPageState createState() => SearchPageState();
}

class SearchPageState extends ConsumerState<SearchPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      ref.read(searchProvider.notifier).search();
    });
  }

  Widget _buildTitle(WidgetRef ref, BuildContext context) {
    return InkWell(
      onTap: () {
        if (Navigator.canPop(context)) {
          ref.read(searchFocusNodeProvider.notifier).requestFocus();
          Navigator.pop(context);
        } else {
          Navigator.pushReplacementNamed(context, CsRouter.searchPre);
        }
      },
      child: Container(
        height: 32.h,
        margin: EdgeInsets.only(right: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: const Color(0xFFFF0E38),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16.h),
        ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Padding(padding: EdgeInsets.only(right: 10.w)),
                  Image.asset(
                    searchIcon,
                    width: 16.w,
                    height: 16.w,
                  ),
                  Padding(padding: EdgeInsets.only(right: 8.w)),
                  Expanded(
                    child: Text(
                      ref.watch(searchKeywordProvider) ?? "",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        debugPrint("onWillPop search page-----");
        ref.read(searchFocusNodeProvider.notifier).requestFocus();
        return true;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: AppBar(
          toolbarHeight: 44.h,
          elevation: 0,
          titleSpacing: 0,
          backgroundColor: const Color(0xFFF5F5F5),
          title: _buildTitle(ref, context),
          leadingWidth: 42.w,
          leading: Leading(
            onBack: () {
              ref.read(searchFocusNodeProvider.notifier).requestFocus();
              Navigator.pop(context);
            },
          ),
        ),
        body: CustomListView(
          sliverHeader: [
            SliverPersistentHeader(
              pinned: false,
              floating: true,
              delegate: PersistentBuilder(
                max: 44.h,
                min: 44.h,
                builder: (_, offset) {
                  return const PlatformTab();
                },
              ),
            ),
            SliverPersistentHeader(
              pinned: true,
              floating: false,
              delegate: PersistentBuilder(
                max: 44.h,
                min: 44.h,
                builder: (_, offset) {
                  return const ConditionFilter();
                },
              ),
            ),
          ],
          data: ref.watch(searchProvider.select((value) => value.goods)),
          onLoadMore: () async {
            ref.read(searchProvider.notifier).loadMore();
          },
          empty: Container(
            padding: EdgeInsets.only(top: 50.h),
            alignment: Alignment.center,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Image.network(
                  searchEmpty,
                  width: 239.w,
                  fit: BoxFit.contain,
                ),
                Positioned(
                  bottom: 15.h,
                  child: Text(
                    "暂无搜索结果",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ),
              ],
            ),
          ),
          footerState: ref.watch(
            searchProvider.select((value) => value.loadState),
          ),
          renderItem: (context, index, item) {
            return GoodsItem(
              index: index,
              goods: item,
            );
          },
        ),
      ),
    );
  }
}
