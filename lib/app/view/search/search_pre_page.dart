import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/search/search_history_provider.dart';
import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:msmds_platform/app/view/search/widgets/search_header.dart';
import 'package:msmds_platform/app/view/search/widgets/search_history.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_pre_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/20 11:36
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/20 11:36
/// @UpdateRemark: 更新说明

class SearchPrePage extends ConsumerStatefulWidget {
  const SearchPrePage({super.key});

  @override
  SearchPrePageState createState() => SearchPrePageState();
}

class SearchPrePageState extends ConsumerState<SearchPrePage> {
  @override
  Widget build(BuildContext context) {
    var historyList = ref.watch(
      searchKeywordHistoryProvider.select((value) => value?.data),
    );
    List<Widget> children = [];

    if (historyList != null && historyList.isNotEmpty) {
      children.add(
        Text(
          "历史搜索",
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w600,
          ),
        ),
      );
      children.add(
        InkWell(
          onTap: () {
            ref.read(searchKeywordHistoryProvider.notifier).cleanHistory();
          },
          child: Image.asset(
            cleanSearchHistory,
            width: 16.w,
            height: 18.h,
            fit: BoxFit.contain,
          ),
        ),
      );
    }

    return WillPopScope(
      onWillPop: () async {
        debugPrint("onWillPop search pre page-----");
        ref.invalidate(searchKeywordProvider);
        return true;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: AppBar(
          toolbarHeight: 44.h,
          elevation: 0,
          titleSpacing: 0,
          backgroundColor: const Color(0xFFF5F5F5),
          title: const SearchHeader(),
          leadingWidth: 42.w,
          leading: Leading(
            onBack: () {
              ref.invalidate(searchKeywordProvider);
              Navigator.pop(context);
            },
          ),
        ),
        body: Column(
          children: [
            // const PlatformTab(
            //   isSearch: false,
            // ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 10.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: children,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      width: MediaQuery.of(context).size.width,
                      child: SearchHistory(
                        onTap: (value) {
                          if (ref.exists(searchKeywordProvider)) {
                            ref
                                .read(searchKeywordProvider.notifier)
                                .setKeyword(value);
                          } else {
                            ref
                                .watch(searchKeywordProvider.notifier)
                                .setKeyword(value);
                          }
                          if (value.isNotEmpty) {
                            Navigator.pushNamed(context, CsRouter.search);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
