import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/account/invite_provider.dart';
import 'package:msmds_platform/app/repository/modals/account/user_fans_item.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../common/img/icon_addres.dart';
import '../../../common/widgets/appbar/leading.dart';
import '../../provider/account/auth_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.inner
/// @ClassName: invite_details_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/25 10:45
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/25 10:45
/// @UpdateRemark: 更新说明
class InviteDetailsPage extends ConsumerWidget {
  const InviteDetailsPage({super.key});

  // 邀请收益明细
  Widget _buildIncomeDetail(WidgetRef ref) {
    var userData = ref.watch(authProvider);
    Widget avatar = Image.asset(
      noLoginAvatar,
      width: 60.w,
      height: 60.w,
    );
    if (userData != null) {
      avatar = ClipRRect(
        borderRadius: BorderRadius.circular(30.w),
        child: Image.network(
          "${userData.data?.avatarUrl}",
          width: 60.w,
          height: 60.w,
        ),
      );
    }

    var vipLevel = userData?.data?.vipLevel ?? -1;
    var isVip = vipLevel >= 0;

    var userFansData = ref.watch(
      userFansInfoListProvider.select((value) => value.userFansData),
    );

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 15.h),
      padding: EdgeInsets.fromLTRB(10.w, 20.h, 10.w, 21.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF202123),
              ),
              children: [
                const TextSpan(text: "您已邀请了"),
                TextSpan(
                  text: "${userFansData?.invitationNum ?? 0}",
                  style: const TextStyle(
                    color: Color(0xFFEA3931),
                  ),
                ),
                const TextSpan(text: "位好友， 已有"),
                TextSpan(
                  text: "${userFansData?.contributionNum ?? 0}",
                  style: const TextStyle(
                    color: Color(0xFFEA3931),
                  ),
                ),
                const TextSpan(text: "位好友在为您贡献收益"),
              ],
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            "(0元购商品不参与返利分成)",
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF9C9C9C),
            ),
          ),
          SizedBox(height: 15.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  avatar,
                  if (isVip)
                    Positioned(
                      top: -6.h,
                      left: -12.w,
                      child: Transform.rotate(
                        angle: 315 * pi / 180,
                        child: Image.network(
                          "${Constant.msmdsAliCdn}/downListCap.png",
                          height: 20.h,
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(width: 20.w),
              Column(
                children: [
                  Text(
                    "累计邀请收益",
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: const Color(0xFF202123),
                    ),
                  ),
                  Text(
                    "${userFansData?.totalIncome ?? 0}",
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: const Color(0xFFF5C531),
                    ),
                  ),
                ],
              )
            ],
          )
        ],
      ),
    );
  }

  // 粉丝列表
  Widget _buildFansList(WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      padding: EdgeInsets.fromLTRB(10.w, 20.h, 10.w, 21.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "排名",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF202123),
                ),
              ),
              Text(
                "邀请好友",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF202123),
                ),
              ),
              Text(
                "贡献收益",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF202123),
                ),
              ),
            ],
          ),
          SizedBox(height: 11.h),
          const Divider(height: 0.5, color: Color(0xFFF5F5F5)),
          Expanded(
            child: CustomListView(
              onLoadMore: () async {
                ref.read(userFansInfoListProvider.notifier).loadMore();
              },
              footerState: ref.watch(
                userFansInfoListProvider.select((value) => value.loadState),
              ),
              data: ref.watch(
                userFansInfoListProvider.select((value) => value.userFansList),
              ),
              renderItem: (context, index, o) {
                return _buildItem(o, index);
              },
              empty: Container(
                alignment: Alignment.center,
                margin: EdgeInsets.only(top: 40.h),
                child: Text(
                  "您还没有邀请好友",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF9a9a9a),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // item
  Widget _buildItem(UserFansItem item, int index) {
    Widget avatar = Image.asset(
      noLoginAvatar,
      width: 40.w,
      height: 40.w,
    );
    if (item.avatarUrl != null && item.avatarUrl!.isNotEmpty) {
      avatar = ClipRRect(
        borderRadius: BorderRadius.circular(20.w),
        child: Image.network(
          "${item.avatarUrl}",
          width: 40.w,
          height: 40.w,
          errorBuilder: (_, o, s) {
            return Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20.w),
              ),
            );
          },
        ),
      );
    }
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: EdgeInsets.only(left: 6.w),
            width: 60.w,
            child: Text(
              "${index + 1}",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF202123),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  avatar,
                  if (item.member == true)
                    Positioned(
                      top: -6.h,
                      left: -7.w,
                      child: Transform.rotate(
                        angle: 315 * pi / 180,
                        child: Image.network(
                          "${Constant.msmdsAliCdn}/downListCap.png",
                          height: 15.h,
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(width: 10.w),
              SizedBox(
                width: 120.w,
                child: Text(
                  "${item.nickname}",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
            ],
          ),
          Container(
            width: 90.w,
            alignment: Alignment.centerRight,
            child: Text(
              "¥${item.contributionAmount}",
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "我的分享收益",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        backgroundColor: Colors.white,
        leading: const Leading(),
        actions: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {},
                child: Text(
                  "活动规则",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
              SizedBox(width: 16.w),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildIncomeDetail(ref),
          Expanded(child: _buildFansList(ref)),
          SizedBox(
            height: MediaQuery.of(context).padding.bottom + 20.h,
          ),
        ],
      ),
    );
  }
}
