import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../provider/config/inner_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.inner
/// @ClassName: suning_shop_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/21 15:36
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/21 15:36
/// @UpdateRemark: 更新说明
class SuNingShopPage extends ConsumerStatefulWidget {
  const SuNingShopPage({super.key});

  @override
  SuNingShopPageState createState() => SuNingShopPageState();
}

class SuNingShopPageState extends ConsumerState<SuNingShopPage> {
  // 返现流程
  Widget _buildSavingSetup() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFFCEBD8),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          children: [
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 67.w,
                  height: 6.h,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFFFFFF),
                        Color(0xFFFFE9D9),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  "返现流程",
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2E3032),
                  ),
                ),
                SizedBox(width: 8.w),
                Container(
                  width: 67.w,
                  height: 6.h,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFFE9D9),
                        Color(0xFFFFFFFF),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 10.h),
            Text(
              "订单签收后最快第8天可获得返现入账",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF7C7C7C),
              ),
            ),
            const SetupWidget(),
          ],
        ),
      ),
    );
  }

  // 返现规则说明
  Widget _buildSavingRule() {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 16.h, 10.w, 16.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFFCEBD8),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          children: [
            SizedBox(height: 18.h),
            Stack(
              alignment: Alignment.center,
              children: [
                Image.network(
                  "${Constant.msmdsAliCdn}/appNormal/wphRulesText.png",
                  width: 200.w,
                  height: 16.h,
                ),
                Text(
                  "返现规则说明",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF2E3032),
                  ),
                ),
              ],
            ),
            SizedBox(height: 15.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Text(
                "1、苏宁易购自营商品下单购买均可获得佣金（无返佣情况除外），最高返利比例为 50 % （实际返现金额以买什么都省消息推送为准）；非苏宁易购自营商品（第三方商家商品）根据商家实际设置的佣金为准，可能存在无返佣的情况，以苏宁易购平台订单信息为准；在苏宁易购H5或小程序下单可能无返现，建议下载苏宁易购app下单；\n2、返现金额根据用户实际下单金额进行计算，使用优惠券／礼品卡／积分、余额等非现金支付的订单，非现金支付的部分不计算返现计算内，税费、运费、赠品不计算返现，实际返现金额以买什么都省消息推送为准；\n3、整单退货和换货的订单整单无返现，申请部分退款和部分换货的订单，返现减少或无返现，实际返现金额以买什么都省消息推送为准；\n4、实际返现金额以买什么都省消息推送为准，若您通过其他电商平台进行购买，则无返现，订单签收后最快第8天可获得返现入账，实际结算时间以订单详情页为准；\n5、苏宁易购自营苹果品牌系列商品、闪拍、拍卖、购买礼品卡、虚拟类商品、茅台、五粮液等类似敏感、紧俏商品、工艺品、时尚饰品、礼品、汽车、线下服务、 S 码商品（ S 码商品是指用户绑定了 S 码，具有优先抢购、专属优惠以及其他附加权益而产生的订单商品）、特例商品（非自营店铺中的荣耀、美的等）、商用用品、二手优品、苏宁广场、苏宁极物、企业账户下单无返现；\n买什么都省用户知悉并确认，因业务发展需要，上述不予计算佣金或影响佣金计算的事项将可能发生变动，并将在本平台以公告形式公布，以最新版本APP规则为准。如有更多疑问，请联系在线客服。",
                style: TextStyle(
                  fontSize: 12.sp,
                  height: 2,
                  color: const Color(0xFF2E3032),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFDC25E),
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "苏宁易购返现",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        backgroundColor: Colors.white,
        leading: const Leading(),
      ),
      body: Column(
        children: [
          Expanded(
            child: CustomScrollView(
              slivers: [
                const SliverToBoxAdapter(
                  child: SwiperWidget(),
                ),
                SliverToBoxAdapter(
                  child: _buildSavingSetup(),
                ),
                SliverToBoxAdapter(
                  child: _buildSavingRule(),
                ),
              ],
            ),
          ),
          Container(
            color: Colors.white,
            child: Column(
              children: [
                SizedBox(height: 10.h),
                GradientButton(
                  onPress: () {
                    ref.read(suNingSavingProvider.notifier).goToSuNing();
                  },
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  margin: EdgeInsets.symmetric(horizontal: 10.w),
                  radius: 25.r,
                  shadow: false,
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFEAD51),
                      Color(0xFFFE6F05),
                    ],
                  ),
                  child: Text(
                    "去苏宁下单拿返现",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(
                  height: MediaQuery.of(context).padding.bottom == 0
                      ? 10.h
                      : MediaQuery.of(context).padding.bottom,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class SwiperWidget extends ConsumerStatefulWidget {
  const SwiperWidget({super.key});

  @override
  SwiperWidgetState createState() => SwiperWidgetState();
}

class SwiperWidgetState extends ConsumerState<SwiperWidget> {
  double? _swiperHeight;

  final pictureUrl = "${Constant.msmdsAliCdn}/appNormal/SNHeard.png";

  @override
  void initState() {
    super.initState();
    _initializeSwiperHeight();
  }

  void _initializeSwiperHeight() async {
    try {
      var result = await getImageDimensions(pictureUrl);
      final aspectRatio = result.width / result.height;
      setState(() {
        _swiperHeight = MediaQuery.of(context).size.width / aspectRatio;
      });
    } catch (e) {
      setState(() {
        _swiperHeight = 100.h;
      });
    }
  }

  // 获取图片尺寸
  Future<Size> getImageDimensions(String url) {
    final ImageProvider provider = NetworkImage(url);

    final Completer<Size> completer = Completer();

    final ImageStreamListener listener = ImageStreamListener(
      (image, synchronousCall) {
        final size = Size(
          image.image.width.toDouble(),
          image.image.height.toDouble(),
        );

        completer.complete(size);
      },
      onError: (e, s) {
        completer.completeError(e, s);
      },
    );

    final ImageStream stream = provider.resolve(ImageConfiguration.empty);

    stream.addListener(listener);

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    if (_swiperHeight != null) {
      return Container(
        width: MediaQuery.of(context).size.width,
        height: _swiperHeight,
        margin: EdgeInsets.only(bottom: 12.h),
        child: Image.network(
          pictureUrl,
          fit: BoxFit.contain,
          errorBuilder: (c, o, s) {
            return Container();
          },
        ),
      );
    }
    return Container();
  }
}

// 返现流程
class SetupWidget extends StatelessWidget {
  const SetupWidget({super.key});

  Widget _buildItem(String img, String title) {
    return Column(
      children: [
        Image.network(img, width: 43.w, height: 43.w),
        SizedBox(height: 10.h),
        Text(
          title,
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFFFF7D33),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _segmentation() {
    return Padding(
      padding: EdgeInsets.only(bottom: 18.h),
      child: Wrap(
        spacing: 2.w,
        children: List.generate(
          4,
          (index) => Container(
            width: 4.w,
            height: 4.w,
            decoration: BoxDecoration(
              color: const Color(0xFFFF008A),
              borderRadius: BorderRadius.circular(2.w),
            ),
          ),
        ).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      margin: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildItem("${Constant.msmdsAliCdn}/appNormal/SN1.png", "下单购买"),
          _segmentation(),
          _buildItem("${Constant.msmdsAliCdn}/appNormal/SN2.png", "确认收货"),
          _segmentation(),
          _buildItem("${Constant.msmdsAliCdn}/appNormal/SN3.png", "返现入账"),
          _segmentation(),
          _buildItem("${Constant.msmdsAliCdn}/appNormal/SN4.png", "提取现金"),
        ],
      ),
    );
  }
}
