import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/inner_provider.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/repository/modals/account/red_packet.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../../widgets/tabbar/rect_tab_indicator.dart';
import '../../navigation/router.dart';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package: app.view.inner
/// @ClassName: red_packets_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/22 14:12
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/22 14:12
/// @UpdateRemark: 更新说明
class RedPacketsPage extends ConsumerWidget {
  const RedPacketsPage({super.key});

  Widget _buildTabBar(BuildContext context, WidgetRef ref) {
    return DefaultTabController(
      length: giftTabs.length,
      child: Container(
        height: 40.h,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          indicator: RectTabIndicator(
            indicatorSize: 3.h,
            offset: 8,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF666666),
          unselectedLabelStyle:
              TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
          labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
          labelColor: const Color(0xFFFF0E38),
          tabs: giftTabs.map((e) => Tab(text: e.name)).toList(),
          onTap: (index) {
            var giftItem = giftTabs[index];
            ref
                .read(redPacketsListProvider.notifier)
                .changeGiftStatus(giftItem);
          },
        ),
      ),
    );
  }

  // 红包item
  Widget _buildItem(BuildContext context, WidgetRef ref, RedPacket redPacket) {
    var status = ref.read(
      redPacketsListProvider.select((value) => value.giftItem?.status),
    );
    String? img;
    if (status == 1) {
      img = "${Constant.msmdsAliCdn}/appNormal/hongbaoJihuo.png";
    } else if (status == 4) {
      img = "${Constant.msmdsAliCdn}/appNormal/hongbaoGuoqi.png";
    } else if (status == 3) {
      img = "${Constant.msmdsAliCdn}/appNormal/hongbaoUsed.png";
    }
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
      child: Row(
        children: [
          Expanded(
            child: ClipPath(
              clipper: const QuarterCircleRightClipper(circleSize: 5),
              child: Container(
                height: 85.h,
                padding: EdgeInsets.fromLTRB(12.w, 10.h, 0, 10.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.r),
                    bottomLeft: Radius.circular(8.r),
                  ),
                ),
                child: Stack(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${redPacket.useType == 1 ? "提现红包" : redPacket.typeDesc}",
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                fontSize: 13.sp,
                                color: const Color(0xFF3A3A3A),
                              ),
                            ),
                            if (redPacket.moneyLimit == 0)
                              Padding(
                                padding: EdgeInsets.only(top: 4.h),
                                child: Text(
                                  "下单即激活返利红包",
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: const Color(0xFF3A3A3A),
                                  ),
                                ),
                              ),
                            if (redPacket.moneyLimit != 0 &&
                                redPacket.useType == 2)
                              Padding(
                                padding: EdgeInsets.only(top: 4.h),
                                child: Text(
                                  "每单返利满${(redPacket.moneyLimit ?? 0) / 100}元可用",
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: const Color(0xFF3A3A3A),
                                  ),
                                ),
                              ),
                            if (redPacket.moneyLimit != 0 &&
                                redPacket.useType == 1)
                              Padding(
                                padding: EdgeInsets.only(top: 4.h),
                                child: Text(
                                  "单次提现满${(redPacket.moneyLimit ?? 0) / 100}元可用",
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: const Color(0xFF3A3A3A),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Text(
                          (status == 1 || status == 3) &&
                                  redPacket.orderNo != null
                              ? "订单号:${redPacket.orderNo}"
                              : "有效期至:${redPacket.endTime}",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: const Color(0xFFA3A3A3),
                          ),
                        ),
                      ],
                    ),
                    if (img != null)
                      Positioned(
                        right: -6.w,
                        top: 0,
                        bottom: 0,
                        child: Image.network(
                          img,
                          width: 50.w,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          ClipPath(
            clipper: const QuarterCircleLeftClipper(circleSize: 5),
            child: Container(
              width: 88.w,
              height: 85.h,
              decoration: BoxDecoration(
                color: status == 0
                    ? const Color(0xFFFB133C)
                    : const Color(0xFFC1C2C3),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontSize: 22.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      children: [
                        TextSpan(
                          text: "¥ ",
                          style: TextStyle(
                            fontSize: 14.sp,
                          ),
                        ),
                        TextSpan(text: "${(redPacket.money ?? 0) / 100}"),
                      ],
                    ),
                  ),
                  if (status == 0)
                    SizedBox(
                      width: 52.w,
                      child: GradientButton(
                        onPress: () {
                          Navigator.popUntil(
                            context,
                            (route) =>
                                route.settings.name == CsRouter.home ||
                                route.settings.name == "/",
                          );
                          ref.read(homeProvider.notifier).jumpToPage(0);
                        },
                        radius: 8.r,
                        padding: EdgeInsets.symmetric(vertical: 2.h),
                        margin: EdgeInsets.only(top: 10.h),
                        gradient: const LinearGradient(
                          colors: [
                            Colors.white,
                            Colors.white,
                          ],
                        ),
                        child: Text(
                          "去使用",
                          style: TextStyle(
                            fontSize: 8.sp,
                            color: const Color(0xFFFB133C),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // empty
  Widget _buildEmpty(WidgetRef ref) {
    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.only(top: 160.h),
      child: Text(
        "暂无红包",
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFF9C9C9C),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          "我的红包",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
        backgroundColor: Colors.white,
        leading: const Leading(),
        // actions: [
        //   Row(
        //     crossAxisAlignment: CrossAxisAlignment.center,
        //     children: [
        //       InkWell(
        //         onTap: () {},
        //         child: Text(
        //           "使用说明",
        //           style: TextStyle(
        //             fontSize: 12.sp,
        //             color: const Color(0xFF333333),
        //           ),
        //         ),
        //       ),
        //       SizedBox(width: 16.w),
        //     ],
        //   ),
        // ],
      ),
      body: Column(
        children: [
          _buildTabBar(context, ref),
          Expanded(
            child: CustomListView(
              onLoadMore: () async {
                ref.read(redPacketsListProvider.notifier).loadMore();
              },
              renderItem: (context, index, o) {
                return _buildItem(context, ref, o);
              },
              data: ref.watch(
                redPacketsListProvider.select((value) => value.redPacketList),
              ),
              footerState: ref.watch(
                redPacketsListProvider.select((value) => value.loadState),
              ),
              empty: _buildEmpty(ref),
            ),
          ),
        ],
      ),
    );
  }
}

class QuarterCircleRightClipper extends CustomClipper<Path> {
  const QuarterCircleRightClipper({
    this.circleSize = 10,
  });

  final double circleSize;

  @override
  Path getClip(Size size) {
    final path = Path();

    // 定义左上角和左下角
    path.moveTo(0, 0);
    path.lineTo(0, size.height);

    // 定义右下角的四分之一圆
    path.lineTo(size.width - circleSize, size.height);
    path.arcToPoint(
      Offset(size.width, size.height - circleSize),
      radius: Radius.circular(circleSize),
    );

    // 定义右上角的四分之一圆
    path.lineTo(size.width, circleSize);
    path.arcToPoint(
      Offset(size.width - circleSize, 0),
      radius: Radius.circular(circleSize),
    );

    // 闭合路径
    path.close();

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}

class QuarterCircleLeftClipper extends CustomClipper<Path> {
  const QuarterCircleLeftClipper({
    this.circleSize = 10,
  });

  final double circleSize;

  @override
  Path getClip(Size size) {
    final path = Path();

    path.moveTo(circleSize, 0);
    path.arcToPoint(
      Offset(0, circleSize),
      radius: Radius.circular(circleSize),
    );

    path.lineTo(0, size.height - circleSize);
    path.arcToPoint(
      Offset(circleSize, size.height),
      radius: Radius.circular(circleSize),
    );

    path.lineTo(size.width, size.height);
    path.lineTo(size.width, 0);

    // 闭合路径
    path.close();

    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}
