import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../../../widgets/button/gradient_button.dart';
import 'confirm_withdrawal_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.withdrawal.dialog
/// @ClassName: amount_input_dialog
/// @Description: 自定义输入提现金额
/// @Author: frankylee
/// @CreateDate: 2024/9/13 11:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/13 11:23
/// @UpdateRemark: 更新说明
class AmountInputDialog {
  /// 自定义输入提现金额
  static void showDialog() {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "amount_input_dialog",
      builder: (context) {
        return AnimatedPadding(
          padding: MediaQuery.of(context).viewInsets,
          duration: const Duration(milliseconds: 100),
          child: const AmountCustomWidget(),
        );
      },
    );
  }
}

class AmountCustomWidget extends ConsumerStatefulWidget {
  const AmountCustomWidget({super.key});

  @override
  AmountCustomWidgetState createState() => AmountCustomWidgetState();
}

class AmountCustomWidgetState extends ConsumerState<AmountCustomWidget> {
  final TextEditingController _amountEditingController =
      TextEditingController();

  @override
  void dispose() {
    _amountEditingController.dispose();
    super.dispose();
  }

  Widget _amountEdit() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(6),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: TextField(
              controller: _amountEditingController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                MoneyInputFormatter(),  // 限制输入金额格式
              ],
              style: TextStyle(fontSize: 14.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                counterText: "",
                hintStyle: TextStyle(
                  color: const Color(0xFFB4B4B4),
                  fontSize: 14.sp,
                ),
                hintText: "请输入提现金额",
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 295.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.fromLTRB(0, 10.h, 10.w, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      child: Icon(
                        Icons.close_rounded,
                        size: 16.r,
                        color: const Color(0xFFC8C8C8),
                      ),
                      onTap: () {
                        SmartDialog.dismiss(tag: "amount_input_dialog");
                      },
                    )
                  ],
                ),
              ),
              Text(
                "自定义金额提现",
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF202123),
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 16.h)),
              _amountEdit(),
              Padding(padding: EdgeInsets.only(bottom: 18.h)),
              GradientButton(
                onPress: () {
                  var amount = _amountEditingController.text;
                  if (amount.isNotEmpty) {
                    SmartDialog.dismiss(tag: "amount_input_dialog");
                    ConfirmWithdrawalDialog.confirmWithdrawalDialog(amount: amount);
                  }
                },
                margin: EdgeInsets.symmetric(horizontal: 15.w),
                padding: EdgeInsets.symmetric(vertical: 10.h),
                radius: 25,
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFE5640),
                    Color(0xFFFA2E1B),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                child: Text(
                  "确定",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              Text(
                "指定金额提现将额外得福利",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFF9C9C9C),
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 10.h)),
            ],
          ),
        ),
      ],
    );
  }
}

class MoneyInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 如果输入为空，允许删除
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 正则表达式：确保整数部分不以多个前导0开头，且最多两位小数
    final RegExp regExp = RegExp(r'^(0|[1-9]\d*)?(\.\d{0,2})?$');

    // 检查是否符合金额格式
    if (regExp.hasMatch(newValue.text)) {
      // 确保首字符不是"0"（除非输入"0"或"0."的合法形式）
      if (newValue.text.startsWith('0') && newValue.text.length > 1 && newValue.text[1] != '.') {
        return oldValue; // 不允许以"0"开头后跟非小数点字符
      }
      return newValue;
    }

    // 如果不符合正则表达式规则，则返回旧值
    return oldValue;
  }
}