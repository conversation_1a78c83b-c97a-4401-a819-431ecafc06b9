import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: confirm_withdrawal_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 17:37
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 17:37
/// @UpdateRemark: 更新说明
class ConfirmWithdrawalDialog {
  /// 绑定提现支付宝
  static void confirmWithdrawalDialog({String? amount}) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "confirm_withdrawal_dialog",
      builder: (context) {
        return WithdrawalWidget(
          amount: amount,
        );
      },
    );
  }
}

class WithdrawalWidget extends ConsumerWidget {
  const WithdrawalWidget({
    super.key,
    this.amount,
  });

  final String? amount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 296.w,
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 23.h, bottom: 19.h),
                child: Text(
                  "确认提现",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                "即将提现到",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 5.h)),
              Text(
                "支付宝：${ref.watch(alipayAccountProvider.select(
                  (value) => value?.phone ?? "",
                ))}",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF666666),
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 18.h)),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "confirm_withdrawal_dialog");
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 44.w,
                        vertical: 9.h,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: const Color(0xFFCCCCCC),
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        "取消",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                    ),
                  ),
                  GradientButton(
                    padding: EdgeInsets.symmetric(
                      horizontal: 44.w,
                      vertical: 9.h,
                    ),
                    onPress: () {
                      SmartDialog.dismiss(tag: "confirm_withdrawal_dialog");

                      if (amount != null) {
                        // 自定义提现金额
                        ref
                            .watch(initiateWithdrawalProvider.notifier)
                            .customInitiate(amount!);
                      } else {
                        // 固定提现金额
                        ref
                            .watch(initiateWithdrawalProvider.notifier)
                            .initiate();
                      }
                    },
                    shadow: false,
                    radius: 20,
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFFFE5640),
                        Color(0xFFFA2E1B),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    child: Text(
                      "确认",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.white,
                      ),
                    ),
                  )
                ],
              ),
              Padding(padding: EdgeInsets.only(bottom: 30.h)),
            ],
          ),
        ),
      ],
    );
  }
}
