import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: bind_alipay_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 14:21
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 14:21
/// @UpdateRemark: 更新说明
class BindAlipayDialog {
  /// 绑定提现支付宝
  static void showBindAlipayDialog() {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "bind_alipay_dialog",
      builder: (context) {
        return AnimatedPadding(
          padding: MediaQuery.of(context).viewInsets,
          duration: const Duration(milliseconds: 100),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 295.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Form(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.fromLTRB(0, 10.h, 10.w, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            InkWell(
                              child: Icon(
                                Icons.close_rounded,
                                size: 16.r,
                                color: const Color(0xFFC8C8C8),
                              ),
                              onTap: () {
                                SmartDialog.dismiss(tag: "bind_alipay_dialog");
                              },
                            )
                          ],
                        ),
                      ),
                      Text(
                        "填写收款支付宝账号",
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      Padding(padding: EdgeInsets.only(bottom: 26.h)),
                      const AlipayAccount(),
                      Padding(padding: EdgeInsets.only(bottom: 18.h)),
                      const AlipayName(),
                      Padding(padding: EdgeInsets.only(bottom: 27.h)),
                      Consumer(
                        builder: (context, ref, child) {
                          return GradientButton(
                            onPress: () {
                              if (Form.of(context).validate()) {
                                /// 验证通过提交数据
                                Form.of(context).save();
                                ref
                                    .read(bindAlipayAccountProvider.notifier)
                                    .bindWithdrawal();
                              }
                            },
                            margin: EdgeInsets.symmetric(horizontal: 28.w),
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            radius: 25,
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xFFFE5640),
                                Color(0xFFFA2E1B),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            child: Text(
                              "确定",
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.white,
                              ),
                            ),
                          );
                        },
                      ),
                      Padding(padding: EdgeInsets.only(bottom: 20.h)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 支付宝账户输入
class AlipayAccount extends ConsumerWidget {
  const AlipayAccount({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "支付宝账号：",
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF333333),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 10.h)),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(6),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: TextFormField(
              // controller: _editingController,
              onSaved: (value) {
                debugPrint("支付宝账号: $value");
                ref
                    .watch(alipayAccountEditingProvider.notifier)
                    .setAlipayNo(value);
              },
              style: TextStyle(fontSize: 14.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                counterText: "",
                hintStyle: TextStyle(
                  color: const Color(0xFFB4B4B4),
                  fontSize: 14.sp,
                ),
                hintText: "请输入（收款支付宝账号）",
              ),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 4.h)),
          Text(
            "提醒：一旦提现成功，设置的支付宝账户不可修改",
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF9C9C9C),
            ),
          ),
        ],
      ),
    );
  }
}

/// 支付宝姓名输入
class AlipayName extends ConsumerWidget {
  const AlipayName({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "账号真实姓名：",
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF333333),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 10.h)),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(6),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: TextFormField(
              // controller: _editingController,
              onSaved: (value) {
                debugPrint("账号真实姓名: $value");
                ref
                    .watch(alipayAccountNameEditingProvider.notifier)
                    .setAlipayName(value);
              },
              style: TextStyle(fontSize: 14.sp),
              decoration: InputDecoration(
                border: InputBorder.none,
                counterText: "",
                hintStyle: TextStyle(
                  color: const Color(0xFFB4B4B4),
                  fontSize: 14.sp,
                ),
                hintText: "请输入（该支付宝对应的真实姓名）",
              ),
            ),
          ),
        ],
      ),
    );
  }
}
