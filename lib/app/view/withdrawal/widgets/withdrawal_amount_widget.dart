import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';

import '../../../repository/modals/bill/withdrawal_amount_item.dart';
import '../dialog/amount_input_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.withdrawal.widgets
/// @ClassName: withdrawal_amount_widget
/// @Description: 提现金额选择
/// @Author: frankylee
/// @CreateDate: 2024/9/12 17:26
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/12 17:26
/// @UpdateRemark: 更新说明
class WithdrawalAmountWidget extends ConsumerWidget {
  const WithdrawalAmountWidget({super.key});

  Widget _buildItem(
    BuildContext context,
    WithdrawalAmountItem item,
    WidgetRef ref,
  ) {
    var currentSelectItem = ref.watch(currentSelectWithdrawAmountProvider);
    bool select = currentSelectItem?.withdrawType == item.withdrawType;
    // 金额显示
    Widget moneyWidget;
    if (item.withdrawType != "0") {
      var money = int.tryParse(item.money ?? "") ?? 0;
      moneyWidget = Text(
        "${money ~/ 100}元",
        style: TextStyle(
          fontSize: 14.sp,
          color: select ? const Color(0xFFFB133C) : const Color(0xFF3A3A3A),
        ),
      );
    } else {
      moneyWidget = Text(
        "${item.desc}",
        style: TextStyle(
          fontSize: 14.sp,
          color: select ? const Color(0xFFFB133C) : const Color(0xFF3A3A3A),
        ),
      );
    }

    // tag显示
    Widget tagWidget = const SizedBox();
    if (item.bgImg != null) {
      tagWidget = Container(
        width: 70.w,
        height: 15.h,
        alignment: Alignment.topCenter,
        decoration: BoxDecoration(
          image: DecorationImage(image: NetworkImage(item.bgImg!)),
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 1),
          child: Text(
            "${item.desc}",
            style: TextStyle(
              fontSize: 8.sp,
              color: Colors.white,
            ),
          ),
        ),
      );
    }

    return InkWell(
      onTap: () {
        ref
            .read(currentSelectWithdrawAmountProvider.notifier)
            .setWithdrawalAmount(item);
        if (item.withdrawType == "0") {
          // 显示自定义金额弹窗
          AmountInputDialog.showDialog();
        }
      },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            width: ((MediaQuery.of(context).size.width - 68.w) / 3).floorToDouble(),
            height: 50.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: select ? const Color(0x0DFB133C) : const Color(0xFFFFFFFF),
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color:
                    select ? const Color(0xFFFF6E88) : const Color(0xFF979797),
                width: 0.5,
              ),
            ),
            child: moneyWidget,
          ),
          Positioned(top: -6.h, right: 0, child: tagWidget),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchWithdrawAmountListProvider).when(
      data: (data) {
        if (data == null || data.data == null) {
          return const SizedBox();
        }
        return Container(
          margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
          padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 16.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "提现金额",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF3A3A3A),
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(
                height: 12.h,
              ),
              Wrap(
                spacing: 10.w,
                runSpacing: 10.h,
                children:
                    data.data!.map((e) => _buildItem(context, e, ref)).toList(),
              ),
              SizedBox(
                height: 16.h,
              ),
              Text(
                "提醒：同一用户每天只能提现一次",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF9C9C9C),
                ),
              ),
            ],
          ),
        );
      },
      error: (o, s) {
        return const SizedBox();
      },
      loading: () {
        return const SizedBox();
      },
    );
  }
}
