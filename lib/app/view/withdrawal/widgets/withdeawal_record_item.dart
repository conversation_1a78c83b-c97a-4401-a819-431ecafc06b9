import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../repository/modals/bill/withdrawn_bill.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdeawal_record_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/14 10:24
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/14 10:24
/// @UpdateRemark: 更新说明
class WithdrawalRecordItem extends StatelessWidget {
  const WithdrawalRecordItem({
    super.key,
    required this.record,
  });

  final WithdrawnBill? record;

  /// 标题
  Widget _buildTitle() {
    return Row(
      children: [
        Text(
          "提现到${record?.withdrawType == 2 ? "支付宝" : "微信零钱"}",
          style: TextStyle(
            fontSize: 15.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.h, 0),
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitle(),
                Padding(padding: EdgeInsets.only(bottom: 5.h)),
                if (record?.createTime != null)
                  Text(
                    "提现时间：${record?.createTime ?? ""}",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
              ],
            ),
          ),
          Padding(padding: EdgeInsets.only(left: 10.w)),
          Text(
            "¥${record?.amount}",
            style: TextStyle(
              fontSize: 18.sp,
              color: const Color(0xFFFF0E38),
              fontWeight: FontWeight.w400,
            ),
          )
        ],
      ),
    );
  }
}
