import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: withdrawal_desc
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 12:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 12:06
/// @UpdateRemark: 更新说明

final List<String> descList = [
  "1.提现将在2个工作日内审核到账，如遇高峰期，可能会延迟到账，请耐心等候，到账进度请到提现页面查询，每次提现不能超过200元；",
  "2.同一支付宝账户、同一设备、同一手机号，满足上述任一条件均视为同一账户，同一账户一天只能提现一次，一个支付宝账户只能绑定一个买什么都省账号；",
  "3.支付宝账户一旦设置，不可修改。支付宝账户仅供买什么都省向用户支付可提现金额可用，不会涉及您的资金。买什么都省客服不会向您索要支付宝密码，索要密码的都是骗子，请勿泄露密码。买什么都省不会向第三方平台泄露您的支付宝账号及真实姓名；",
  "4.对于以下行为包括但不限于：虚假交易、违规交易等，平台将判定用户为恶意用户，平台有权采取适当方式规范用户行为，包括但不限于：冻结用户账号，追回违规所得；",
  "5.提现金额通过支付宝的方式转账给您，因此需要绑定到账的支付宝账户和对应支付宝账户认证的真实姓名；",
  "6.支付宝到账查询：打开支付宝—我的—账单，明细有”买什么都省提现“的记录，即为成功提现；",
  "7.如有任何问题，可在\"我的一客服中心\"咨询人工客服，或拨打联系电话：020-87577697，工作时间为：工作日9:30-12:30，14:00-18:30",
];

class WithdrawalDescWidget extends StatelessWidget {
  const WithdrawalDescWidget({super.key});

  Widget _buildItem(String value) {
    return Column(
      children: [
        Padding(padding: EdgeInsets.only(top: 6.h)),
        Text(
          value,
          style: TextStyle(
            fontSize: 12.sp,
            height: 1.7,
            color: const Color(0xFF999999),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [
      Text(
        "提现说明",
        style: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
      ),
    ];
    descList.map((e) => children.add(_buildItem(e))).toList();
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }
}
