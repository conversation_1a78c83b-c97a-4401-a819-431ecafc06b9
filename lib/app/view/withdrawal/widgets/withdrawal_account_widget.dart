import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/app/view/withdrawal/dialog/bind_alipay_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_account
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 11:41
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 11:41
/// @UpdateRemark: 更新说明
class WithdrawalAccountWidget extends StatelessWidget {
  const WithdrawalAccountWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 22.h, 10.w, 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.fromLTRB(14.w, 14.h, 0, 16.h),
            child: Text(
              "提现到支付宝",
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF333333),
              ),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 14.w, right: 6.w),
                child: Image.network(
                  alipayIcon,
                  width: 20.w,
                  height: 20.w,
                ),
              ),
              Expanded(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          "支付宝",
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF333333),
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(right: 24.w)),
                        Consumer(
                          builder: (context, ref, child) {
                            return Text(
                              ref.watch(alipayAccountProvider.select(
                                (value) => value?.phone ?? "",
                              )),
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: const Color(0xFF333333),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 20.h)),
                    Consumer(
                      builder: (context, ref, child) {
                        var bind = ref.watch(alipayAccountProvider);
                        if (bind != null) {
                          return Container();
                        }
                        return Padding(
                          padding: EdgeInsets.only(bottom: 20.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "暂未绑定支付宝",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: const Color(0xFF999999),
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  BindAlipayDialog.showBindAlipayDialog();
                                },
                                child: Container(
                                  margin: EdgeInsets.only(right: 30.w),
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 18.w,
                                    vertical: 1.h,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: const Color(0xFFFF0E38),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    "绑定",
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      color: const Color(0xFFFE1D44),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
