import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/app/view/withdrawal/widgets/withdeawal_record_item.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_record_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 18:31
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 18:31
/// @UpdateRemark: 更新说明
class WithdrawalRecordPage extends ConsumerStatefulWidget {
  const WithdrawalRecordPage({super.key});

  @override
  WithdrawalRecordPageState createState() => WithdrawalRecordPageState();
}

class WithdrawalRecordPageState extends ConsumerState<WithdrawalRecordPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          "提现记录",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: const Leading(),
      ),
      body: CustomListView(
        onRefresh: () async {
          ref.read(withdrawalRecordListProvider.notifier).loadData();
        },
        onLoadMore: () async {
          ref.read(withdrawalRecordListProvider.notifier).loadMore();
        },
        data: ref.watch(
          withdrawalRecordListProvider.select((value) => value.recordList),
        ),
        renderItem: (context, index, item) {
          return WithdrawalRecordItem(record: item);
        },
        footerState: ref.watch(
          withdrawalRecordListProvider.select((value) => value.loadState),
        ),
        empty: Container(
          margin: EdgeInsets.only(top: 83.h),
          alignment: Alignment.center,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              Image.network(
                withdrawalEmpty,
                width: 239.w,
                fit: BoxFit.contain,
              ),
              Positioned(
                bottom: 10.h,
                child: Text(
                  "暂无提现记录",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
