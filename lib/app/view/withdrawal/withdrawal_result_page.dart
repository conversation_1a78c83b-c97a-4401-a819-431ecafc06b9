import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_result_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 17:57
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 17:57
/// @UpdateRemark: 更新说明
class WithdrawalResultPage extends ConsumerWidget {
  const WithdrawalResultPage({super.key});

  // 提现金额显示
  String _showAmount(String? value) {
    debugPrint("WithdrawalResultPage: $value");
    if (value == null) return "¥0";
    var amount = num.tryParse(value);
    if (amount == null) return "¥0";
    return "¥${(amount / 100).toString()}";
  }

  Widget _buildImageStatus(WidgetRef ref) {
    var status = ref.watch(withdrawalStatusProvider.select(
      (value) => value?.status ?? false,
    ));
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.only(top: 40.h, bottom: 12.h),
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Image.network(
            status ? withdrawalSuccess : withdrawalFail,
            width: 120.w,
            height: 111.h,
          ),
          Text(
            status ? "提现成功" : "提现失败",
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  /// 失败原因
  Widget _buildFailMessage(WidgetRef ref) {
    var status = ref.watch(withdrawalStatusProvider.select(
      (value) => value?.status ?? false,
    ));
    if (status) {
      return Container();
    }
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFCF7DC),
        borderRadius: BorderRadius.circular(7),
      ),
      margin: EdgeInsets.fromLTRB(14.w, 31.h, 14.w, 0),
      padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 7.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.network(
                withdrawalFailTap,
                width: 14.w,
                height: 14.w,
              ),
              Padding(padding: EdgeInsets.only(right: 3.w)),
              Text(
                "失败原因：",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFFEB7623),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Padding(padding: EdgeInsets.only(bottom: 6.h)),
          Text(
            ref.watch(
              withdrawalStatusProvider.select((value) => value?.msg ?? ""),
            ),
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFFEB7623),
            ),
          ),
        ],
      ),
    );
  }

  /// 完成/提现记录
  Widget _buildConfirm(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.only(top: 34.h, bottom: 52.h),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 104.w),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFF999999), width: 1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Text(
                "完成",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF333333),
                ),
              ),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 10.h)),
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, CsRouter.withdrawalRecord);
            },
            child: Text(
              "提现记录",
              style: TextStyle(
                fontSize: 14.sp,
                decoration: TextDecoration.underline,
                decorationColor: const Color(0xFF666666),
                color: const Color(0xFF666666),
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          "余额提现",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: const Leading(),
      ),
      body: Container(
        margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildImageStatus(ref),
            Text(
              _showAmount("${ref.watch(
                withdrawalStatusProvider
                    .select((value) => value?.userWithdraw?.money),
              )}"),
              style: TextStyle(
                fontSize: 30.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.w600,
              ),
            ),
            _buildFailMessage(ref),
            _buildConfirm(context, ref),
          ],
        ),
      ),
    );
  }
}
