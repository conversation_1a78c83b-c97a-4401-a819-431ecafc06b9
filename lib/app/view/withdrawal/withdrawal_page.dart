import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/app/view/withdrawal/dialog/bind_alipay_dialog.dart';
import 'package:msmds_platform/app/view/withdrawal/dialog/confirm_withdrawal_dialog.dart';
import 'package:msmds_platform/app/view/withdrawal/widgets/withdrawal_account_widget.dart';
import 'package:msmds_platform/app/view/withdrawal/widgets/withdrawal_amount_widget.dart';
import 'package:msmds_platform/app/view/withdrawal/widgets/withdrawal_desc_widget.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/common/widgets/back/back_widget.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import 'dialog/amount_input_dialog.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 11:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 11:06
/// @UpdateRemark: 更新说明
class WithdrawalPage extends StatefulWidget {
  const WithdrawalPage({super.key});

  @override
  WithdrawalPageState createState() => WithdrawalPageState();
}

class WithdrawalPageState extends State<WithdrawalPage> {
  late ScrollController _scrollController;

  final ValueNotifier<int> _appBarAlpha = ValueNotifier(0);

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      _appBarAlpha.value = _scrollController.offset.toInt().clamp(0, 255);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// appbar
  SliverAppBar _buildAppBar(BuildContext context, int alpha) {
    return SliverAppBar(
      pinned: true,
      centerTitle: true,
      toolbarHeight: 44.h,
      backgroundColor: const Color(0xFFFF0000).withAlpha(alpha),
      elevation: 0,
      title: Text(
        "余额提现",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      leadingWidth: 42.w,
      leading: const Leading(
        color: Colors.white,
      ),
      actions: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.only(right: 15.w),
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, CsRouter.withdrawalRule);
                },
                child: Text(
                  "提现规则",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 提现金额和提现记录
  Widget _buildWithdrawal() {
    return Padding(
      padding: EdgeInsets.only(left: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 16.h, bottom: 12.h),
            child: Consumer(
              builder: (context, ref, child) {
                return Text.rich(
                  TextSpan(
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                    children: [
                      TextSpan(text: "¥ ", style: TextStyle(fontSize: 16.sp)),
                      TextSpan(
                          text: "${ref.watch(
                        walletInfoProvider.select(
                          (value) => value?.canCash ?? 0.0,
                        ),
                      )}"),
                    ],
                  ),
                );
              },
            ),
          ),
          Row(
            children: [
              Text(
                "可提现余额（元）",
                style: TextStyle(
                  fontSize: 11.sp,
                  color: const Color(0xFFFAFAFA),
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, CsRouter.withdrawalRecord);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 3.h),
                  decoration: BoxDecoration(
                    color: Colors.black12,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    "提现记录",
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          BackWidget(
            scrollController: _scrollController,
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFF0000),
                Color(0xFFF5F5F5),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            rate: 0.85,
          ),
          CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(
              parent: BouncingScrollPhysics(),
            ),
            controller: _scrollController,
            slivers: [
              ValueListenableBuilder(
                valueListenable: _appBarAlpha,
                builder: (context, alpha, child) {
                  return _buildAppBar(context, alpha);
                },
              ),
              SliverToBoxAdapter(
                child: _buildWithdrawal(),
              ),
              const SliverToBoxAdapter(
                child: WithdrawalAccountWidget(),
              ),
              const SliverToBoxAdapter(
                child: WithdrawalAmountWidget(),
              ),
              const SliverToBoxAdapter(
                child: WithdrawalDescWidget(),
              ),
            ],
          )
        ],
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 60.h + MediaQuery.of(context).padding.bottom,
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: Consumer(
          builder: (context, ref, child) {
            return GradientButton(
              onPress: () {
                var accountDetail = ref.read(alipayAccountProvider);
                var currentAmount =
                    ref.read(currentSelectWithdrawAmountProvider);

                /// 是否绑定提现账户
                if (accountDetail != null) {
                  if (currentAmount?.withdrawType == "0") {
                    // 显示自定义金额弹窗
                    AmountInputDialog.showDialog();
                  } else {
                    /// 提现
                    ConfirmWithdrawalDialog.confirmWithdrawalDialog();
                  }
                } else {
                  /// 绑定提现账户
                  BindAlipayDialog.showBindAlipayDialog();
                }
              },
              margin: EdgeInsets.symmetric(horizontal: 51.w, vertical: 10.h),
              radius: 22,
              gradient: const LinearGradient(
                colors: [Color(0xFFFF3F4E), Color(0xFFFA2C1C)],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              child: Text(
                "立即提现",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
