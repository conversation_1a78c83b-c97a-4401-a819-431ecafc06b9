import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_rule_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/15 11:47
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/15 11:47
/// @UpdateRemark: 更新说明

class Desc {
  String title;
  String desc;

  Desc(this.title, this.desc);
}

final List<Desc> descList = [
  Desc(
    "1.提现说明",
    "提现指买什么都省用户将可提现金额提现至账户绑定的支付宝账户。用户在确认收货后最快第8-15天可提现。\n注：①可提现金额满1元即可提\n②下单时使用淘金币、淘宝红包等会影响返现金额",
  ),
  Desc(
    "2.提现到账",
    "提现将在2个工作日内审核到账，如遇高峰期，可能会延迟到账，请耐心等候，到账进度请到提现页面查询，每次提现不能超过200元。",
  ),
  Desc(
    "3.提现方式",
    "①用户提现时需填写支付宝账号与该支付宝账号认证的真实姓名，需确保绑定支付宝账号与该支付宝账号认证的真实姓名一致，不然会不到账哦。\n②.同一个用户只能绑定1个支付宝账号/手机号/买什么都省账号，1个支付宝账号仅限绑定1个买什么都省账号。",
  ),
  Desc(
    "4.重新提现",
    "以下情况可能导致提现失败，用户可重新提现，包括但不限于：到账支付宝不存在、到账支付宝存在安全隐患、支付宝账户存在身份冒用风险、支付宝账户未实名认证、提现人数拥挤、系统升级等。",
  ),
  Desc(
    "5.免责说明",
    "买什么都省有权对提现规则进行调整，并依照法律法规的最新规定主张免责，出现以下情况将会调整，包括但不限于：法律法规和相关政策的变动、淘宝/京东等平台规则变动、非法外部入侵、重大灾害事件等。",
  ),
];

class WithdrawalRulePage extends StatelessWidget {
  const WithdrawalRulePage({super.key});

  Widget _buildItem(String title, String desc) {
    return Container(
      margin: EdgeInsets.fromLTRB(15.w, 0, 15.w, 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
          Text(
            desc,
            style: TextStyle(
              fontSize: 14.sp,
              height: 1.7,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          "提现规则",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: 12.h),
              alignment: Alignment.center,
              child: Text(
                "提现规则说明",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ...descList.map((e) => _buildItem(e.title, e.desc)).toList(),
            Row(
              children: [
                SizedBox(
                  width: 15.w,
                ),
                Text(
                  "提现规则以最新版本APP为准。",
                  style: TextStyle(
                    fontSize: 14.sp,
                    height: 1.7,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h,),
            Row(
              children: [
                SizedBox(
                  width: 15.w,
                ),
                Text(
                  "若还有疑问未解决，请\n1）点击“我的—必备工具—客服中心”，即可联系到我们；\n2）拨打电话：020-87577697（工作时间：9:30-18:30）",
                  style: TextStyle(
                    fontSize: 14.sp,
                    height: 1.7,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h,),
          ],
        ),
      ),
    );
  }
}
