import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/bill/bill_provider.dart';
import 'package:msmds_platform/app/view/bill/widgets/bill_category_item.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: waller_bill_category
/// @Description: 即将入账
/// @Author: frankylee
/// @CreateDate: 2023/12/12 15:17
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/12 15:17
/// @UpdateRemark: 更新说明
class WallerBillCategory extends ConsumerStatefulWidget {
  const WallerBillCategory({
    super.key,
    required this.scrollController,
  });

  final ScrollController scrollController;

  @override
  WallerBillCategoryState createState() => WallerBillCategoryState();
}

class WallerBillCategoryState extends ConsumerState<WallerBillCategory> {
  @override
  Widget build(BuildContext context) {
    return CustomListView(
      isNest: true,
      attachController: false,
      physics: const ClampingScrollPhysics(),
      controller: widget.scrollController,
      data: ref.watch(
        comingSoonBillListProvider.select((value) => value.billList),
      ),
      onLoadMore: () async {
        ref.read(comingSoonBillListProvider.notifier).loadMore();
      },
      renderItem: (context, index, item) {
        return BillCategoryItem(
          bill: item,
        );
      },
      footerState: ref.watch(
        comingSoonBillListProvider.select(
          (value) => value.loadState ?? LoadState.idle,
        ),
      ),
      empty: Container(
        padding: EdgeInsets.only(top: 50.h),
        color: const Color(0xFFF9F9F9),
        alignment: Alignment.center,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Image.network(
              billEmpty,
              width: 236.w,
              fit: BoxFit.contain,
            ),
            Positioned(
              bottom: 15.h,
              child: Text(
                "暂无数据",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF999999),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
