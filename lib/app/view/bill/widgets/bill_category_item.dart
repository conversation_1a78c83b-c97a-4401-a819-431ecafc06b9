import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/bill/bill.dart';
import 'package:msmds_platform/app/repository/modals/bill/bill_status_desc.dart';

import '../../../navigation/router.dart';
import '../../../provider/order/order_detail_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: bill_category_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/27 15:03
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/27 15:03
/// @UpdateRemark: 更新说明

class BillCategoryItem extends ConsumerWidget {
  const BillCategoryItem({
    super.key,
    required this.bill,
  });

  final Bill? bill;

  // 账单点击，从标题中获取订单号，跳转订单详情
  void _onItemTap(BuildContext context, WidgetRef ref) {
    if (bill != null && bill?.billTitle != null && bill?.billType == 1) {
      var list = bill?.billTitle?.split("：");
      if (list != null && list.length >= 2) {
        var orderNo = list[1];
        ref.watch(currentOrderProvider.notifier).setCurrentOrder(orderNo);
        Navigator.pushNamed(context, CsRouter.orderDetail);
      }
    }
  }

  // 订单信息
  Widget _buildBillInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          "${bill?.billTitle}",
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFF202123),
          ),
        ),
        SizedBox(
          height: 1.h,
        ),
        Text(
          "${bill?.billTime}",
          style: TextStyle(
            fontSize: 9.sp,
            color: const Color(0xFF9C9C9C),
          ),
        ),
        SizedBox(
          height: 20.h,
        ),
        if (bill?.orderStatusDescVO != null) BillProgressWidget(bill: bill),
      ],
    );
  }

  // 收益金额信息
  Widget _buildAmountInfo() {
    return Column(
      children: [
        _amountItem(
          "${bill?.orderAmount}",
          bill?.billType == 1 ? " 返利" : " 邀请收益",
        ),
        if (bill?.redPacketAmount != null) SizedBox(height: 2.h),
        if (bill?.redPacketAmount != null)
          _amountItem(
            "${bill?.redPacketAmount}",
            " 红包",
          ),
      ],
    );
  }

  Widget _amountItem(String amount, String tag) {
    return Text.rich(
      TextSpan(
        style: TextStyle(fontSize: 15.sp, color: const Color(0xFFEA3931)),
        children: [
          TextSpan(
            text: "¥ ",
            style: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          TextSpan(
            text: amount,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          TextSpan(
            text: tag,
            style: TextStyle(
              fontSize: 8.sp,
              color: const Color(0xFF7C7C7C),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
      child: InkWell(
        onTap: () {
          _onItemTap(context, ref);
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              Image.network(
                bill?.goodsImgUrl ?? "",
                width: 64.w,
                height: 64.w,
              ),
              SizedBox(
                width: 6.w,
              ),
              Expanded(child: _buildBillInfo()),
              SizedBox(
                width: 20.w,
              ),
              _buildAmountInfo(),
            ],
          ),
        ),
      ),
    );
  }
}

// 进度显示
class BillProgressWidget extends StatelessWidget {
  const BillProgressWidget({
    super.key,
    required this.bill,
  });

  final Bill? bill;

  // 计算进度条
  double _progress(BillStatusDesc orderStatusDescVO) {
    var progressNum = 0.25;
    if ((orderStatusDescVO.statusSchedule == 2) ||
        (orderStatusDescVO.statusSchedule == 3)) {
      if (orderStatusDescVO.remainingRatio != null) {
        // 1代签收，2确认收货  3维权 4 维权失败 5 已退款
        progressNum = 0.5 + orderStatusDescVO.remainingRatio! * 0.5; // 进度条
      }
    } else if (orderStatusDescVO.statusSchedule == 4) {
      progressNum = 1;
    } else if (orderStatusDescVO.statusSchedule == 5) {
      progressNum = 1;
    }

    if (progressNum == 0.25) {
      progressNum = 0.27;
    }

    if (progressNum == 0.5) {
      progressNum = 0.52;
    }

    if (progressNum == 0.75) {
      progressNum = 0.77;
    }

    if (progressNum > 1) {
      progressNum = 1;
    }

    return progressNum;
  }

  // 圆形
  Widget _circle(Color color) {
    return Container(
      width: 4.w,
      height: 4.w,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(2.w),
      ),
    );
  }

  // 标题显示
  Widget _buildBillTitleStatus(BillStatusDesc orderStatusDescVO) {
    var index = 0;
    var progress = _progress(orderStatusDescVO);
    if (progress < 0.52) {
      index = 1;
    } else if (progress > 0.52 && progress <= 1) {
      index = 2;
    }
    var titleList = orderStatusDescVO.statusTitle;
    if (titleList == null || titleList.isEmpty) {
      return const SizedBox();
    }
    List<Widget> children = [];
    for (var i = 0; i < titleList.length; i++) {
      children.add(
        Text(
          titleList[i],
          style: TextStyle(
            fontSize: 7.sp,
            color: i != index || orderStatusDescVO.statusSchedule == 5
                ? const Color(0xFF9C9C9C)
                : const Color(0xFF202123),
          ),
        ),
      );
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: children,
    );
  }

  @override
  Widget build(BuildContext context) {
    var orderStatusDescVO = bill?.orderStatusDescVO;
    if (orderStatusDescVO == null) {
      return const SizedBox();
    }
    return Column(
      children: [
        Container(
          height: 8.h,
          margin: EdgeInsets.symmetric(horizontal: 2.w),
          decoration: BoxDecoration(
            color: const Color(0xFFFFD2D0),
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Stack(
            alignment: Alignment.centerLeft,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 2.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _circle(const Color(0xFFFFAEAE)),
                    _circle(const Color(0xFFFFAEAE)),
                    _circle(const Color(0xFFFFAEAE)),
                  ],
                ),
              ),
              FractionallySizedBox(
                widthFactor: _progress(orderStatusDescVO),
                child: Container(
                  height: 8.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.h),
                    color: orderStatusDescVO.statusSchedule == 5
                        ? const Color(0xFFCCCCCC)
                        : const Color(0xFFEA3931),
                  ),
                ),
              ),
              Container(
                height: 8.h,
                padding: EdgeInsets.symmetric(horizontal: 2.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _circle(Colors.white),
                    _circle(_progress(orderStatusDescVO) >= 0.5
                        ? Colors.white
                        : Colors.transparent),
                    _circle(_progress(orderStatusDescVO) == 1
                        ? Colors.white
                        : Colors.transparent),
                    if (orderStatusDescVO.statusSchedule == 5)
                      _circle(Colors.white),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 2.h,
        ),
        _buildBillTitleStatus(orderStatusDescVO),
      ],
    );
  }
}
