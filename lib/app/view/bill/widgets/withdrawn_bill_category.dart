import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../common/img/icon_addres.dart';
import '../../../../widgets/refresh/refresh_container.dart';
import '../../../provider/bill/bill_provider.dart';
import '../../../repository/modals/bill/withdrawn_bill.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.bill.widgets
/// @ClassName: withdrawn_bill_category
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/11 18:02
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/11 18:02
/// @UpdateRemark: 更新说明
class WithdrawnBillCategory extends ConsumerStatefulWidget {
  const WithdrawnBillCategory({
    super.key,
    required this.scrollController,
  });

  final ScrollController scrollController;

  @override
  WithdrawnBillCategoryState createState() => WithdrawnBillCategoryState();
}

class WithdrawnBillCategoryState extends ConsumerState<WithdrawnBillCategory> {
  Widget _renderItem(WithdrawnBill withdrawnBill) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "提现到${withdrawnBill.withdrawType == 2 ? "支付宝" : "微信零钱"}",
                style: TextStyle(
                  fontSize: 13.sp,
                  color: const Color(0xFF333333),
                ),
              ),
              SizedBox(
                height: 5.h,
              ),
              Text(
                withdrawnBill.createTime??"",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFF999999),
                ),
              ),
            ],
          ),
          Text(
            "${withdrawnBill.amount}",
            style: TextStyle(
              fontSize: 18.sp,
              color: const Color(0xFFFA5849),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomListView(
      isNest: true,
      attachController: false,
      physics: const ClampingScrollPhysics(),
      controller: widget.scrollController,
      data: ref.watch(
        withdrawnBillListProvider.select((value) => value.withdrawnBillList),
      ),
      onLoadMore: () async {
        ref.read(withdrawnBillListProvider.notifier).loadMore();
      },
      renderItem: (context, index, item) {
        return _renderItem(item);
      },
      footerState: ref.watch(
        withdrawnBillListProvider.select(
          (value) => value.loadState ?? LoadState.idle,
        ),
      ),
      empty: Container(
        padding: EdgeInsets.only(top: 50.h),
        color: const Color(0xFFF9F9F9),
        alignment: Alignment.center,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Image.network(
              billEmpty,
              width: 236.w,
              fit: BoxFit.contain,
            ),
            Positioned(
              bottom: 15.h,
              child: Text(
                "暂无数据",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF999999),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
