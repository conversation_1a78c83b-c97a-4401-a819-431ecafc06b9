import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/bill/bill_provider.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/view/bill/widgets/credited_bill_category.dart';
import 'package:msmds_platform/app/view/bill/widgets/waller_bill_category.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/common/widgets/back/back_widget.dart';
import 'package:msmds_platform/common/widgets/delegate/persistent_builder.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';
import 'package:msmds_platform/widgets/tabbar/rect_tab_indicator.dart';

import 'widgets/all_bill_category.dart';
import 'widgets/withdrawn_bill_category.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: bill_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/27 11:35
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/27 11:35
/// @UpdateRemark: 更新说明

class BillPage extends ConsumerStatefulWidget {
  const BillPage({super.key});

  @override
  BillPageState createState() => BillPageState();
}

class BillPageState extends ConsumerState<BillPage> {
  late ScrollController _scrollController;

  final ValueNotifier<int> _appBarAlpha = ValueNotifier(0);

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      _appBarAlpha.value = _scrollController.offset.toInt().clamp(0, 255);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// appbar
  SliverAppBar _buildAppBar(BuildContext context, int alpha) {
    return SliverAppBar(
      pinned: true,
      centerTitle: true,
      toolbarHeight: 44.h,
      backgroundColor: const Color(0xFFFF0000).withAlpha(alpha),
      elevation: 0,
      title: Text(
        "提现明细",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      leadingWidth: 42.w,
      leading: const Leading(
        color: Colors.white,
      ),
      actions: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.only(right: 15.w),
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, CsRouter.withdrawalRule);
                },
                child: Text(
                  "提现规则",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 通知信息
  // Widget _buildNotifyMsg() {
  //   return Container(
  //     margin: EdgeInsets.fromLTRB(10.w, 6.h, 10.w, 0),
  //     height: 36.h,
  //     child: Container(
  //       decoration: BoxDecoration(
  //         color: const Color(0xFFF7EADA),
  //         borderRadius: BorderRadius.circular(6.r),
  //       ),
  //       child: "不影响返现，请放心下单！(使用非买什么都省红包无返现）".marquee(
  //         ratioOfBlankToScreen: 0.2,
  //         moveDistance: 10,
  //         duration: 500,
  //         textStyle: TextStyle(
  //           fontSize: 14.sp,
  //           color: const Color(0xFF945D24),
  //         ),
  //       ),
  //     ),
  //   );
  // }

  /// 收益信息
  Widget _buildStatistics(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16.w, 10.h, 16.w, 0),
      child: Stack(
        children: [
          ClipRect(
            child: Align(
              alignment: Alignment.topCenter,
              heightFactor: 137 / 147,
              child: Image.network(
                billAmountBg,
                fit: BoxFit.fitWidth,
              ),
            ),
          ),
          Consumer(
            builder: (context, ref, child) {
              return Column(
                children: [
                  Container(
                    margin: EdgeInsets.fromLTRB(12.w, 12.h, 12.w, 16.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "可提现金额（元）",
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: Colors.black,
                              ),
                            ),
                            Padding(padding: EdgeInsets.only(bottom: 3.h)),
                            Text(
                              "${ref.watch(
                                walletInfoProvider.select(
                                  (value) => value?.canCash ?? "0.0",
                                ),
                              )}",
                              style: TextStyle(
                                fontSize: 24.sp,
                                color: const Color(0xFFFF0E38),
                              ),
                            ),
                          ],
                        ),
                        GradientButton(
                          onPress: () {
                            Navigator.pushNamed(context, CsRouter.withdrawal);
                          },
                          gradient: const LinearGradient(
                            colors: [Colors.white, Colors.white],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          shadow: false,
                          radius: 15.r,
                          child: Container(
                            padding: EdgeInsets.fromLTRB(11.w, 2.h, 6.w, 2.h),
                            child: Row(
                              children: [
                                Text(
                                  "立即提现",
                                  style: TextStyle(
                                    fontSize: 11.sp,
                                    color: const Color(0xFF4B4B4C),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_right_rounded,
                                  color: const Color(0xFF4B4B4C),
                                  size: 22.r,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(
                    indent: 12.w,
                    endIndent: 12.w,
                    height: 1,
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 12.h),
                    child: IntrinsicHeight(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildIncomeItem(
                            "累计收益 (元)",
                            "${ref.watch(
                              walletInfoProvider.select(
                                (value) => value?.totalEconomize ?? "0.0",
                              ),
                            )}",
                          ),
                          VerticalDivider(
                            indent: 5.h,
                            endIndent: 2.h,
                            width: 1,
                            color: const Color(0x39848484),
                          ),
                          _buildIncomeItem(
                            "已提现 (元)",
                            "${ref.watch(
                              walletInfoProvider.select(
                                (value) => value?.alreadyCash ?? "0.0",
                              ),
                            )}",
                          ),
                          VerticalDivider(
                            indent: 5.h,
                            endIndent: 2.h,
                            width: 1,
                            color: const Color(0x39848484),
                          ),
                          _buildIncomeItem(
                            "即将入账 (元)",
                            "${ref.watch(
                              walletInfoProvider.select(
                                (value) => value?.waitCash ?? "0.0",
                              ),
                            )}",
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  /// 收益item显示
  Widget _buildIncomeItem(String title, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF333333),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF202123),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// tab bar
  Widget _buildTabBar(int alpha) {
    return Container(
      height: 60.h,
      decoration: BoxDecoration(
        color: const Color(0xFFF9F9F9),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(
            16 - (alpha * 0.1).toDouble().clamp(0.0, 16.0),
          ),
          topLeft: Radius.circular(
            16 - (alpha * 0.1).toDouble().clamp(0.0, 16.0),
          ),
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0xFFF9F9F9),
            blurRadius: 0.0,
            spreadRadius: 0.0,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 10.w,
          vertical: 10.h,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: TabBar(
          labelPadding: EdgeInsets.zero,
          indicatorSize: TabBarIndicatorSize.label,
          indicator: RectTabIndicator(
            offset: 8,
            indicatorSize: 3.h,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF333333),
          unselectedLabelStyle:
              TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                    fontSize: 13.sp,
                  ),
          labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                fontSize: 13.sp,
              ),
          labelColor: const Color(0xFFFF0E38),
          tabs: BillType.values.map((e) => Tab(text: e.name)).toList(),
        ),
      ),
    );
  }

  /// PageView
  Widget _buildPageView() {
    return TabBarView(
      children: BillType.values.map(
        (e) {
          Widget child = Container();
          if (e == BillType.withdrawn) {
            child = WithdrawnBillCategory(
              scrollController: _scrollController,
            );
          }
          if (e == BillType.waller) {
            child = WallerBillCategory(
              scrollController: _scrollController,
            );
          }
          if (e == BillType.settled) {
            child = CreditedBillCategory(
              scrollController: _scrollController,
            );
          }
          if (e == BillType.all) {
            child = AllBillCategory(
              scrollController: _scrollController,
            );
          }
          return ScrollConfiguration(
            // This removes scrollbars from the inner scroll view <----
            behavior: ScrollConfiguration.of(context).copyWith(
              scrollbars: false,
            ),
            child: child,
          );
        },
      ).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          BackWidget(
            scrollController: _scrollController,
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFF0000),
                Color(0xFFF5F5F5),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            rate: 1.5,
          ),
          DefaultTabController(
            length: BillType.values.length,
            child: RefreshIndicator(
              color: const Color(0xFFFF3F4E),
              notificationPredicate: (notification) {
                return true;
              },
              onRefresh: () async {
                if (ref.exists(withdrawnBillListProvider)) {
                  ref.read(withdrawnBillListProvider.notifier).loadData();
                }
                if (ref.exists(comingSoonBillListProvider)) {
                  ref.read(comingSoonBillListProvider.notifier).loadData();
                }
                if (ref.exists(creditedBillListProvider)) {
                  ref.read(creditedBillListProvider.notifier).loadData();
                }
                if (ref.exists(allBillListProvider)) {
                  ref.read(allBillListProvider.notifier).loadData();
                }
              },
              child: NestedScrollView(
                controller: _scrollController,
                headerSliverBuilder:
                    (BuildContext context, bool innerBoxIsScrolled) {
                  return [
                    ValueListenableBuilder(
                      valueListenable: _appBarAlpha,
                      builder: (context, alpha, child) {
                        return _buildAppBar(context, alpha);
                      },
                    ),
                    // SliverToBoxAdapter(
                    //   child: _buildNotifyMsg(),
                    // ),
                    SliverToBoxAdapter(
                      child: _buildStatistics(context),
                    ),
                    SliverPersistentHeader(
                      pinned: true,
                      delegate: PersistentBuilder(
                        max: 60.h,
                        min: 60.h,
                        builder: (_, offset) {
                          return ValueListenableBuilder(
                            valueListenable: _appBarAlpha,
                            builder: (context, alpha, child) {
                              return _buildTabBar(alpha);
                            },
                          );
                        },
                      ),
                    ),
                  ];
                },
                body: _buildPageView(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
