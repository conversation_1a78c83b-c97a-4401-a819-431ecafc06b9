import 'package:flutter/cupertino.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: container_clipper
/// @Description: 账单样式Clipper
/// @Author: frankylee
/// @CreateDate: 2023/12/12 14:10
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/12 14:10
/// @UpdateRemark: 更新说明
class BillItemLeftClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(size.width - 5, 0);
    path.quadraticBezierTo(size.width - 5, 5, size.width, 5);
    path.lineTo(size.width, size.height - 5);
    path.quadraticBezierTo(
        size.width - 5, size.height - 5, size.width - 5, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}

class BillItemRightClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(0, 5);
    path.quadraticBezierTo(5, 5, 5, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(5, size.height);
    path.quadraticBezierTo(5, size.height - 5, 0, size.height - 5);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}
