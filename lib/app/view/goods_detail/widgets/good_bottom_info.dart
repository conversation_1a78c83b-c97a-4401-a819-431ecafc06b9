import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/goods/favorites_provider.dart';
import 'package:msmds_platform/app/provider/goods/history_provider.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_detail.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

import '../../../../widgets/button/gradient_button.dart';
import '../../../provider/conversion/link_conversion_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: good_bottom_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 16:45
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 16:45
/// @UpdateRemark: 更新说明
class GoodsBottomInfo extends ConsumerStatefulWidget {
  const GoodsBottomInfo({
    super.key,
    required this.detail,
  });

  final GoodsDetail? detail;

  @override
  GoodsBottomInfoState createState() => GoodsBottomInfoState();
}

class GoodsBottomInfoState extends ConsumerState<GoodsBottomInfo> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // 检查商品是否收藏
      ref
          .read(checkGoodsFavoriteProvider.notifier)
          .check(widget.detail?.goodsId, widget.detail?.platformType);

      // 添加商品浏览记录
      ref.read(saveGoodsBrowsingHistoryProvider.notifier).addBrowsingHistory(
            widget.detail?.goodsId,
            widget.detail?.platformType,
          );
    });
  }

  Widget _buildFavWidget(WidgetRef ref) {
    if (widget.detail?.platformType != 19) {
      var isFav = ref.watch(checkGoodsFavoriteProvider);
      return SizedBox(
        width: 60.w,
        child: InkWell(
          onTap: () {
            ref.read(checkGoodsFavoriteProvider.notifier).addFavOrCancel(
                  isFav,
                  widget.detail?.goodsId,
                  widget.detail?.platformType,
                );
          },
          child: Column(
            children: [
              Image.asset(
                isFav ? collection : uncollection,
                width: 20.w,
                height: 20.w,
              ),
              SizedBox(height: 5.h),
              Text(
                isFav ? "已收藏" : "收藏",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFF333333),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return SizedBox(width: 26.w);
  }

  @override
  Widget build(BuildContext context) {
    var btnStr = "领券购买";
    if (widget.detail?.platformType == 6) {
      btnStr = "去淘宝购买";
    } else if (widget.detail?.platformType == 2) {
      btnStr = "去京东购买";
    }

    debugPrint("GoodsBottomInfo: ${widget.detail?.platformType}");

    return Container(
      color: Colors.white,
      height: 48.h + MediaQuery.of(context).padding.bottom,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildFavWidget(ref),
              Expanded(
                child: SizedBox(
                  height: 40.h,
                  child: GradientButton(
                    onPress: () {
                      ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
                            widget.detail?.platformType,
                            materialId: widget.detail?.goodsId,
                            goodsSign: widget.detail?.goodsId,
                            goodsId: widget.detail?.goodsId,
                            goodsUrl: widget.detail?.goodsUrl,
                            couponUrl: widget.detail?.couponUrl,
                          );
                    },
                    gradient: const LinearGradient(colors: [
                      Color(0xFFFE7500),
                      Color(0xFFFF4A00),
                    ]),
                    shadow: false,
                    radius: 20.h,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          btnStr,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFFFE7D7),
                          ),
                        ),
                        if (widget.detail?.subtractAmount != null)
                          Text(
                            "(约省${widget.detail?.subtractAmount}元)",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: const Color(0xFFFFE7D7),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: 26.w),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).padding.bottom,
          ),
        ],
      ),
    );
  }
}
