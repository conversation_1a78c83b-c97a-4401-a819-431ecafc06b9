import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_detail.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: goods_cover_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 17:11
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 17:11
/// @UpdateRemark: 更新说明
class GoodsCoverInfo extends ConsumerWidget {
  const GoodsCoverInfo({
    super.key,
    required this.detail,
  });

  final GoodsDetail? detail;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<String> images = [];
    if (detail != null && detail?.smallPicList != null) {
      images = detail!.smallPicList!;
    } else {
      images = [detail?.goodsImg ?? ""];
    }
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
        bottom: 12.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 240.w,
            height: 240.w,
            child: Swiper(
              autoplay: true,
              containerHeight: 240.w,
              containerWidth: 240.w,
              itemHeight: 240.w,
              itemWidth: 240.w,
              itemBuilder: (context, index) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(4.r),
                  child: Image.network(
                    images[index],
                    fit: BoxFit.contain,
                    width: 240.w,
                    height: 240.w,
                  ),
                );
              },
              itemCount: images.length,
              pagination: SwiperCustomPagination(builder: (context, config) {
                return Positioned(
                  bottom: 20.h,
                  right: 10.w,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: const Color(0x99000000),
                      borderRadius: BorderRadius.circular(7.r),
                    ),
                    child: Text(
                      "${config.activeIndex + 1}/${config.itemCount}",
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                );
              }),
              controller: SwiperController(),
            ),
          ),
        ],
      ),
    );
  }
}
