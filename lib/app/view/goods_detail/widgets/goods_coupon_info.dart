import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_detail.dart';
import 'package:msmds_platform/config/constant.dart';

import '../../../provider/conversion/link_conversion_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: goods_coupon_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/19 11:52
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/19 11:52
/// @UpdateRemark: 更新说明
class GoodsCouponInfo extends ConsumerWidget {
  const GoodsCouponInfo({
    super.key,
    required this.detail,
  });

  final GoodsDetail? detail;

  // 京东券
  Widget _buildJdUseCoupon(WidgetRef ref) {
    List<Widget> children = [];
    var couponLabelList = detail?.jdCanUseLabelInfoList;
    if (couponLabelList != null && couponLabelList.isNotEmpty) {
      var firstLabel = couponLabelList.first;
      children.add(
        Container(
          margin: EdgeInsets.only(bottom: 10.h),
          child: Row(
            children: [
              Text(
                "可叠加促销：",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFF333333),
                ),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFEE9E8),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    "${firstLabel.promotionLabel}",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFFFA2B18),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (detail != null &&
        detail!.jdCanUseCoupons != null &&
        detail!.jdCanUseCoupons!.isNotEmpty) {
      children.addAll(detail!.jdCanUseCoupons!
          .map(
            (e) => InkWell(
              onTap: () {
                ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
                      detail?.platformType,
                      materialId: detail?.goodsId,
                      goodsSign: detail?.goodsId,
                      goodsId: detail?.goodsId,
                      couponUrl: e.link,
                    );
              },
              child: Container(
                width: 335.w,
                height: 60.h,
                margin: EdgeInsets.only(bottom: 8.h),
                padding: EdgeInsets.symmetric(horizontal: 14.w),
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                        "${Constant.msmdsAliCdn}/APPSHOW/gooddetail_gift_bg.png"),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          "${e.discountInfo}",
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(
                          width: 11.w,
                        ),
                        Text(
                          "买省专享优惠券",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      "领取 >",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
          .toList());
    }

    return Column(
      children: children,
    );
  }

  // 其他券
  Widget _buildDefaultCoupon(WidgetRef ref) {
    return InkWell(
      onTap: () {
        ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
              detail?.platformType,
              materialId: detail?.goodsId,
              goodsSign: detail?.goodsId,
              goodsId: detail?.goodsId,
              couponUrl: detail?.couponUrl,
            );
      },
      child: Container(
        width: 335.w,
        height: 60.h,
        margin: EdgeInsets.only(bottom: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(
                "${Constant.msmdsAliCdn}/APPSHOW/gooddetail_gift_bg.png"),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: "¥",
                        style: TextStyle(
                          fontSize: 14.sp,
                        ),
                      ),
                      TextSpan(text: "${detail?.couponAmount}"),
                    ],
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
                SizedBox(
                  width: 11.w,
                ),
                Text(
                  "买省专享优惠券",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            Text(
              "领取 >",
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (detail != null &&
        detail?.jdCanUseCoupons != null &&
        detail!.jdCanUseCoupons!.isNotEmpty) {
      return Container(
        margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
        padding: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Column(
          children: [
            _buildJdUseCoupon(ref),
          ],
        ),
      );
    }
    if (detail?.couponInfo != null) {
      return Container(
        margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
        padding: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Column(
          children: [
            _buildDefaultCoupon(ref),
          ],
        ),
      );
    }
    return Container();
  }
}
