import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_detail.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: goods_detail_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 15:22
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 15:22
/// @UpdateRemark: 更新说明
class GoodsDetailInfo extends ConsumerWidget {
  const GoodsDetailInfo({
    super.key,
    required this.detail,
  });

  final GoodsDetail? detail;

  // 标题
  Widget _buildTitle() {
    Color tagBgColor = const Color(0xFFFE002D);
    if (detail?.platformType == 21) {
      tagBgColor = const Color(0xFF333333);
    } else if (detail?.platformType == 19) {
      tagBgColor = const Color(0xFFD60075);
    }
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 16.sp,
          color: const Color(0xFF3A3A3A),
          height: 1.2,
          fontWeight: FontWeight.w600,
        ),
        children: [
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Container(
              margin: const EdgeInsets.only(right: 4),
              padding: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: LinearGradient(
                  colors: [
                    tagBgColor,
                    tagBgColor,
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: Text(
                detail?.platformTag ?? "",
                style: TextStyle(
                  fontSize: 11.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          TextSpan(text: "${detail?.goodsName}"),
        ],
      ),
      style: TextStyle(
        fontSize: 14.sp,
        color: const Color(0xFF333333),
        height: 1.2,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  // 价格，销量
  Widget _buildPriceVolume() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IntrinsicHeight(
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "买省到手约",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFFEA3931),
                    ),
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: const Color(0xFFEA3931),
                        fontWeight: FontWeight.w600,
                      ),
                      children: [
                        TextSpan(
                          text: "¥ ",
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                        ),
                        TextSpan(
                          text: "${detail?.priceAfterReceive}",
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                width: 10.w,
              ),
              const VerticalDivider(
                width: 1,
                color: Color(0xFFCCCCCC),
              ),
              SizedBox(
                width: 10.w,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _wlPricePrx(),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: const Color(0xFF999999),
                        fontWeight: FontWeight.w600,
                      ),
                      children: [
                        TextSpan(
                          text: "¥ ",
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                        ),
                        TextSpan(
                          text: "${detail?.price}",
                          style: const TextStyle(
                            decoration: TextDecoration.lineThrough,
                          )
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        if (detail?.saleVolume != null)
          Text(
            "月销量：${detail?.saleVolume}",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF9C9C9C),
            ),
          ),
      ],
    );
  }

  // 在售价前缀
  String _wlPricePrx() {
    var prx = "";
    if (detail?.platformType == 2) {
      prx = "京东在售价";
    } else if (detail?.platformType == 6) {
      prx = "淘宝在售价";
    } else if (detail?.platformType == 10) {
      prx = "拼多多在售价";
    } else if (detail?.platformType == 19) {
      prx = "唯品会在售价";
    } else if (detail?.platformType == 21) {
      prx = "抖音在售价";
    }
    return prx;
  }

  // 红包和返现
  Widget _badeAndCommission(WidgetRef ref) {
    var userInfo = ref.watch(authProvider);
    var vipLevel = userInfo?.data?.vipLevel ?? -1;
    var isVip = vipLevel >= 0;
    List<Widget> children = [];
    if (detail?.redPacketAmount != null) {
      children.add(
        Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 1.h),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFC5E61),
                Color(0xFFFF231F),
              ],
            ),
          ),
          child: Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.white,
              ),
              children: [
                const TextSpan(text: "平台红包"),
                TextSpan(
                  text: "¥${detail?.redPacketAmount}",
                  style: const TextStyle(
                    color: Color(0xFFFFE600),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    children.add(
      Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 1.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          gradient: const LinearGradient(
            colors: [
              Color(0xFFFC5E61),
              Color(0xFFFF231F),
            ],
          ),
        ),
        child: Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.white,
            ),
            children: [
              TextSpan(text: isVip ? "会员约返" : "约返"),
              TextSpan(
                text:
                    "¥${isVip ? detail?.vipCommission : detail?.noVipCommission}",
                style: const TextStyle(
                  color: Color(0xFFFFE600),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    return Wrap(
      spacing: 10.w,
      runSpacing: 10.h,
      children: children,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(),
          SizedBox(
            height: 18.h,
          ),
          _buildPriceVolume(),
          SizedBox(
            height: 18.h,
          ),
          _badeAndCommission(ref),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }
}
