import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/goods_detail/goods_detail_provider.dart';
import 'package:msmds_platform/widgets/marquee/marquee_text.dart';

import '../../../repository/modals/popularize/popularize.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: notice_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/7 15:57
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/7 15:57
/// @UpdateRemark: 更新说明
class NoticeWidget extends ConsumerWidget {
  const NoticeWidget({
    super.key,
    required this.platformType,
  });

  final int? platformType;

  Popularize? _expectData(List<Popularize> list) {
    Popularize? expectData;
    for (var i = 0; i < list.length; i++) {
      dynamic data;
      try {
        if (list[i].data != null) {
          data = jsonDecode(list[i].data!);
        }
      } catch (error) {
        data = {};
      }

      if ((platformType == 6 && data['type'] == 16) ||
          (platformType == 2 && data['type'] == 15) ||
          (platformType == 10 && data['type'] == 17)) {
        expectData = list[i];
      }
    }

    if (expectData?.picture == null && expectData?.url == null) {
      return null;
    }

    return expectData;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchNoticeDetailProvider).when(
      data: (list) {
        if (list == null || list.isEmpty) {
          return const SizedBox();
        }
        var item = _expectData(list);
        if (item == null) {
          return const SizedBox();
        }
        return Container(
          height: 32.h,
          margin: EdgeInsets.symmetric(horizontal: 10.w),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFFD8A1),
                Color(0xFFF0B97B),
              ],
            ),
            borderRadius: BorderRadius.circular(6.r),
          ),
          child: "${item.picture}  ${item.url}".marquee(
            textStyle: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF29221A),
            ),
          ),
        );
      },
      error: (o, s) {
        return const SizedBox();
      },
      loading: () {
        return const SizedBox();
      },
    );
  }
}
