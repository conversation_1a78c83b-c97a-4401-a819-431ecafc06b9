import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_detail.dart';
import 'package:msmds_platform/config/constant.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: goods_shop_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 16:28
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 16:28
/// @UpdateRemark: 更新说明
class GoodsShopInfo extends ConsumerWidget {
  const GoodsShopInfo({
    super.key,
    required this.detail,
  });

  final GoodsDetail? detail;

  String _getPlatformImg() {
    var platLogo = "";
    if (detail?.platformType == 6) {
      if (detail?.platformTag == "淘宝") {
        platLogo = "${Constant.msmdsAliCdn}/detail/logo.png";
      } else {
        platLogo = "${Constant.msmdsAliCdn}/detail/tm.png";
      }
    } else if (detail?.platformType == 2) {
      platLogo = "${Constant.msmdsAliCdn}/detail/jdlogo.png";
    } else if (detail?.platformType == 10) {
      platLogo = "${Constant.msmdsAliCdn}/detail/pddlogo.png";
    } else if (detail?.platformType == 21) {
      platLogo = "${Constant.msmdsAliCdn}/APPSHOW/dylogo.png";
    } else if (detail?.platformType == 19) {
      platLogo = "${Constant.msmdsAliCdn}/peiZhiBanner/homeIcon_weipinhui.png";
    }
    return platLogo;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        children: [
          Image.network(
            _getPlatformImg(),
            width: 44.w,
            height: 44.w,
          ),
          SizedBox(
            width: 16.w,
          ),
          Text(
            "${detail?.shopName}",
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF3A3A3A),
            ),
          ),
        ],
      ),
    );
  }
}
