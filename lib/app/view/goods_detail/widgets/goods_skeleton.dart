import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail.widgets
/// @ClassName: goods_skeleton
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 17:04
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 17:04
/// @UpdateRemark: 更新说明
class GoodsSkeleton extends ConsumerWidget {
  const GoodsSkeleton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Column(
        children: [
          Stack(
            children: [
              Container(
                height: 264.w,
                margin:
                    EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: 240.w,
                      width: 240.w,
                      color: Colors.grey.withAlpha(10),
                    ),
                  ],
                ),
              ),
              AppBar(
                elevation: 0,
                backgroundColor: Colors.white.withAlpha(0),
                leading: const Leading(),
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 12.h,
                  color: Colors.grey.withAlpha(20),
                ),
                SizedBox(
                  height: 4.h,
                ),
                Container(
                  height: 12.h,
                  margin: EdgeInsets.only(right: 50.w),
                  color: Colors.grey.withAlpha(20),
                ),
                SizedBox(
                  height: 18.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 40.w,
                          height: 12.h,
                          color: Colors.grey.withAlpha(20),
                        ),
                        SizedBox(
                          width: 20.w,
                        ),
                        Container(
                          width: 40.w,
                          height: 12.h,
                          color: Colors.grey.withAlpha(20),
                        ),
                      ],
                    ),
                    Container(
                      width: 40.w,
                      height: 12.h,
                      color: Colors.grey.withAlpha(20),
                    ),
                  ],
                ),
                SizedBox(
                  height: 12.h,
                ),
                Container(
                  width: 70.w,
                  height: 12.h,
                  color: Colors.grey.withAlpha(20),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6.r),
            ),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            margin: EdgeInsets.symmetric(horizontal: 12.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 12.w,
                  color: Colors.grey.withAlpha(20),
                ),
                SizedBox(
                  height: 10.w,
                ),
                Container(
                  height: 12.w,
                  margin: EdgeInsets.only(right: 120.w),
                  color: Colors.grey.withAlpha(20),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.w,
                  color: Colors.grey.withAlpha(20),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Expanded(
                  child: Container(
                    height: 12.w,
                    margin: EdgeInsets.only(right: 120.w),
                    color: Colors.grey.withAlpha(20),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 12.h),
            child: Row(
              children: [
                Container(
                  width: 60.w,
                  height: 12.h,
                  color: Colors.grey.withAlpha(20),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Expanded(
              child: Container(
            color: Colors.white60,
          )),
        ],
      ),
    );
  }
}
