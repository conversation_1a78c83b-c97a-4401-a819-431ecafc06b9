import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/goods_detail/widgets/goods_cover_info.dart';
import 'package:msmds_platform/app/view/goods_detail/widgets/goods_detail_info.dart';
import 'package:msmds_platform/app/view/goods_detail/widgets/goods_shop_info.dart';
import 'package:msmds_platform/app/view/goods_detail/widgets/goods_skeleton.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../provider/goods_detail/goods_detail_provider.dart';
import 'widgets/good_bottom_info.dart';
import 'widgets/goods_coupon_info.dart';
import 'widgets/notice_widget.dart';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package: app.view.goods_detail
/// @ClassName: goods_detail_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 14:17
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 14:17
/// @UpdateRemark: 更新说明
class GoodsDetailPage extends ConsumerStatefulWidget {
  const GoodsDetailPage({
    super.key,
    this.arguments,
  });

  final Object? arguments;

  @override
  GoodsDetailPageState createState() => GoodsDetailPageState();
}

class GoodsDetailPageState extends ConsumerState<GoodsDetailPage> {
  late ScrollController _scrollController;

  final ValueNotifier<int> _appBarAlpha = ValueNotifier(0);

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      _appBarAlpha.value = _scrollController.offset.toInt().clamp(0, 255);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// appbar
  Widget _buildAppBar(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _appBarAlpha,
      builder: (context, alpha, child) {
        return Column(
          children: [
            AppBar(
              // pinned: true,
              centerTitle: true,
              toolbarHeight: 44.h,
              backgroundColor: const Color(0xFFFFFFFF).withAlpha(alpha),
              elevation: 0,
              title: Text(
                "商品详情",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.black.withAlpha(alpha),
                  fontWeight: FontWeight.w600,
                ),
              ),
              leadingWidth: 42.w,
              leading: Leading(
                alpha: alpha,
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ref.watch(fetchGoodsDetailProvider(widget.arguments)).when(
      data: (detail) {
        if (detail == null) {
          return const GoodsSkeleton();
        }
        return Scaffold(
          body: Stack(
            children: [
              CustomScrollView(
                controller: _scrollController,
                slivers: [
                  SliverToBoxAdapter(
                    child: GoodsCoverInfo(detail: detail),
                  ),
                  SliverToBoxAdapter(
                    child: NoticeWidget(
                      platformType: detail.platformType,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: GoodsDetailInfo(detail: detail),
                  ),
                  SliverToBoxAdapter(
                    child: GoodsCouponInfo(detail: detail),
                  ),
                  SliverToBoxAdapter(
                    child: GoodsShopInfo(detail: detail),
                  ),
                  if (detail.smallPicList != null &&
                      detail.smallPicList!.isNotEmpty)
                    SliverToBoxAdapter(
                      child: Container(
                        padding: EdgeInsets.fromLTRB(20.w, 11.h, 0, 11.h),
                        color: Colors.white,
                        child: Text(
                          "商品详情",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color(0xFF333333),
                          ),
                        ),
                      ),
                    ),
                  if (detail.smallPicList != null &&
                      detail.smallPicList!.isNotEmpty)
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        childCount: detail.smallPicList?.length,
                        (context, index) => Image.network(
                          detail.smallPicList?[index] ?? "",
                          width: MediaQuery.of(context).size.width,
                        ),
                      ),
                    ),
                ],
              ),
              _buildAppBar(context),
            ],
          ),
          bottomNavigationBar: GoodsBottomInfo(detail: detail),
        );
      },
      error: (o, s) {
        return const GoodsSkeleton();
      },
      loading: () {
        return const GoodsSkeleton();
      },
    );
  }
}
