import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/account/login_provider.dart';
import 'package:msmds_platform/app/view/login/widget/agreement.dart';
import 'package:msmds_platform/app/view/login/widget/text_animation.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/utils/signature_util.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON> Lee
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: login_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 15:56
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 15:56
/// @UpdateRemark: 更新说明
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  LoginPageState createState() => LoginPageState();
}

class LoginPageState extends ConsumerState<LoginPage>
    with TickerProviderStateMixin {
  /// 手机号
  final TextEditingController _textPhoneController = TextEditingController();

  late final AnimationController _shakeController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 500),
  );

  /// 同意隐私协议
  bool agree = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      ref.read(authProvider.notifier).logout();
    });

    super.initState();
  }

  void _closeLoginPage() {
    Navigator.pop(context);
  }

  /// 手机软键盘提交按钮
  void _onSubmitted(String text) {
    _getVerCode();
  }

  /// 获取验证码
  void _getVerCode() async {
    String phone = _textPhoneController.text;
    debugPrint('_getVerCode: $phone');
    debugPrint('_getVerCode: $agree');
    if (phone.isEmpty) {
      ToastUtil.showToast(
        "请先输入手机号",
      );
      return;
    }
    if (!SignatureUtil.phoneNumMatch(phone)) {
      ToastUtil.showToast(
        "手机格式错误",
      );
      return;
    }
    if (!agree) {
      _shakeController.forward();
      return;
    }

    /// 设置手机号
    if (ref.exists(loginProvider)) {
      ref.read(loginProvider.notifier).setVerifyPhone(phone);
    } else {
      ref.watch(loginProvider.notifier).setVerifyPhone(phone);
    }

    /// 发送验证码
    var result =
        await ref.read(verifyCodeProvider.notifier).getVerifyCode(context);
    if (result) {
      if (!mounted) return;
      Navigator.pushNamed(context, CsRouter.verification);
    }
  }

  /// 关闭按钮
  Widget _buildTopBtn() {
    return GestureDetector(
      onTap: _closeLoginPage,
      child: Container(
        margin: EdgeInsets.only(bottom: 70.h),
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 20),
        alignment: Alignment.centerRight,
        child: Text(
          "不登录,先逛逛",
          style: TextStyle(
              letterSpacing: 1,
              fontSize: 18.sp,
              color: const Color(0xFF7C7C7C),
              fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "登录即享吃喝玩乐",
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF000000),
            fontWeight: FontWeight.bold,
          ),
        ),
        const Padding(padding: EdgeInsets.only(right: 2)),
        Image.network(
          '${Constant.msmdsAliCdn}/appNormal/loginOneFold.png',
          width: 66.w,
          height: 25.h,
        ),
      ],
    );
  }

  /// 手机号输入
  Widget _buildPhoneInput() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(2),
      ),
      margin: EdgeInsets.only(top: 45.h),
      padding: const EdgeInsets.symmetric(horizontal: 22),
      child: TextField(
        controller: _textPhoneController,
        keyboardType: TextInputType.number,
        maxLength: 11,
        style: TextStyle(fontSize: 14.sp),
        onSubmitted: _onSubmitted,
        decoration: InputDecoration(
          border: InputBorder.none,
          counterText: "",
          hintStyle: TextStyle(
            color: const Color(0xFF999999),
            fontSize: 14.sp,
          ),
          hintText: "请输入您的手机号码",
        ),
      ),
    );
  }

  /// 获取验证码按钮
  Widget _buildGetVerCodeBtn() {
    return GradientButton(
      onPress: _getVerCode,
      radius: 25,
      margin: EdgeInsets.only(top: 20.h, bottom: 15.h),
      padding: EdgeInsets.symmetric(vertical: 10.h),
      gradient: const LinearGradient(
        colors: [
          Color(0xFFFE5640),
          Color(0xFFFA2E1B),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      child: Text(
        "获取验证码",
        style: TextStyle(fontSize: 18.sp, color: Colors.white),
      ),
    );
  }

  /// 隐私协议
  Widget _buildAgreement() {
    return TextAnimationWidget(
      shakeController: _shakeController,
      child: Agreement(
        onChange: (value) {
          agree = value;
        },
      ),
    );
  }

  /// 其他登录方式
  // Widget _buildOtherLogin() {
  //   return Column(
  //     children: [
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.center,
  //         children: [
  //           Container(
  //             width: 20.w,
  //             height: 1,
  //             color: const Color(0xFFBBBBBB),
  //           ),
  //           Text(
  //             AppLocalizations.of(context)!.otherLoginLabel,
  //             style: TextStyle(
  //               fontSize: 12.sp,
  //               color: const Color(0xFFBBBBBB),
  //             ),
  //           ),
  //           Container(
  //             width: 20.w,
  //             height: 1,
  //             color: const Color(0xFFBBBBBB),
  //           ),
  //         ],
  //       ),
  //       Padding(padding: EdgeInsets.only(bottom: 20.h)),
  //       Column(
  //         children: [
  //           Image.asset(
  //             iconWechatLogin,
  //             width: 42.w,
  //             height: 42.w,
  //           ),
  //           Text(
  //             AppLocalizations.of(context)!.wechatLogin,
  //             style: TextStyle(
  //               fontSize: 14.sp,
  //               color: const Color(0xFF999999),
  //             ),
  //           ),
  //         ],
  //       ),
  //       // Padding(padding: EdgeInsets.only(bottom: 120.h)),
  //     ],
  //   );
  // }

  /// 构建登录方式
  /// 没有酷赛ID就使用手机号登录
  /// 有酷赛ID就使用ID一键登录
  Widget _buildInputOrOnKey() {
    return Column(
      children: [
        _buildPhoneInput(),
        _buildGetVerCodeBtn(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarContrastEnforced: false,
      ),
      child: Scaffold(
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            children: [
              _buildTopBtn(),
              _buildTitle(),
              _buildInputOrOnKey(),
              _buildAgreement(),
              // Padding(padding: EdgeInsets.only(bottom: 200.h)),
              // _buildOtherLogin(),
            ],
          ),
        ),
      ),
    );
  }
}
