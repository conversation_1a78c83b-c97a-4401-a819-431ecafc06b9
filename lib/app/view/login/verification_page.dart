import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/account/login_provider.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/view/login/widget/verification_box_item.dart';
import 'package:msmds_platform/app/view/login/widget/verificationbox.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/utils/router_util.dart';

/// Copyright (C), 2021-2022, <PERSON>y <PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: verification_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/21 11:05
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/21 11:05
/// @UpdateRemark: 验证码输入页面
class VerificationPage extends ConsumerStatefulWidget {
  const VerificationPage({super.key});

  @override
  VerificationState createState() => VerificationState();
}

class VerificationState extends ConsumerState<VerificationPage> {
  void _exitLogin() {
    RouterUtil.exitLogin(context);
  }

  /// 登录
  void _onSubmitted(code) async {
    if (ref.exists(verifyCodeProvider)) {
      ref.read(verifyCodeProvider.notifier).setVerifyCode(code);
    } else {
      ref.watch(verifyCodeProvider.notifier).setVerifyCode(code);
    }
    var result = await ref.read(authProvider.notifier).login();
    if (result != null) {
      /// 登录成功
      _exitLogin();
      // 更新用户信息
      ref.read(newestUserInfoProvider.notifier).getNewestUserInfo();
    }
  }

  /// 提示信息
  Widget _buildCodeTip() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 32.w, vertical: 48.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "验证码输入",
            style: TextStyle(
                fontSize: 30.sp,
                color: const Color(0xFF202123),
                fontWeight: FontWeight.w600),
          ),
          const Padding(padding: EdgeInsets.only(top: 10)),
          Text.rich(
            TextSpan(
              children: [
                const TextSpan(
                  text: "已发送6位验证码至",
                ),
                TextSpan(
                  text: ref.watch(loginProvider),
                  style: const TextStyle(color: Color(0xFFEA3D35)),
                ),
                const TextSpan(
                  text: ",请留意短信",
                )
              ],
            ),
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF7C7C7C),
            ),
          )
        ],
      ),
    );
  }

  /// 验证码输入
  Widget _buildVerCodeInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15),
      child: VerificationBox(
        focusBorderColor: Colors.black87,
        onSubmitted: _onSubmitted,
        showCursor: true,
        cursorColor: Colors.grey.withOpacity(0.6),
        borderWidth: 1,
        itemWidget: 40.w,
        unfocus: true,
        type: VerificationBoxItemType.underline,
        textStyle: TextStyle(
          fontSize: 30.sp,
          color: const Color(0xFF282828),
        ),
        borderColor: Colors.grey,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          GestureDetector(
            onTap: _exitLogin,
            child: Container(
              alignment: Alignment.center,
              margin: EdgeInsets.only(right: 15.w),
              child: Text(
                "不登录,先逛逛",
                style: TextStyle(
                    letterSpacing: 1,
                    fontSize: 18.sp,
                    color: const Color(0xFF7C7C7C),
                    fontWeight: FontWeight.w500),
              ),
            ),
          )
        ],
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCodeTip(),
            _buildVerCodeInput(),
          ],
        ),
      ),
    );
  }
}
