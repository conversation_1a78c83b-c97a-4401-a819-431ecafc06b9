import 'dart:math';

import 'package:flutter/material.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: privacy_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/21 10:32
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/21 10:32
/// @UpdateRemark: 用户协议与隐私政策抖动效果

const shakeCount = 4;

class TextAnimationWidget extends StatefulWidget {
  const TextAnimationWidget({
    super.key,
    required this.shakeController,
    required this.child,
  });

  final AnimationController shakeController;

  final Widget child;

  @override
  TextAnimationWidgetState createState() => TextAnimationWidgetState();
}

class TextAnimationWidgetState extends State<TextAnimationWidget> {
  @override
  void initState() {
    widget.shakeController.addListener(() {
      if (widget.shakeController.status == AnimationStatus.completed) {
        widget.shakeController.reset();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.shakeController,
      builder: (context, child) {
        final sineValue =
            sin(shakeCount * 2 * pi * widget.shakeController.value);
        return Transform.translate(
            offset: Offset(sineValue * 5, 0), child: child);
      },
      child: widget.child,
    );
  }
}
