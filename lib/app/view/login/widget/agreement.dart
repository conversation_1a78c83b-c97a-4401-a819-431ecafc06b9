import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/checkbox/msm_checkbox.dart';
import 'package:msmds_platform/widgets/highlight/highlight_text.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: agreement
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/15 10:25
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/15 10:25
/// @UpdateRemark: 更新说明

typedef OnChange = void Function(bool value);

class Agreement extends StatefulWidget {
  const Agreement({
    super.key,
    required this.onChange,
  });

  final OnChange onChange;

  @override
  AgreementState createState() => AgreementState();
}

class AgreementState extends State<Agreement> {
  bool state = false;

  /// keys点击
  void keysTap(String key) {
    if (key == '《用户协议》') {
      Navigator.pushNamed(
        context,
        CsRouter.webPage,
        arguments: [
          "",
          Constant.userServiceAgreement,
        ],
      );
    } else if (key == '《隐私政策》') {
      Navigator.pushNamed(
        context,
        CsRouter.webPage,
        arguments: [
          "",
          Constant.userAgreement,
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    List<String> content = [
      "未注册的手机号验证后将自动创建买什么都省账号，并且我已阅读并同意",
      "《用户协议》",
      "和",
      "《隐私政策》",
    ];
    return InkWell(
      onTap: () {
        setState(() {
          state = !state;
          widget.onChange.call(state);
        });
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(padding: EdgeInsets.only(right: 12)),
          MsmCheckbox(
            value: state,
            onChanged: (value) {
              setState(() {
                state = value;
              });
              widget.onChange.call(value);
            },
          ),
          const Padding(padding: EdgeInsets.only(right: 5)),
          Expanded(
            child: HighlightText(
              data: content,
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF999999),
              ),
              keyStyle: TextStyle(
                color: const Color(0xFFF93324),
                fontSize: 12.sp,
              ),
              keys: const [
                "《用户协议》",
                "《隐私政策》",
              ],
              onTapCallback: (String key) {
                keysTap(key);
              },
            ),
          ),
        ],
      ),
    );
  }
}
