/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: verification_box_cursor
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/21 11:35
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/21 11:35
/// @UpdateRemark: 更新说明
import 'package:flutter/material.dart';

///
/// des: 模拟光标
///
class VerificationBoxCursor extends StatefulWidget {
  const VerificationBoxCursor(
      {super.key,
      required this.color,
      required this.width,
      required this.indent,
      required this.endIndent});

  ///
  /// 光标颜色
  ///
  final Color color;

  ///
  /// 光标宽度
  ///
  final double width;

  ///
  /// 光标距离顶部距离
  ///
  final double indent;

  ///
  /// 光标距离底部距离
  ///
  final double endIndent;

  @override
  State<StatefulWidget> createState() => _VerificationBoxCursorState();
}

class _VerificationBoxCursorState extends State<VerificationBoxCursor>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    _controller =
        AnimationController(duration: const Duration(milliseconds: 500), vsync: this)
          ..addStatusListener((status) {
            if (status == AnimationStatus.completed) {
              _controller.reverse();
            } else if (status == AnimationStatus.dismissed) {
              _controller.forward();
            }
          });
    _controller.forward();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _controller,
      child: VerticalDivider(
        thickness: widget.width,
        color: widget.color,
        indent: widget.indent,
        endIndent: widget.endIndent,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
