import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MainSkeleton extends StatelessWidget {
  const MainSkeleton({super.key});

  Widget _buildItem(int index) {
    return Container(
      padding: EdgeInsets.fromLTRB(8, index == 0 ? 6 : 0, 8, 10),
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SizedBox(
                    height: 110.w,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          height: 15.h,
                          color: Colors.grey.withOpacity(0.1),
                        ),
                        Container(
                          margin: const EdgeInsets.only(right: 20),
                          height: 15.h,
                          color: Colors.grey.withOpacity(0.1),
                        ),
                        Container(
                          margin: const EdgeInsets.only(right: 80),
                          height: 15.h,
                          color: Colors.grey.withOpacity(0.1),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 100.w,
                              height: 15.h,
                              color: Colors.grey.withOpacity(0.1),
                            ),
                            Container(
                              width: 60.w,
                              height: 15.h,
                              color: Colors.grey.withOpacity(0.1),
                            )
                          ],
                        )
                      ],
                    ),
                  ),
                ),
                const Padding(padding: EdgeInsets.symmetric(horizontal: 5)),
                Container(
                  height: 104.w,
                  width: 104.w,
                  color: Colors.grey.withOpacity(0.1),
                ),
              ],
            ),
            SizedBox(height: 10.h,),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 70.w,
                      height: 15.h,
                      color: Colors.grey.withOpacity(0.1),
                    ),
                    SizedBox(width: 8.w,),
                    Container(
                      width: 70.w,
                      height: 15.h,
                      color: Colors.grey.withOpacity(0.1),
                    )
                  ],
                ),
                Row(
                  children: [
                    Container(
                      width: 60.w,
                      height: 15.h,
                      color: Colors.grey.withOpacity(0.1),
                    ),
                    SizedBox(width: 8.w,),
                    Container(
                      width: 60.w,
                      height: 15.h,
                      color: Colors.grey.withOpacity(0.1),
                    )
                  ],
                ),
              ],
            )
          ],
        ) ,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(10, (index) => _buildItem(index)).toList(),
    );
  }
}
