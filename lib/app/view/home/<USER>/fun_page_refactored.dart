import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_collection_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/fun_activity_tab_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/meituan_group_list_widget.dart';

import '../../../provider/fun/fun_list_provider.dart';
import '../../../repository/modals/fun/fun_tab_config.dart';

/// 重构后的 FunPage - 使用纯 Riverpod 状态管理
class FunPageRefactored extends ConsumerWidget {
  const FunPageRefactored({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      body: Stack(
        children: [
          // 背景图片
          Positioned.fill(
            child: Image.network(
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_bg.png',
              width: 375,
              height: 327,
              fit: BoxFit.cover,
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: _buildContent(context, ref),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.network(
            "https://alicdn.msmds.cn/APPSHOW/fun_activity_title_new.png",
            width: 131,
          ),
          Row(
            children: [
              Image.network(
                "https://alicdn.msmds.cn/APPSHOW/fun_activity_location.png",
                width: 11,
              ),
              const SizedBox(width: 5),
              const Text(
                "附近",
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, WidgetRef ref) {
    final tabConfigState = ref.watch(manageFunTabConfigProvider);

    return tabConfigState.when(
      data: (tabConfig) {
        if (tabConfig == null) {
          return const Center(child: Text('配置加载失败'));
        }
        return _buildContentWithTabConfig(context, ref, tabConfig);
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => _buildErrorState(ref),
    );
  }

  Widget _buildContentWithTabConfig(BuildContext context, WidgetRef ref, TabStateConfig tabConfig) {
    return CustomScrollView(
      slivers: [
        // 头部组件
        SliverToBoxAdapter(
          child: _buildHeaderComponents(),
        ),
        // 标签页组件
        SliverToBoxAdapter(
          child: FunActivityTabWidget(
            onSearch: () => _handleSearch(),
          ),
        ),
        // 动态内容区域
        _buildDynamicContent(context, ref, tabConfig),
      ],
    );
  }

  Widget _buildHeaderComponents() {
    return Column(
      children: [
        // 视频教程组件
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          alignment: Alignment.center,
          child: _buildVideoTutorial(),
        ),
        // 返现平台视图
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: const MallCollectionWidget(),
        ),
      ],
    );
  }

  Widget _buildVideoTutorial() {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Image.network(
            'https://alicdn.msmds.cn/APPSHOW/fun_activity_step.png',
            fit: BoxFit.contain,
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: GestureDetector(
            onTap: () {
              // TODO: 导航到介绍页面
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 5,
                vertical: 3,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(
                    'https://alicdn.msmds.cn/APPSHOW/fun_activity_play_tag.png',
                    width: 14,
                    height: 14,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(width: 3),
                  const Text(
                    '视频教程',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 根据标签配置动态构建内容
  Widget _buildDynamicContent(BuildContext context, WidgetRef ref, TabStateConfig tabConfig) {
    final tabSelection = ref.watch(tabSelectionStateProvider);
    final mainTabIndex = tabSelection.mainTabIndex;
    final childTabIndex = tabSelection.childTabIndex;

    // 验证索引有效性
    if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
        childTabIndex >= tabConfig.childrenTab.tabs.length) {
      return const SliverToBoxAdapter(
        child: Center(child: Text('标签配置错误')),
      );
    }

    final mainTab = tabConfig.mainTab.tabs[mainTabIndex];
    final childTab = tabConfig.childrenTab.tabs[childTabIndex];
    final currentSort = tabConfig.currentSort;

    // 根据主标签类型返回对应内容
    return _buildContentByTabType(
      context,
      mainTab.code ?? '',
      mainTab,
      childTab,
      currentSort,
    );
  }

  Widget _buildContentByTabType(
    BuildContext context,
    String tabCode,
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) {
    switch (tabCode) {
      case 'meituan_groupBuying':
      case 'meituan_sharpshooter':
        // 生成唯一的key来确保标签切换时重新创建widget
        final widgetKey = ValueKey('${mainTab.code}_${childTab.code}_${currentSort?.sortField ?? 'default'}');

        return SliverToBoxAdapter(
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.6, // 给定固定高度
            child: MeituanGroupListWithPaginationWidget(
              key: widgetKey,
              mainTabConfig: mainTab,
              childTabConfig: childTab,
              latitude: 39.9042, // TODO: 使用真实定位
              longitude: 116.4074,
              sortOption: currentSort,
              scrollController: PrimaryScrollController.of(context), // 传入外层的ScrollController
            ),
          ),
        );
      case 'douyin_groupBuying':
        return _buildDouyinContent();
      case 'bawangcan_sort':
        return _buildBawangcanContent();
      default:
        return _buildDefaultContent();
    }
  }

  Widget _buildDouyinContent() {
    return const SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Center(
          child: Text(
            '抖音团购功能开发中...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      ),
    );
  }

  Widget _buildBawangcanContent() {
    return const SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Center(
          child: Text(
            '霸王餐功能开发中...',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultContent() {
    return const SliverToBoxAdapter(
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Center(
          child: Text(
            '请选择标签查看内容',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(WidgetRef ref) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text('加载失败'),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => ref.invalidate(manageFunTabConfigProvider),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  void _handleSearch() {
    // TODO: 实现搜索功能
    debugPrint('Search clicked');
  }
}

/// 进一步优化的版本 - 使用 Consumer 局部响应状态变化
class FunPageOptimized extends ConsumerWidget {
  const FunPageOptimized({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      body: Stack(
        children: [
          // 背景图片
          Positioned.fill(
            child: Image.network(
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_bg.png',
              width: 375,
              height: 327,
              fit: BoxFit.cover,
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: CustomScrollView(
                    slivers: [
                      // 头部组件（不需要响应状态变化）
                      SliverToBoxAdapter(
                        child: _buildHeaderComponents(),
                      ),
                      // 标签页组件
                      const SliverToBoxAdapter(
                        child: FunActivityTabWidget(),
                      ),
                      // 动态内容区域（使用 Consumer 局部监听状态）
                      SingleChildScrollView(child: _buildDynamicContentConsumer()),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.network(
            "https://alicdn.msmds.cn/APPSHOW/fun_activity_title_new.png",
            width: 131,
          ),
          Row(
            children: [
              Image.network(
                "https://alicdn.msmds.cn/APPSHOW/fun_activity_location.png",
                width: 11,
              ),
              const SizedBox(width: 5),
              const Text(
                "附近",
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderComponents() {
    return Column(
      children: [
        // 视频教程组件
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          alignment: Alignment.center,
          child: _buildVideoTutorial(),
        ),
        // 返现平台视图
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: const MallCollectionWidget(),
        ),
      ],
    );
  }

  Widget _buildVideoTutorial() {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Image.network(
            'https://alicdn.msmds.cn/APPSHOW/fun_activity_step.png',
            fit: BoxFit.contain,
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: GestureDetector(
            onTap: () {
              // TODO: 导航到介绍页面
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(
                    'https://alicdn.msmds.cn/APPSHOW/fun_activity_play_tag.png',
                    width: 14,
                    height: 14,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(width: 3),
                  const Text(
                    '视频教程',
                    style: TextStyle(color: Colors.black, fontSize: 11),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 使用 Consumer 来局部监听状态变化，提高性能
  Widget _buildDynamicContentConsumer() {
    return Consumer(
      builder: (context, ref, child) {
        final tabConfigState = ref.watch(manageFunTabConfigProvider);
        final tabSelection = ref.watch(tabSelectionStateProvider);

        return tabConfigState.when(
          data: (tabConfig) {
            if (tabConfig == null) {
              return const SizedBox(
                height: 200,
                child: Center(child: Text('配置加载失败')),
              );
            }

            final mainTabIndex = tabSelection.mainTabIndex;
            final childTabIndex = tabSelection.childTabIndex;

            // 验证索引有效性
            if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
                childTabIndex >= tabConfig.childrenTab.tabs.length) {
              return const SizedBox(
                height: 200,
                child: Center(child: Text('标签配置错误')),
              );
            }

            final mainTab = tabConfig.mainTab.tabs[mainTabIndex];
            final childTab = tabConfig.childrenTab.tabs[childTabIndex];
            final currentSort = tabConfig.currentSort;

            return _buildContentByTabType(
              context,
              mainTab.code ?? '',
              mainTab,
              childTab,
              currentSort,
            );
          },
          loading: () => const SizedBox(
            height: 200,
            child: Center(child: CircularProgressIndicator()),
          ),
          error: (error, stackTrace) => SizedBox(
            height: 200,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('加载失败'),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () => ref.invalidate(manageFunTabConfigProvider),
                    child: const Text('重试'),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildContentByTabType(
    BuildContext context,
    String tabCode,
    FunTabConfig mainTab,
    FunTabConfig childTab,
    FunSortOption? currentSort,
  ) {
    switch (tabCode) {
      case 'meituan_groupBuying':
      case 'meituan_sharpshooter':
        // 生成唯一的key来确保标签切换时重新创建widget
        final widgetKey = ValueKey('${mainTab.code}_${childTab.code}_${currentSort?.sortField ?? 'default'}');

        return MeituanGroupListWithPaginationWidget(
          key: widgetKey,
          mainTabConfig: mainTab,
          childTabConfig: childTab,
          latitude: 39.9042, // TODO: 使用真实定位
          longitude: 116.4074,
          sortOption: currentSort,
          scrollController: PrimaryScrollController.of(context), // 传入外层的ScrollController
        );
      case 'douyin_groupBuying':
        return _buildPlaceholderContent('抖音团购功能开发中...');
      case 'bawangcan_sort':
        return _buildPlaceholderContent('霸王餐功能开发中...');
      default:
        return _buildPlaceholderContent('请选择标签查看内容');
    }
  }

  Widget _buildPlaceholderContent(String text) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, color: Colors.grey),
        ),
      ),
    );
  }
}