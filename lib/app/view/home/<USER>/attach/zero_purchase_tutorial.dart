import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

import '../../../../../utils/prefs_util.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: zero_purchase_tutorial
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/15 17:04
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/15 17:04
/// @UpdateRemark: 更新说明
class ZeroPurchaseTutorial {
  /// 0元购引导
  static void showAttach(BuildContext context) {
    SmartDialog.showAttach(
      targetContext: context,
      clickMaskDismiss: false,
      animationType: SmartAnimationType.fade,
      highlightBuilder: (Offset targetOffset, Size targetSize) {
        return Positioned(
          top: targetOffset.dy,
          left: targetOffset.dx,
          child: Container(
            height: targetSize.height,
            width: targetSize.width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
          ),
        );
      },
      builder: (context) {
        return Column(
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 59.w),
                  child: Image.asset(
                    guideTwo,
                    width: 191.w,
                    height: 113.h,
                  ),
                ),
              ],
            ),
            Padding(padding: EdgeInsets.only(bottom: 25.h)),
            InkWell(
              onTap: () {
                SmartDialog.dismiss();
                PrefsUtil().setBool(PrefsKeys.freeBuyKey, true);
              },
              child: Container(
                width: 146.w,
                height: 34.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: const Color(0xFFFE4511),
                  borderRadius: BorderRadius.circular(18.r),
                ),
                child: Text(
                  "知道了，马上领取",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            )
          ],
        );
      },
    );
  }
}
