import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/utils/prefs_util.dart';

import '../../../../../common/img/icon_addres.dart';
import '../../../../navigation/router.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: title_tutorial
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/15 16:12
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/15 16:12
/// @UpdateRemark: 更新说明
class TitleTutorial extends ConsumerStatefulWidget {
  const TitleTutorial({
    super.key,
    required this.scrollController,
  });

  final ScrollController scrollController;

  @override
  TitleTutorialState createState() => TitleTutorialState();
}

class TitleTutorialState extends ConsumerState<TitleTutorial> {
  @override
  void initState() {
    super.initState();
  }

  void _showAttach() {
    SmartDialog.showAttach(
      targetContext: context,
      clickMaskDismiss: false,
      animationType: SmartAnimationType.fade,
      highlightBuilder: (Offset targetOffset, Size targetSize) {
        return Positioned(
          top: targetOffset.dy,
          left: targetOffset.dx,
          child: Container(
            height: targetSize.height,
            width: targetSize.width,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
            ),
          ),
        );
      },
      builder: (context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 12.w),
              child: Image.asset(
                guideOne,
                width: 233.w,
                height: 106.h,
              ),
            ),
            Padding(padding: EdgeInsets.only(bottom: 25.h)),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    SmartDialog.dismiss();
                    PrefsUtil().setBool(PrefsKeys.freeBuyKey, true);
                  },
                  child: Container(
                    width: 78.w,
                    height: 32.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 0.6),
                      borderRadius: BorderRadius.circular(18.r),
                    ),
                    child: Text(
                      "跳过",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                Padding(padding: EdgeInsets.only(right: 33.w)),
                InkWell(
                  onTap: () async {
                    var addHeight = 0.0;
                    var remindNum = ref.read(
                      reminderGoodsListProvider
                          .select((value) => value.goodsNum),
                    );
                    if (remindNum != null && remindNum != 0) {
                      addHeight = (remindNum >= 2 ? 2 : 1) * 130.h;
                    }
                    await widget.scrollController.animateTo(
                      addHeight + 230.h,
                      duration: const Duration(microseconds: 500),
                      curve: Curves.linear,
                    );
                    await SmartDialog.dismiss();
                    ref
                        .read(freeBuyEntranceTutorialProvider.notifier)
                        .showTitleTutorial(true);
                  },
                  child: Container(
                    width: 78.w,
                    height: 32.h,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: const Color(0xFFFE4511),
                      border: Border.all(color: Colors.white, width: 0.6),
                      borderRadius: BorderRadius.circular(18.r),
                    ),
                    child: Text(
                      "下一步",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                Padding(padding: EdgeInsets.only(right: 42.w)),
              ],
            )
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    bool show = ref.watch(freeBuyTutorialProvider);
    if (show) {
      _showAttach();
      ref.invalidate(freeBuyTutorialProvider);
    }
    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          CsRouter.webPage,
          arguments: [
            "如何找券/返现",
            "https://ecoh5.szprize.cn/#/app/beginnerCourse",
          ],
        );
      },
      child: Image.asset(
        titleTutorial,
        width: 100.w,
        height: 30.h,
      ),
    );
  }
}
