import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/fun_dynamic_list_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_Collection_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/fun_activity_tab_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/meituan_group_list_widget.dart';

import '../../../repository/modals/fun/fun_tab_config.dart';
import '../../../provider/fun/fun_list_provider.dart';

/// 重构后的 FunPage - 使用纯 Riverpod 状态管理
class FunPage extends ConsumerStatefulWidget {
  const FunPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FunPageState();
}

class _FunPageState extends ConsumerState<FunPage> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _tabWidgetKey = GlobalKey();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print("build FunPage！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！");
    return Scaffold(
      backgroundColor: const Color(0xFFF4F4F2),
      body: Stack(
        children: [
          // 背景图片
          Positioned(
            child: Image.network(
              'https://alicdn.msmds.cn/APPSHOW/fun_activity_bg.png',
              width: double.infinity,
              height: 370,
              fit: BoxFit.cover,
            ),
          ),
          SafeArea(
            child: PrimaryScrollController(
              controller: _scrollController,
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                SliverToBoxAdapter(
                  child:_buildHeader(),
                ),
                // 头部组件（视频教程 + 返现平台）
                SliverToBoxAdapter(
                  child: _buildHeaderComponents(),
                ),
                // 使用动态高度的SliverPersistentHeader
                // const DynamicSliverPersistentHeader(),
                SliverToBoxAdapter(
                  child: FunActivityTabWidget(key: _tabWidgetKey,),
                ),
                const FunDynamicListWidget(),
              ],
              ),
            ),
          ),
        ],
      ),
    );
  }

 

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.network(
            "https://alicdn.msmds.cn/APPSHOW/fun_activity_title_new.png",
            width: 131,
          ),
          GestureDetector(
            onTap: () {
              final RenderBox? renderBox = _tabWidgetKey.currentContext!.findRenderObject() as RenderBox?;
              if (renderBox != null && mounted) {
                final newHeight = renderBox.size.height;
                print('TabWidget height: $newHeight');
              }
            },
            child: Row(
              children: [
                Image.network(
                  "https://alicdn.msmds.cn/APPSHOW/fun_activity_location.png",
                  width: 11,
                ),
                const SizedBox(width: 5),
                const Text(
                  "附近",
                  style: TextStyle(color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderComponents() {
    print("rebuild header components");
    return Column(
      children: [
        // 视频教程组件
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          alignment: Alignment.center,
          child: _buildVideoTutorial(),
        ),
        // 返现平台视图
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 10),
          child: const MallCollectionWidget(),
        ),
      ],
    );
  }

  Widget _buildVideoTutorial() {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.all(10),
          alignment: Alignment.center,
          child: Image.network(
            'https://alicdn.msmds.cn/APPSHOW/fun_activity_step.png',
            fit: BoxFit.contain,
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: GestureDetector(
            onTap: () {
              // TODO: 导航到介绍页面
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.network(
                    'https://alicdn.msmds.cn/APPSHOW/fun_activity_play_tag.png',
                    width: 14,
                    height: 14,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(width: 3),
                  const Text(
                    '视频教程',
                    style: TextStyle(color: Colors.black, fontSize: 11),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }



  /// 根据标签类型构建对应的 CustomListView
  // Widget _buildListViewByTabType(
  //   String tabCode,
  //   FunTabConfig mainTab,
  //   FunTabConfig childTab,
  //   FunSortOption? currentSort,
  // ) {
  //   switch (tabCode) {
  //     case 'meituan_groupBuying':
  //     case 'meituan_sharpshooter':
  //       return _buildMeituanListViewWithParams(mainTab, childTab, currentSort);
  //     case 'douyin_groupBuying':
  //       return _buildPlaceholderListView('抖音团购功能开发中...');
  //     case 'bawangcan_sort':
  //       return _buildPlaceholderListView('霸王餐功能开发中...');
  //     default:
  //       return _buildPlaceholderListView('请选择标签查看内容');
  //   }
  // }

  // /// 构建美团列表视图 - 用于 CustomScrollView 中的 Sliver
  // Widget _buildMeituanListView(WidgetRef ref) {
  //   final tabConfigState = ref.watch(manageFunTabConfigProvider);
  //   final tabSelection = ref.watch(tabSelectionStateProvider);
    
  //   return tabConfigState.when(
  //     data: (tabConfig) {
  //       if (tabConfig == null) {
  //         return SliverToBoxAdapter(
  //           child: _buildErrorWidget('标签配置加载失败'),
  //         );
  //       }

  //       // 获取当前选中的主标签和子标签
  //       final mainTabIndex = tabSelection.mainTabIndex;
  //       final childTabIndex = tabSelection.childTabIndex;
  //       print("当前标签索引: $mainTabIndex, $childTabIndex");

  //       if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
  //           childTabIndex >= tabConfig.childrenTab.tabs.length) {
  //         return SliverToBoxAdapter(
  //           child: _buildErrorWidget('标签索引超出范围'),
  //         );
  //       }

  //       final currentMainTab = tabConfig.mainTab.tabs[mainTabIndex];
  //       final currentChildTab = tabConfig.childrenTab.tabs[childTabIndex];
  //       final currentSort = tabConfig.currentSort;
  //       print("当前标签信息${currentMainTab.code} ${currentSort?.sortName}");

  //       // 检查是否是美团相关的标签
  //       final tabCode = currentMainTab.code ?? '';
  //       print('tabCode: $tabCode ');
  //       if (tabCode == 'meituan_groupBuying' || tabCode == 'meituan_sharpshooter') {
  //         // 生成唯一的key来确保标签切换时重新创建widget
  //         final widgetKey = ValueKey('${currentMainTab.code}_${currentChildTab.code}_${currentSort?.sortField ?? 'default'}');

  //         return MeituanGroupListWithPaginationWidget(
  //           key: widgetKey,
  //           mainTabConfig: currentMainTab,
  //           childTabConfig: currentChildTab,
  //           latitude: 39.9042, // TODO: 使用真实定位数据
  //           longitude: 116.4074, // TODO: 使用真实定位数据
  //           sortOption: currentSort,
  //           scrollController: PrimaryScrollController.of(context), // 传入外层的ScrollController
  //         );
  //       } else {
  //         // 非美团标签显示占位符
  //         return SliverToBoxAdapter(
  //           child: _buildPlaceholderContent('请选择美团相关标签查看内容'),
  //         );
  //       }
  //     },
  //     loading: () => SliverToBoxAdapter(
  //       child: Container(
  //         height: 200,
  //         alignment: Alignment.center,
  //         child: const CircularProgressIndicator(),
  //       ),
  //     ),
  //     error: (error, stackTrace) => SliverToBoxAdapter(
  //       child: _buildErrorWidget('加载失败: $error'),
  //     ),
  //   );
  // }

  // /// 带参数的美团列表视图构建方法 - 用于 _buildListViewByTabType
  // Widget _buildMeituanListViewWithParams(FunTabConfig mainTab, FunTabConfig childTab, FunSortOption? currentSort) {
  //   // 生成唯一的key来确保标签切换时重新创建widget
  //   final widgetKey = ValueKey('${mainTab.code}_${childTab.code}_${currentSort?.sortField ?? 'default'}');

  //   return SliverToBoxAdapter(
  //     child: SizedBox(
  //       height: MediaQuery.of(context).size.height * 0.6,
  //       child: MeituanGroupListWithPaginationWidget(
  //         key: widgetKey,
  //         mainTabConfig: mainTab,
  //         childTabConfig: childTab,
  //         latitude: 39.9042, // TODO: 使用真实定位数据
  //         longitude: 116.4074, // TODO: 使用真实定位数据
  //         sortOption: currentSort,
  //         scrollController: PrimaryScrollController.of(context), // 传入外层的ScrollController
  //       ),
  //     ),
  //   );
  // }


  // /// 构建占位符列表视图
  // Widget _buildPlaceholderListView(String message) {
  //   return SliverToBoxAdapter(
  //     child: _buildPlaceholderContent(message),
  //   );  
  // }

  // /// 构建错误提示组件
  // Widget _buildErrorWidget(String message) {
  //   return Container(
  //     margin: EdgeInsets.only(top: 80.h),
  //     alignment: Alignment.center,
  //     child: Text(
  //       message,
  //       style: TextStyle(
  //         fontSize: 14.sp,
  //         color: const Color(0xFF999999),
  //       ),
  //     ),
  //   );
  // }



  // /// 加载更多美团数据
  // Future<void> _loadMoreMeituanData(
  //   WidgetRef ref,
  //   FunTabConfig mainTab,
  //   FunTabConfig childTab,
  //   FunSortOption? currentSort,
  // ) async {
  //   // TODO: 实现分页加载逻辑
  //   // 这里需要扩展 fetchMeituanCouponListProvider 支持分页
  //   debugPrint('加载更多美团数据');
  // }

  /// 处理美团分享
  // void _handleMeituanShare(dynamic item) {
  //   // TODO: 实现分享逻辑
  //   debugPrint('分享美团商品: ${item.goodsName}');
  // }

  // /// 处理美团购买
  // void _handleMeituanBuy(dynamic item) {
  //   // TODO: 实现购买逻辑
  //   debugPrint('购买美团商品: ${item.goodsName}');
  // }

  // Widget _buildPlaceholderContent(String text) {
  //   return Padding(
  //     padding: const EdgeInsets.all(20),
  //     child: Center(
  //       child: Text(
  //         text,
  //         style: const TextStyle(fontSize: 16, color: Colors.grey),
  //       ),
  //     ),
  //   );
  // }
}

/// 动态高度的SliverPersistentHeader组件
class DynamicSliverPersistentHeader extends ConsumerStatefulWidget {
  const DynamicSliverPersistentHeader({super.key});

  @override
  ConsumerState<DynamicSliverPersistentHeader> createState() => _DynamicSliverPersistentHeaderState();
}

class _DynamicSliverPersistentHeaderState extends ConsumerState<DynamicSliverPersistentHeader> {
  final GlobalKey _tabWidgetKey = GlobalKey();
  double _tabWidgetHeight = 160.0; // 默认高度
  bool _isDataLoaded = false;

  @override
  void initState() {
    super.initState();
    // 延迟检查初始数据状态
    Future.delayed(Duration(seconds: 1), () {
      _checkDataLoadedState();
    });
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _checkDataLoadedState();
    // });
  }

  void _checkDataLoadedState() {
    final tabState = ref.read(manageFunTabConfigProvider);
    if (tabState is AsyncData && tabState.value != null) {
      setState(() {
        _isDataLoaded = true;
      });
      _updateTabWidgetHeight();
    }
  }

  // 计算并更新TabWidget高度的方法
  void _updateTabWidgetHeight() {
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    Future.delayed(Duration(seconds: 1), () {
      if (_tabWidgetKey.currentContext != null) {
        final RenderBox? renderBox = _tabWidgetKey.currentContext!.findRenderObject() as RenderBox?;
        print('TabWidget height: ${renderBox?.size.height}');
        if (renderBox != null && renderBox.hasSize) {
          print('TabWidget height2: ${renderBox.size.height}');
          final newHeight = renderBox.size.height;
          if (newHeight != _tabWidgetHeight && newHeight > 0) {
            print('TabWidget height3: ${renderBox.size.height}');
            if (mounted) {
              print('TabWidget height4: ${renderBox.size.height}');
              setState(() {
                _tabWidgetHeight = newHeight;
              });
            }
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final tabState = ref.watch(manageFunTabConfigProvider);
    print("build DynamicSliverPersistentHeader！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！");

    // 监听标签数据状态变化
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider, (previous, next) {
      if (next is AsyncData && next.value != null && !_isDataLoaded) {
        print("监听标签数据状态变化 $_isDataLoaded");
        setState(() {
          _isDataLoaded = true;
        });
        _updateTabWidgetHeight();
      }
    });

    // 监听标签选择状态变化，重新计算高度
    ref.listen<TabSelection>(tabSelectionStateProvider, (previous, next) {
      print("监听标签选择状态变化 $_isDataLoaded");
      if (_isDataLoaded) {
        _updateTabWidgetHeight();
      }
    });

    // 如果数据还未加载完成，显示loading状态
    if (!_isDataLoaded || tabState is AsyncLoading) {
      return SliverToBoxAdapter(
        child: Container(
          height: 180,
          alignment: Alignment.center,
          child: const CircularProgressIndicator(),
        ),
      );
    }

    // 如果加载出错，显示错误状态
    if (tabState is AsyncError) {
      return SliverToBoxAdapter(
        child: Container(
          height: 100,
          alignment: Alignment.center,
          child: const Text("加载失败"),
        ),
      );
    }

    // 正常情况下显示SliverPersistentHeader
    return SliverPersistentHeader(
      pinned: true, // 固定在顶部
      delegate: _SliverAppBarDelegate(
        minHeight: _tabWidgetHeight,
        maxHeight: _tabWidgetHeight,
        child: LayoutBuilder(
          builder: (context, constraints) {
            print('constraints: ${constraints.maxHeight}');
            return Container(
              key: _tabWidgetKey,
              child: FunActivityTabWidget(),
            );
          }
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    // 当任何关键属性发生变化时重建
    return minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight ||
        child != oldDelegate.child;
  }
}
