import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: task_content
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 15:37
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 15:37
/// @UpdateRemark: 更新说明
class TaskContent extends StatelessWidget {
  const TaskContent({
    super.key,
    required this.title,
    required this.index,
    required this.length,
  });

  final String title;

  final int index;

  final int length;

  Widget _buildTitle() {
    return Container(
      padding: EdgeInsets.only(top: 10.h),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F5F5),
      ),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        padding: EdgeInsets.fromLTRB(10.w, 15.h, 0, 6.h),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16.sp,
            color: const Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  Widget _buildItem() {
    return Container(
      color: const Color(0xFFF5F5F5),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular((index + 1 == length) ? 10 : 0),
            bottomRight: Radius.circular((index + 1 == length) ? 10 : 0),
          ),
        ),
        child: Column(
          children: [
            Padding(padding: EdgeInsets.only(top: 12.h)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 45.w,
                      height: 45.h,
                      color: Colors.grey,
                    ),
                    Padding(padding: EdgeInsets.only(right: 10.w)),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "逛天天一元起会场",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF333333),
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 3.h)),
                        Text(
                          "+20积分",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFE88D00),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                GradientButton(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 5.h),
                  radius: 20,
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFF2269),
                      Color(0xFFFF0000),
                      Color(0xFFFF0000),
                      Color(0xFFFF7038),
                      Color(0xFFFFC52C),
                    ],
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                  ),
                  child: Text(
                    "逛一逛",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            Padding(padding: EdgeInsets.only(top: 12.h)),
            Divider(
              indent: 55.w,
              color: const Color(0xFFF2F2F2),
              height: 1,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (index == 0) {
      return _buildTitle();
    }
    return _buildItem();
  }
}
