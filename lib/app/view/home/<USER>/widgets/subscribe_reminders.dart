import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/repository/modals/reminder/reminder.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/reminder_delete_dialog.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/reminder_no_commission_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../../provider/conversion/link_conversion_provider.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: subscribe_reminders
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 11:55
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 11:55
/// @UpdateRemark: 更新说明
class SubscribeReminders extends ConsumerWidget {
  const SubscribeReminders({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var reminders = ref.watch(
      reminderGoodsListProvider.select((value) => value.reminders),
    );
    if (reminders == null || reminders.isEmpty) {
      return Container();
    }
    return Container(
      width: 355.w,
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        gradient: const LinearGradient(
          colors: [
            Color(0xFFFFEEE1),
            Color(0xFFFFF8F8),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Stack(
        children: [
          Image.asset(
            reminderTitleBg,
            width: 355.w,
            fit: BoxFit.fitWidth,
          ),
          Column(
            children: [
              Container(
                padding: EdgeInsets.fromLTRB(18.w, 4.h, 18.w, 2.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          "下单通知",
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF793C1A),
                          ),
                        ),
                        SwitchTheme(
                          data: SwitchThemeData(
                            splashRadius: 0,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            thumbColor: MaterialStateProperty.resolveWith(
                              (states) {
                                return Colors.white;
                              },
                            ),
                            trackColor: MaterialStateProperty.resolveWith(
                              (states) {
                                if (states.contains(MaterialState.selected)) {
                                  return const Color(0xFFF2270C); // 选中时滑道颜色
                                }
                                return const Color(0xFFDED8DA); // 默认滑道颜色
                              },
                            ),
                          ),
                          child: Switch(
                            value: ref.watch(
                              reminderGoodsListProvider
                                  .select((value) => value.notice),
                            ),
                            onChanged: (value) {
                              ref
                                  .read(reminderGoodsListProvider.notifier)
                                  .setNotice(value);
                            },
                          ),
                        ),
                      ],
                    ),
                    Badge(
                      backgroundColor: const Color(0xFFF93324),
                      label: Text(
                        "${ref.watch(
                          reminderGoodsListProvider
                              .select((value) => value.goodsNum),
                        )}",
                        style: TextStyle(
                          fontSize: 9.sp,
                          color: Colors.white,
                        ),
                      ),
                      child: Text(
                        "商品数",
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF793C1A),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        ReminderDeleteDialog.reminderDeleteDialog(
                          "是否全部清除",
                          () {
                            ref
                                .read(saveReminderProvider.notifier)
                                .cleanAllReminder();
                          },
                        );
                      },
                      child: Row(
                        children: [
                          Image.asset(
                            cleanReminder,
                            width: 13.w,
                            height: 14.h,
                          ),
                          SizedBox(
                            width: 2.w,
                          ),
                          Text(
                            "全部清除",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: const Color(0xFF793C1A),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                physics: const NeverScrollableScrollPhysics(),
                itemCount: reminders.length,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return ReminderItemWidget(reminder: reminders[index]);
                },
              ),
              const ReminderFooterWidget(),
            ],
          )
        ],
      ),
    );
  }
}

class ReminderFooterWidget extends ConsumerWidget {
  const ReminderFooterWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var reminders = ref.watch(
      reminderGoodsListProvider.select((value) => value.reminders),
    );
    var footerState = ref.watch(
      reminderGoodsListProvider.select((value) => value.loadState),
    );
    switch (footerState) {
      case LoadState.noMore:
        if (reminders == null || reminders.length <= 2) {
          return Container();
        } else {
          return InkWell(
            onTap: () {
              ref.read(reminderGoodsListProvider.notifier).loadData();
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "收起",
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: const Color(0xFF793C1A),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_up_rounded,
                  color: const Color(0xFFB28C78),
                  size: 15.r,
                ),
              ],
            ),
          );
        }
      case LoadState.loading:
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: const CircularProgressIndicator(
                strokeWidth: 1,
                color: Color(0xFFF94B3D),
              ),
            )
          ],
        );
      case LoadState.idle:
        return InkWell(
          onTap: () {
            ref.read(reminderGoodsListProvider.notifier).loadMore();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "查看更多",
                style: TextStyle(
                  fontSize: 11.sp,
                  color: const Color(0xFF793C1A),
                ),
              ),
              Icon(
                Icons.keyboard_arrow_down_rounded,
                color: const Color(0xFFB28C78),
                size: 15.r,
              ),
            ],
          ),
        );
      case null:
      case LoadState.fail:
        return Container();
    }
  }
}

class ReminderItemWidget extends ConsumerStatefulWidget {
  const ReminderItemWidget({
    super.key,
    this.reminder,
  });

  final Reminder? reminder;

  @override
  ReminderItemWidgetState createState() => ReminderItemWidgetState();
}

class ReminderItemWidgetState extends ConsumerState<ReminderItemWidget> {
  Timer? _timer;
  late Duration diff;

  @override
  void initState() {
    super.initState();
    if (widget.reminder != null && widget.reminder?.createTime != null) {
      /// 创建时间
      var createTime = DateTime.parse(widget.reminder!.createTime!);

      /// 加上2个小时
      var increase = createTime.add(const Duration(hours: 2));
      diff = increase.difference(DateTime.now());

      /// 开启倒计时
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        /// 计算与当前时间的时间差
        var difference = increase.difference(DateTime.now());
        if (difference.inSeconds == 0) {
          timer.cancel();
        }
        setState(() {
          diff = diff - const Duration(seconds: 1);
        });
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    _timer?.cancel();
    _timer = null;
  }

  /// item点击
  void _goodsItemClick() {
    if (diff.inSeconds <= 0) {
      _goodsChange();
    } else {
      /// 未到时间
      ReminderNoCommissionDeleteDialog.reminderNoCommissionDialog(_goodsChange);
    }
  }

  /// 商品转链接
  void _goodsChange() {
    ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
          widget.reminder?.goodsType,
          materialId: widget.reminder?.completeGoodsId,
          goodsSign: widget.reminder?.completeGoodsId,
          goodsId: widget.reminder?.completeGoodsId,
        );
  }

  /// 优惠券返利信息
  Widget _buildCouponInfo() {
    List<Widget> child = [];
    if (widget.reminder != null && widget.reminder?.couponInfo != null) {
      child.add(
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFF93324)),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                decoration: const BoxDecoration(
                  color: Color(0xFFF93324),
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Text(
                  "券",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 3.w)),
              Text(
                "${widget.reminder?.couponInfo}",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFFF93324),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 5.w)),
            ],
          ),
        ),
      );
    }
    child.add(
      Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFF93324)),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Text(
          "约返${widget.reminder?.userCommission}元",
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFFF93324),
          ),
        ),
      ),
    );
    return Wrap(
      spacing: 4.w,
      runSpacing: 4.h,
      children: child,
    );
  }

  /// 价格显示
  Widget _buildPrice() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF999999),
        ),
        children: [
          TextSpan(
            text: "${widget.reminder?.receivedPrice}元",
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFFF93324),
            ),
          ),
          WidgetSpan(
            child: Padding(
              padding: EdgeInsets.only(right: 4.w),
            ),
          ),
          TextSpan(
            text: "¥${widget.reminder?.originalPrice}",
            style: const TextStyle(
              decoration: TextDecoration.lineThrough,
              decorationThickness: 1.2,
              decorationColor: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 倒计时组件
  Widget _buildTimerWidget() {
    Widget child;
    String? title;
    if (diff.inSeconds <= 0) {
      title = "领返现";
      child = Container(
        width: 65.w,
        height: 24.h,
        margin: EdgeInsets.only(left: 5.w, right: 3.w),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: const Color(0xFFF94B3D),
          borderRadius: BorderRadius.circular(11.r),
        ),
        child: Text(
          "去下单",
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFFFFFFFF),
          ),
        ),
      );
    } else {
      title = "可返现";
      child = SubCountdown(
        duration: diff,
      );
    }
    return Column(
      children: [
        child,
        SizedBox(
          height: 5.h,
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: const Color(0xFFF93324),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _goodsItemClick,
      child: Container(
        margin: EdgeInsets.only(bottom: 10.h),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(8.w, 8.h, 11.w, 8.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4.r),
                    child: Image.network(
                      widget.reminder?.cover ?? "",
                      width: 78.w,
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                  SizedBox(
                    width: 6.w,
                  ),
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      "${widget.reminder?.goodsName}",
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF333333),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              _buildCouponInfo(),
                              SizedBox(
                                height: 10.h,
                              ),
                              _buildPrice(),
                            ],
                          ),
                        ),
                        _buildTimerWidget(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              right: -3.w,
              top: -6.h,
              child: InkWell(
                onTap: () {
                  ReminderDeleteDialog.reminderDeleteDialog(
                    "是否清除该商品订阅记录",
                    () {
                      if (widget.reminder != null &&
                          widget.reminder?.id != null) {
                        ref
                            .read(saveReminderProvider.notifier)
                            .cleanReminder(widget.reminder!.id!);
                      }
                    },
                  );
                },
                child: Container(
                  padding: EdgeInsets.all(3.w),
                  child: Image.network(
                    delete,
                    width: 18.w,
                    height: 18.w,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 倒计时显示
class SubCountdown extends ConsumerWidget {
  const SubCountdown({
    super.key,
    required this.duration,
  });

  final Duration duration;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    int hours = duration.inHours % 24;
    int minute = duration.inMinutes % 60;
    int seconds = duration.inSeconds % 60;
    String hoursStr = hours >= 10 ? "$hours" : "0$hours";
    String minuteStr = minute >= 10 ? "$minute" : "0$minute";
    String secondsStr = seconds >= 10 ? "$seconds" : "0$seconds";
    return Container(
      width: 73.w,
      height: 20.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: const Color(0xFFFFECEC),
        borderRadius: BorderRadius.circular(11.r),
      ),
      child: Text(
        "$hoursStr:$minuteStr:$secondsStr后",
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFFF93324),
        ),
      ),
    );
  }
}
