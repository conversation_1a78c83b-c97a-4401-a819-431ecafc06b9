import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout_gift_item.dart';

import '../../../../../common/img/icon_addres.dart';
import '../../../../navigation/router.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: exchange
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 14:59
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 14:59
/// @UpdateRemark: 更新说明
class Exchange extends StatelessWidget {
  const Exchange({
    super.key,
    this.signLayout,
  });

  final SignLayout? signLayout;

  Widget _buildItem(
    BuildContext context,
    SignLayoutGiftItem? signLayoutGiftItem,
  ) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, CsRouter.myIntegralPage);
      },
      child: Column(
        children: [
          Image.network(
            "${signLayoutGiftItem?.pic}",
            width: (MediaQuery.of(context).size.width - 70.w) / 4,
            height: (MediaQuery.of(context).size.width - 70.w) / 4,
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                signCoin,
                width: 10.w,
                height: 10.w,
              ),
              Text(
                "${signLayoutGiftItem?.integral}",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFFF93324),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var redPacketList = signLayout?.redPacketList;
    if (redPacketList == null || redPacketList.isEmpty) {
      return const SizedBox();
    }
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text(
                      "积分兑红包",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    Text(
                      "每天0点更新，先到先得",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF9C9C9C),
                      ),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    Navigator.pushNamed(context, CsRouter.myIntegralPage);
                  },
                  child: Text(
                    "更多",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF9C9C9C),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            child: Wrap(
              spacing: 10.w,
              runSpacing: 12.h,
              children:
                  redPacketList.map((e) => _buildItem(context, e)).toList(),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 7.h)),
        ],
      ),
    );
  }
}
