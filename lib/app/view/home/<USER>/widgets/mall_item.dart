import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: mall_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 11:05
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 11:05
/// @UpdateRemark: 更新说明
class MallItem extends StatelessWidget {
  const MallItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10.w),
      padding: EdgeInsets.fromLTRB(12.w, 12.h, 16.w, 12.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: <PERSON>(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                color: Colors.grey,
              ),
              Padding(padding: EdgeInsets.only(right: 8.w)),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '电影票',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF000000),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 4.h)),
                  Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: const Color(0xFF333333),
                      ),
                      children: const [
                        TextSpan(text: "最高"),
                        TextSpan(
                          text: "返8%",
                          style: TextStyle(
                            color: Color(0xFFFF103A),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          GradientButton(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            radius: 12,
            shadow: false,
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFF1864),
                Color(0xFFFE0D2D),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            child: Text(
              "立即前往",
              style: TextStyle(fontSize: 14.sp, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
