import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/fun/fun_list_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_meituan_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';
import 'meituan_group_item_widget.dart';

class MeituanGroupListWidget extends ConsumerWidget {
  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;

  const MeituanGroupListWidget({
    super.key,
    required this.mainTabConfig,
    required this.childTabConfig,
    this.latitude,
    this.longitude,
    this.sortOption,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 fetchMeituanCouponList provider 获取数据
    final meituanCouponProvider = fetchMeituanCouponListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
    );

    final couponState = ref.watch(meituanCouponProvider);

    return couponState.when(
      data: (response) {
        if (response.data?.list == null || response.data!.list!.isEmpty) {
          return const Center(
            child: Text(
              '暂无数据',
              style: TextStyle(
                color: Color(0xFF999999),
                fontSize: 16,
              ),
            ),
          );
        }

        return _buildCouponList(response.data!.list!);
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '加载失败',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  ref.invalidate(meituanCouponProvider);
                },
                child: const Text('重试'),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCouponList(List<MeituanCouponItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return MeituanGroupItemWidget(
          item: item,
          index: index,
          onShareTap: () => _handleShare(item),
          onBuyTap: () => _handleBuy(item),
        );
      },
    );
  }

  void _handleShare(MeituanCouponItem item) {
    // TODO: 实现分享逻辑
    debugPrint('分享商品: ${item.goodsName}');
  }

  void _handleBuy(MeituanCouponItem item) {
    // TODO: 实现购买逻辑
    debugPrint('购买商品: ${item.goodsName}');
  }
}

/// 带有分页功能的美团团购列表组件
class MeituanGroupListWithPaginationWidget extends ConsumerStatefulWidget {
  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;

  const MeituanGroupListWithPaginationWidget({
    super.key,
    required this.mainTabConfig,
    required this.childTabConfig,
    this.latitude,
    this.longitude,
    this.sortOption,
  });

  @override
  ConsumerState<MeituanGroupListWithPaginationWidget> createState() =>
      _MeituanGroupListWithPaginationWidgetState();
}

class _MeituanGroupListWithPaginationWidgetState
    extends ConsumerState<MeituanGroupListWithPaginationWidget> {
  final ScrollController _scrollController = ScrollController();
  final List<MeituanCouponItem> _allItems = [];
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _currentPage = 1;
      _allItems.clear();
    });

    await _loadPage(1);
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    await _loadPage(_currentPage + 1);
  }

  Future<void> _loadPage(int page) async {
    try {
      final provider = fetchMeituanCouponListProvider(
        widget.mainTabConfig,
        widget.childTabConfig,
        latitude: widget.latitude,
        longitude: widget.longitude,
        pageNo: page,
        sortOption: widget.sortOption,
      );

      final response = await ref.read(provider.future);

      if (response.data?.list != null) {
        setState(() {
          if (page == 1) {
            _allItems.clear();
          }
          _allItems.addAll(response.data!.list!);
          _currentPage = page;
          _hasMore = response.data!.hasMore;
        });
      }
    } catch (e) {
      debugPrint('加载数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_allItems.isEmpty && _isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_allItems.isEmpty && !_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              '暂无数据',
              style: TextStyle(
                color: Color(0xFF999999),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: _loadInitialData,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _allItems.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _allItems.length) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text(
                      '没有更多数据了',
                      style: TextStyle(
                        color: Color(0xFF999999),
                        fontSize: 14,
                      ),
                    ),
            ),
          );
        }

        final item = _allItems[index];
        return MeituanGroupItemWidget(
          item: item,
          index: index,
          onShareTap: () => _handleShare(item),
          onBuyTap: () => _handleBuy(item),
        );
      },
    );
  }

  void _handleShare(MeituanCouponItem item) {
    // TODO: 实现分享逻辑
    debugPrint('分享商品: ${item.goodsName}');
  }

  void _handleBuy(MeituanCouponItem item) {
    // TODO: 实现购买逻辑
    debugPrint('购买商品: ${item.goodsName}');
  }
}