import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/fun/fun_list_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_meituan_response.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';
import 'meituan_group_item_widget.dart';
import 'share_bottom_sheet.dart';

class MeituanGroupListWidget extends ConsumerWidget {
  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;

  const MeituanGroupListWidget({
    super.key,
    required this.mainTabConfig,
    required this.childTabConfig,
    this.latitude,
    this.longitude,
    this.sortOption,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 fetchMeituanCouponList provider 获取数据
    final meituanCouponProvider = fetchMeituanCouponListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      sortOption: sortOption,
    );

    final couponState = ref.watch(meituanCouponProvider);

    return couponState.when(
      data: (response) {
        if (response.data?.list == null || response.data!.list!.isEmpty) {
          return const Center(
            child: Text(
              '暂无数据',
              style: TextStyle(
                color: Color(0xFF999999),
                fontSize: 16,
              ),
            ),
          );
        }

        return _buildCouponList(response.data!.list!);
      },
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '加载失败',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  ref.invalidate(meituanCouponProvider);
                },
                child: const Text('重试'),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCouponList(List<MeituanCouponItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return MeituanGroupItemWidget(
          item: item,
          index: index,
          onShareTap: () => _handleShare(context, item),
          onBuyTap: () => _handleBuy(item),
        );
      },
    );
  }

  void _handleShare(BuildContext context, MeituanCouponItem item) {
    debugPrint('分享商品: ${item.goodsName}');
    // 显示分享底部弹窗
    ShareBottomSheet.show(context, '分享商品: ${item.goodsName}');
  }

  void _handleBuy(MeituanCouponItem item) {
    // TODO: 实现购买逻辑
    debugPrint('购买商品: ${item.goodsName}');
  }
}

/// 带有分页功能的美团团购列表组件
class MeituanGroupListWithPaginationWidget extends ConsumerStatefulWidget {
  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final FunSortOption? sortOption;
  final ScrollController? scrollController; // 新增：外部传入的滚动控制器

  const MeituanGroupListWithPaginationWidget({
    super.key,
    required this.mainTabConfig,
    required this.childTabConfig,
    this.latitude,
    this.longitude,
    this.sortOption,
    this.scrollController, // 新增参数
  });

  @override
  ConsumerState<MeituanGroupListWithPaginationWidget> createState() =>
      _MeituanGroupListWithPaginationWidgetState();
}

class _MeituanGroupListWithPaginationWidgetState
    extends ConsumerState<MeituanGroupListWithPaginationWidget> {
  ScrollController? _scrollController;
  final List<MeituanCouponItem> _allItems = [];
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;
  String? _lastTabKey; // 用于检测标签切换

  @override
  void initState() {
    super.initState();
    _setupScrollController();
    _loadInitialData();
  }

  @override
  void didUpdateWidget(MeituanGroupListWithPaginationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检测标签是否发生变化
    final currentTabKey = _generateTabKey();
    if (_lastTabKey != null && _lastTabKey != currentTabKey) {
      _loadInitialData();
    }

    // 重新设置滚动控制器
    if (oldWidget.scrollController != widget.scrollController) {
      _setupScrollController();
    }
  }

  @override
  void dispose() {
    _scrollController?.removeListener(_onScroll);
    // 注意：不要dispose外部传入的scrollController
    super.dispose();
  }

  void _setupScrollController() {
    // 移除旧的监听器
    _scrollController?.removeListener(_onScroll);

    // 使用外部传入的scrollController，如果没有则创建新的
    _scrollController = widget.scrollController;

    // 添加滚动监听器
    _scrollController?.addListener(_onScroll);
  }

  String _generateTabKey() {
    return '${widget.mainTabConfig.code}_${widget.childTabConfig.tabName}_${widget.sortOption?.sortField ?? ''}';
  }

  void _onScroll() {
    final controller = _scrollController;
    if (controller != null &&
        controller.position.pixels >=
            controller.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _currentPage = 1;
      _allItems.clear();
    });

    // 更新当前标签key
    _lastTabKey = _generateTabKey();

    await _loadPage(1);
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    await _loadPage(_currentPage + 1);
  }

  Future<void> _loadPage(int page) async {
    try {
      final provider = fetchMeituanCouponListProvider(
        widget.mainTabConfig,
        widget.childTabConfig,
        latitude: widget.latitude,
        longitude: widget.longitude,
        pageNo: page,
        sortOption: widget.sortOption,
      );

      final response = await ref.read(provider.future);

      if (response.data?.list != null) {
        setState(() {
          if (page == 1) {
            _allItems.clear();
          }
          _allItems.addAll(response.data!.list!);
          _currentPage = page;
          _hasMore = response.data!.hasMore;
        });
      }
    } catch (e) {
      debugPrint('加载数据失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print("build MeituanGroupListWithPaginationWidget！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！");
    if (_allItems.isEmpty && _isLoading) {
      return const SliverToBoxAdapter(
          child: Center(child: CircularProgressIndicator()));
    }

    if (_allItems.isEmpty && !_isLoading) {
      return SliverToBoxAdapter(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                '暂无数据',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: _loadInitialData,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == _allItems.length) {
            return Container(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text(
                        '没有更多数据了',
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 14,
                        ),
                      ),
              ),
            );
          }

          final item = _allItems[index];
          return MeituanGroupItemWidget(
            item: item,
            index: index,
            onShareTap: () => _handleShare(item),
            onBuyTap: () => _handleBuy(item),
          );
        },
        childCount: _allItems.length + (_hasMore ? 1 : 0),
      ),
    );
  }

  void _handleShare(MeituanCouponItem item) async {
    final shareContent = await ref.read(meituanShareLinkProvider(item).future);

    if (!mounted) return;

    ShareBottomSheet.show(
      context,
      shareContent.isNotEmpty ? shareContent : "暂无可分享内容",
    );
  }



  void _handleBuy(MeituanCouponItem item) {
    ref.read(funItemClickProvider.notifier).meituanGetCoupon(item);
    debugPrint('购买商品: ${item.goodsName}');
  }
}
