import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/attach/title_tutorial.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

class MainHeader extends ConsumerWidget {
  const MainHeader({
    Key? key,
    required this.t,
    required this.deltaExtent,
    required this.scaleValue,
    required this.scrollController,
  }) : super(key: key);

  /// 折叠系数
  final double t;

  /// 最大高度与最小高度的差值
  final double deltaExtent;

  /// 宽度缩放比率
  final double scaleValue;

  final ScrollController scrollController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFFFFFFFF), Color(0xFFF9F9F9)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      padding: EdgeInsets.fromLTRB(
        10.w,
        MediaQuery.of(context).padding.top,
        10.w,
        0,
      ),
      child: Stack(
        alignment: Alignment.centerRight,
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SizedBox(
              height: 44.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Opacity(
                    opacity: 1 - t,
                    child: Text(
                      "全网购物优惠",
                      style: TextStyle(
                        fontSize: 22.sp,
                        color: const Color(0xFF040404),
                        fontFamily: "Ceyyt",
                      ),
                    ),
                  ),
                  TitleTutorial(
                    scrollController: scrollController,
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: 44.h,
            left: 0,
            right: 0,
            child: Container(
              alignment: Alignment.centerLeft,
              transform: Transform.translate(
                offset: Offset(0, -t * deltaExtent),
              ).transform,
              height: 40.h,
              child: LayoutBuilder(
                builder: (BuildContext context, BoxConstraints constraints) {
                  return InkWell(
                    onTap: () {
                      ref
                          .watch(searchAutoFocusProvider.notifier)
                          .setAutoFocus(true);
                      Navigator.pushNamed(context, CsRouter.searchPre);
                    },
                    child: Container(
                      height: 30.h,
                      // 暂时关闭
                      width: constraints.maxWidth / scaleValue,
                      padding: const EdgeInsets.fromLTRB(9, 2, 2, 2),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            const BorderRadius.all(Radius.circular(20)),
                        border: Border.all(color: const Color(0xFFF93324)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Image.asset(
                                  searchIcon,
                                  width: 15.w,
                                  height: 15.w,
                                ),
                                Padding(padding: EdgeInsets.only(right: 7.w)),
                                Text(
                                  "搜折扣爆料",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: const Color(0xFF999999),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              ref
                                  .watch(searchAutoFocusProvider.notifier)
                                  .setAutoFocus(true);
                              Navigator.pushNamed(context, CsRouter.searchPre);
                            },
                            child: Container(
                              height: 26.h,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 10),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20)),
                              ),
                              child: Center(
                                child: Text(
                                  "搜索",
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
