import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/fun/fun_list_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_tab_config.dart';
import 'meituan_group_list_widget.dart';

/// 美团团购页面使用示例
class MeituanGroupPageExample extends ConsumerWidget {
  const MeituanGroupPageExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabConfigState = ref.watch(manageFunTabConfigProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('美团团购'),
        backgroundColor: const Color(0xFFF4F4F2),
      ),
      backgroundColor: const Color(0xFFF4F4F2),
      body: tabConfigState.when(
        data: (tabConfig) {
          if (tabConfig == null) {
            return const Center(child: Text('配置加载失败'));
          }

          // 检查是否是美团相关的主标签
          final mainTabCode = tabConfig.mainTab.tabs.isNotEmpty
              ? tabConfig.mainTab.tabs.first.code
              : '';

          if (mainTabCode != "meituan_groupBuying" &&
              mainTabCode != "meituan_sharpshooter") {
            return const Center(
              child: Text('当前不是美团标签页'),
            );
          }

          // 获取当前选中的子标签
          final tabSelection = ref.watch(tabSelectionStateProvider);
          final selectedChildIndex = tabSelection.childTabIndex;

          if (tabConfig.childrenTab.tabs.isEmpty ||
              selectedChildIndex >= tabConfig.childrenTab.tabs.length) {
            return const Center(child: Text('子标签配置错误'));
          }

          final selectedChildTab = tabConfig.childrenTab.tabs[selectedChildIndex];
          final mainTab = tabConfig.mainTab.tabs.first;

          return MeituanGroupListWithPaginationWidget(
            mainTabConfig: mainTab,
            childTabConfig: selectedChildTab,
            latitude: 39.9042, // 默认北京坐标
            longitude: 116.4074,
            sortOption: tabConfig.currentSort,
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('加载失败'),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () {
                  ref.invalidate(manageFunTabConfigProvider);
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 简单的美团团购列表示例（不带分页）
class SimpleMeituanGroupListExample extends ConsumerWidget {
  const SimpleMeituanGroupListExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 示例配置
    const mainTabConfig = FunTabConfig(
      code: "meituan_groupBuying",
      name: "美团团购",
    );

    const childTabConfig = FunTabConfig(
      listTopiId: 1,
      platform: 1,
      bizLine: 1,
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('美团团购列表'),
        backgroundColor: const Color(0xFFF4F4F2),
      ),
      backgroundColor: const Color(0xFFF4F4F2),
      body: const MeituanGroupListWidget(
        mainTabConfig: mainTabConfig,
        childTabConfig: childTabConfig,
        latitude: 39.9042,
        longitude: 116.4074,
      ),
    );
  }
}

/// 在现有页面中集成美团团购列表的示例
class IntegratedMeituanGroupExample extends ConsumerWidget {
  const IntegratedMeituanGroupExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Consumer(
      builder: (context, ref, child) {
        final tabConfigState = ref.watch(manageFunTabConfigProvider);
        final tabSelection = ref.watch(tabSelectionStateProvider);

        return tabConfigState.when(
          data: (tabConfig) {
            if (tabConfig == null) return const SizedBox();

            // 获取当前选中的主标签和子标签
            final mainTabIndex = tabSelection.mainTabIndex;
            final childTabIndex = tabSelection.childTabIndex;

            if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
                childTabIndex >= tabConfig.childrenTab.tabs.length) {
              return const SizedBox();
            }

            final mainTab = tabConfig.mainTab.tabs[mainTabIndex];
            final childTab = tabConfig.childrenTab.tabs[childTabIndex];

            // 只在美团相关标签时显示美团团购列表
            if (mainTab.code == "meituan_groupBuying" ||
                mainTab.code == "meituan_sharpshooter") {
              return MeituanGroupListWithPaginationWidget(
                mainTabConfig: mainTab,
                childTabConfig: childTab,
                latitude: 39.9042,
                longitude: 116.4074,
                sortOption: tabConfig.currentSort,
              );
            }

            // 其他标签显示其他内容
            return const Center(
              child: Text('其他内容'),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) => const Center(child: Text('加载失败')),
        );
      },
    );
  }
}