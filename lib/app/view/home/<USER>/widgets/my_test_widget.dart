import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../../provider/fun/fun_list_provider.dart';
import '../../../../repository/modals/fun/fun_tab_config.dart';
import 'fun_activity_tab_widget.dart';

class StickyPage extends ConsumerStatefulWidget {
  const StickyPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _StickyPageState();
}

class _StickyPageState extends ConsumerState<StickyPage> {
  final GlobalKey _headerKey = GlobalKey();
  double _headerHeight = 0;

  @override
  void initState() {
    super.initState();
    // 首次布局后测量高度
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateHeight());
  }

  void _updateHeight() {
    final box = _headerKey.currentContext?.findRenderObject() as RenderBox?;
    if (box != null && mounted) {
      final newHeight = box.size.height;
      if (_headerHeight != newHeight) {
        setState(() {
          _headerHeight = newHeight;
        });
      }
    }
  }

  /// 监听数据变化，当异步数据加载完成后重新测量高度
  void _scheduleHeightUpdate() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateHeight();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 当数据加载完成后，安排高度重新测量
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider,
        (previous, next) {
      if (next.hasValue && next.value != null) {
        _scheduleHeightUpdate();
      }
    });

    return Scaffold(
      body: CustomListView(
        sliverHeader: _buildSliverHeaders(),
        data: const <String>[
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1",
          "1"
        ],
        footerState: LoadState.noMore,
        renderItem: (context, index, item) => Text("商品 $index"),
        empty: Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.only(top: 80),
          child: const Text("暂无数据"),
        ),
      ),
    );
  }

  /// 构建 Sliver Headers
  /// 实现自适应高度的粘性头部效果
  List<Widget> _buildSliverHeaders() {
    final header = HeaderContent(
      key: _headerKey,
      onHeightChanged: _scheduleHeightUpdate,
    );

    return [
      // 头部组件（视频教程 + 返现平台）
      // 顶部图片
      SliverToBoxAdapter(
        child: Container(
          color: Colors.blue,
          height: 200,
        ),
      ),
      // 标题栏
      if (_headerHeight > 0)
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverAppBarDelegate(
            minHeight: _headerHeight,
            maxHeight: _headerHeight,
            child: header,
          ),
        )
      else
        // 先占位，等测量到高度后再替换
        SliverToBoxAdapter(child: header),
    ];
  }
}

/// 黄色+红色区域
class HeaderContent extends ConsumerWidget {
  final VoidCallback? onHeightChanged;

  const HeaderContent({super.key, this.onHeightChanged});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);
    final data = tabState.value;

    // 监听数据变化，当数据加载完成后触发高度重新计算
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider,
        (previous, next) {
      if (next.hasValue && next.value != null && onHeightChanged != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onHeightChanged!();
        });
      }
    });

    return Container(
      color: Colors.yellow,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 黄色部分
          Column(
            children: [
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: const [
                  Text("外卖霸王餐"),
                  Text("神抢手"),
                  Text("美团团购"),
                  Text("抖音团购"),
                ],
              ),
              Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                ),
                height: 36,
                child: Row(
                  children: const [
                    Icon(Icons.search),
                    SizedBox(width: 5),
                    Text("搜索更多优惠，下单享返现"),
                  ],
                ),
              ),
            ],
          ),
          // 红色部分 Wrap
          // Container(
          //   color: Colors.red,
          //   padding: const EdgeInsets.all(8),
          //   child: Wrap(
          //     spacing: 8,
          //     runSpacing: 8,
          //     children: List.generate(
          //       12,
          //       (index) => Chip(label: Text("分类 $index")),
          //     ),
          //   ),
          // ),
          // 红色部分 Wrap
          _buildChildrenTabAndSort(
              context,
              ref,
              data ??
                  TabStateConfig(
                      mainTab: const FunTabResult(),
                      childrenTab: const FunTabResult()),
              tabSelection),
        ],
      ),
    );
  }

  Widget _buildChildrenTabAndSort(BuildContext context, WidgetRef ref,
      TabStateConfig data, TabSelection tabSelection) {
    return Container(
      color: Colors.red,
      padding: const EdgeInsets.all(8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          // 子标签列表
          ...data.childrenTab.tabs.map((e) {
            return _buildChildTabItem(ref, e, data.childrenTab.tabs.indexOf(e),
                tabSelection.childTabIndex);
          }),
          // 排序按钮（如果存在）
          if (data.currentSort != null)
            GestureDetector(
              child: Container(
                margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      data.currentSort?.sortName ?? "",
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFF676764),
                      ),
                    ),
                    const SizedBox(width: 5),
                    CustomPaint(
                      size: const Size(8, 8),
                      painter: ArrowPainter(color: const Color(0xFF676764)),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 子标签
  Widget _buildChildTabItem(
      WidgetRef ref, FunTabConfig item, int index, int selectedIndex) {
    return GestureDetector(
      onTap: () {
        if (selectedIndex != index) {
          ref.read(tabSelectionStateProvider.notifier).setChildTabIndex(index);
        }
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: const Color(0xFFBAA38C), width: 0.5),
          color: selectedIndex == index
              ? const Color(0xFFFFFAED)
              : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.tabName ?? "",
              style: TextStyle(
                color: selectedIndex == index
                    ? const Color(0xFFFE7801)
                    : const Color(0xFF676764),
                fontSize: 10,
              ),
            ),
            if (item.children != null)
              Container(
                margin: const EdgeInsets.only(left: 5, top: 5),
                child: CustomPaint(
                  size: const Size(8, 8),
                  painter: ArrowPainter(
                    color: selectedIndex == index
                        ? const Color(0xFFFE7801)
                        : const Color(0xFF676764),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Delegate
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight ||
        child != oldDelegate.child;
  }
}
