import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/sign/koi_red_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/sign_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../../provider/sign/sign_provider.dart';
import '../dialog/koi_red_dialog.dart';
import '../dialog/koi_red_empty_dialog.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: earn_points
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 14:31
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 14:31
/// @UpdateRemark: 更新说明
class EarnPoints extends ConsumerStatefulWidget {
  const EarnPoints({super.key});

  @override
  EarnPointsState createState() => EarnPointsState();
}

class EarnPointsState extends ConsumerState<EarnPoints>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    // 使用 Tween 配置动画的起始和终止值
    _animation = Tween<double>(begin: 1.1, end: 0.9).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut, // 呼吸效果曲线
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var signRecord = ref.watch(signPageRecordProvider);
    var signCount = signRecord?.signModel?.signCount ?? 1;
    // 剩余每日红包数量
    var luckyGifts = ref.watch(
      fetchLuckGiftInfoProvider.select((value) => value?.luckyGifts),
    );
    var remainingGift =
        luckyGifts?.where((element) => element?.localIndex != null);

    Widget giftTips = SizedBox(height: 14.h);
    if (remainingGift?.length != null) {
      if (remainingGift!.isNotEmpty) {
        giftTips = Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.white,
            ),
            children: [
              const TextSpan(text: "今日还有"),
              TextSpan(
                text: "${remainingGift.length}",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFFF6E113),
                ),
              ),
              const TextSpan(text: "个红包待开启"),
            ],
          ),
        );
      } else {
        giftTips = Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.white,
            ),
            children: const [
              TextSpan(text: "今日红包已全部开启"),
            ],
          ),
        );
      }
    }

    return Container(
      margin: EdgeInsets.only(top: 6.h),
      child: Stack(
        children: [
          Positioned(
            top: 23,
            left: 10,
            child: InkWell(
              onTap: () {
                SignDialog.show(signRecord);
              },
              child: Column(
                children: [
                  Container(
                    width: 64.w,
                    height: 27.h,
                    alignment: Alignment.bottomCenter,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: NetworkImage(signDay),
                        fit: BoxFit.contain,
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 3.h),
                      child: Text(
                        "本月已签到",
                        style: TextStyle(fontSize: 10.sp, color: Colors.white),
                      ),
                    ),
                  ),
                  Container(
                    width: 64.w,
                    alignment: Alignment.center,
                    padding: EdgeInsets.only(top: 6.h, bottom: 6.h),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10),
                        bottomRight: Radius.circular(10),
                      ),
                    ),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFFF93324),
                        ),
                        children: [
                          TextSpan(
                            text: "$signCount",
                            style: TextStyle(
                                fontSize: 30.sp, fontWeight: FontWeight.w700),
                          ),
                          const TextSpan(text: "天")
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 164.w,
                height: 147.h,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                      "${Constant.msmdsAliCdn}/appNormal/sign_gift_bottomnew.png",
                    ),
                    fit: BoxFit.contain,
                  ),
                ),
                child: Column(
                  // mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 6.h),
                          padding: EdgeInsets.symmetric(
                            horizontal: 7.w,
                            vertical: 1.h,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xFFFF7473),
                                Color(0xFFFF4A48),
                                Color(0xFFFF7573),
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                          ),
                          child: giftTips,
                        ),
                        TriangleIndicator(
                          color: const Color(0xFFFF4A48),
                          size: Size(6.w, 2.h),
                        ),
                      ],
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 4.h)),
                    Image.network(
                      "${Constant.msmdsAliCdn}/appNormal/opti_sign_package.png",
                      width: 72.w,
                      height: 72.h,
                      fit: BoxFit.contain,
                    ),
                    SizedBox(height: 8.h),
                    ScaleTransition(
                      scale: _animation,
                      child: SizedBox(
                        width: 130.w,
                        height: 32.h,
                        child: GradientButton(
                          onPress: () {
                            if (remainingGift != null &&
                                remainingGift.isNotEmpty) {
                              KoiRedDialog.show(remainingGift.first);
                            } else {
                              // 全部开完
                              KoiRedEmptyDialog.show();
                            }
                          },
                          radius: 26.r,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFC5E61),
                              Color(0xFFFF231F),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          child: Text(
                            "去开红包>",
                            style: TextStyle(
                              fontSize: 20.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 三角指示器
class TriangleIndicator extends StatelessWidget {
  final Color color;
  final Size size;

  const TriangleIndicator({
    super.key,
    this.color = Colors.red,
    this.size = Size.zero,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: size,
      painter: TrianglePainter(color),
    );
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = color;
    final path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width / 2, size.height);
    path.lineTo(size.width, 0);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
