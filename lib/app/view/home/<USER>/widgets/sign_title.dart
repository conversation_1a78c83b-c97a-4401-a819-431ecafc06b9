import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/repository/modals/sign/user_integral.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/sign_rule_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: sign_title
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 12:08
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 12:08
/// @UpdateRemark: 更新说明
class SignTitle extends StatelessWidget {
  const SignTitle({super.key, this.userIntegral});

  final UserIntegral? userIntegral;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        10.w,
        7.h + MediaQuery.of(context).padding.top,
        9.w,
        7.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, CsRouter.myIntegralPage);
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(6.w, 4.h, 8.w, 4.h),
              decoration: BoxDecoration(
                color: const Color(0x50000000),
                borderRadius: BorderRadius.circular(18.r),
              ),
              child: Row(
                children: [
                  Image.network(
                    signIng,
                    width: 22.w,
                    height: 22.w,
                  ),
                  Padding(padding: EdgeInsets.only(right: 5.w)),
                  Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFFFFFFFF),
                      ),
                      children: [
                        TextSpan(
                          text: "${userIntegral?.simpleIntegral ?? 0}积分",
                          style: const TextStyle(color: Color(0xFFF6E113)),
                        ),
                        const TextSpan(text: "可用"),
                      ],
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(right: 8.w)),
                  Image.network(
                    signRaw,
                    width: 8.w,
                    height: 10.h,
                    fit: BoxFit.contain,
                  ),
                ],
              ),
            ),
          ),
          Row(
            children: [
              TextButton(
                onPressed: () {
                  SignRuleDialog.show();
                },
                child: Text(
                  "说明",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 2.w)),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, CsRouter.integralDetailPage);
                },
                child: Text(
                  "领取记录",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
