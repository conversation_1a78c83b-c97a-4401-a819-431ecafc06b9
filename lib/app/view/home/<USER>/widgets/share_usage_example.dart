import 'package:flutter/material.dart';
import 'share_bottom_sheet.dart';

/// 分享功能使用示例
/// 演示如何调用 ShareBottomSheet.show 方法
class ShareUsageExample extends StatelessWidget {
  const ShareUsageExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('分享功能示例')),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _showShareExample(context),
          child: const Text('显示分享弹窗'),
        ),
      ),
    );
  }

  void _showShareExample(BuildContext context) {
    const shareContent = 'https://example.com/share/product123';
    ShareBottomSheet.show(context, shareContent);
  }
}