import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.widgets
/// @ClassName: sign_dialog_animation
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/13 17:30
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 17:30
/// @UpdateRemark: 更新说明
class CustomDialogAnimation extends StatefulWidget {
  const CustomDialogAnimation({
    Key? key,
    required this.child,
    required this.animationParam,
    this.dy = -0.2,
  }) : super(key: key);

  final Widget child;

  final AnimationParam animationParam;

  final double? dy;

  @override
  State<CustomDialogAnimation> createState() => _CustomAnimationState();
}

class _CustomAnimationState extends State<CustomDialogAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller1;
  late AnimationController _controller2;

  late Animation<Offset> _enterAnimation;
  late Animation<Offset> _exitAnimation;
  late Animation<Offset> _animation;

  @override
  void initState() {
    _controller1 = AnimationController(
      vsync: this,
      duration: widget.animationParam.animationTime,
    );

    _controller2 = AnimationController(
      vsync: this,
      duration: widget.animationParam.animationTime,
    );

    // 进入动画，从顶部进入到中间
    _enterAnimation = Tween<Offset>(
      begin: const Offset(0.0, -1.5), // 屏幕顶部外
      end: Offset(0.0, widget.dy ?? -0.2), // 屏幕中间
    ).animate(CurvedAnimation(
      parent: _controller1,
      curve: Curves.easeOut,
    ));

    // 退出动画，从中间退出到底部
    _exitAnimation = Tween<Offset>(
      begin: Offset(0.0, widget.dy ?? -0.2), // 屏幕中间
      end: const Offset(0.0, 1.5), // 屏幕底部外
    ).animate(CurvedAnimation(
      parent: _controller2,
      curve: Curves.easeIn,
    ));

    _animation = _enterAnimation;

    widget.animationParam.onForward = () {
      setState(() => _animation = _enterAnimation); // 显示组件
      _controller1.forward();
    };
    widget.animationParam.onDismiss = () {
      setState(() => _animation = _exitAnimation); // 显示组件
      _controller2.forward();
    };
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _animation,
      child: widget.child,
    );
  }

  @override
  void dispose() {
    _controller1.dispose();
    _controller2.dispose();
    super.dispose();
  }
}
