import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/utils/router_util.dart';
import 'package:url_launcher/url_launcher.dart';

class OrderModuleWidget extends ConsumerWidget {
  const OrderModuleWidget({super.key});

  Widget _buildItem(
    String icon,
    String name,
    Function() onPress, {
    bool assets = false,
  }) {
    Widget img;
    if (assets) {
      img = Image.asset(
        icon,
        width: 30.r,
        fit: BoxFit.contain,
      );
    } else {
      img = Image.network(
        icon,
        width: 30.r,
        fit: BoxFit.contain,
      );
    }
    return InkWell(
      onTap: onPress,
      child: Column(
        children: [
          img,
          Text(
            name,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF333333),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 10.w,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 18.w,
        vertical: 14.h,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(10.h),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildItem(
            myOrderIcon,
            "我的订单",
            () {
              RouterUtil.checkLogin(
                context,
                call: () {
                  Navigator.pushNamed(context, CsRouter.myOrderPage);
                },
                execute: true,
              );
            },
            assets: true,
          ),
          _buildItem(
            consumerPhoneIcon,
            "客服热线",
            () async {
              if (await canLaunchUrl(Uri.parse("tel:020 87577697"))) {
                await launchUrl(Uri.parse("tel:020 87577697"));
              }
            },
          ),
          _buildItem(
            myFansIncome,
            "我的粉丝",
            () {
              RouterUtil.checkLogin(context, call: () {
                Navigator.pushNamed(context, CsRouter.inviteDetailsPage);
              });
            },
            assets: true,
          ),
          _buildItem(
            myTutorial,
            "返现教程",
            () {
              Navigator.pushNamed(
                context,
                CsRouter.webPage,
                arguments: [
                  "返现教程",
                  "https://ecoh5.szprize.cn/#/app/beginnerCourse",
                ],
              );
            },
            assets: true,
          ),
        ],
      ),
    );
  }
}
