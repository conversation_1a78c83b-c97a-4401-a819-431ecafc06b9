import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/sign/koi_red_provider.dart';
import 'package:msmds_platform/app/repository/modals/sign/luck_gift_info.dart';
import 'package:msmds_platform/app/repository/modals/sign/luck_gift_item.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/koi_red_dialog.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/koi_red_empty_dialog.dart';

import '../../../../../config/constant.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.widgets
/// @ClassName: sign_koi_red_packet
/// @Description: 签到页开红包
/// @Author: frankylee
/// @CreateDate: 2024/11/14 16:16
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 16:16
/// @UpdateRemark: 更新说明
class SignKoiRedPacket extends ConsumerWidget {
  const SignKoiRedPacket({super.key});

  void _openLuckGift(LuckGiftItem? item, LuckGiftInfo giftInfo) {
    var surplusNumber = giftInfo.surplusNumber ?? 0;
    if (surplusNumber <= 0) {
      // 红包已经开完
      KoiRedEmptyDialog.show();
    } else {
      // 弹出开红包弹窗
      KoiRedDialog.show(item);
    }
  }

  Widget _buildItem(LuckGiftItem? item, LuckGiftInfo giftInfo) {
    if (item?.id != null) {
      // 已经开启
      return Container(
        padding: EdgeInsets.only(left: 10.w),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Image.network(
              "${Constant.msmdsAliCdn}/appNormal/Koiitemopenbg.png",
              width: 50.w,
              height: 69.h,
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(5.w, 15.h, 5.w, 0),
              child: Text(
                "${item?.name}",
                style: TextStyle(
                  fontSize: 8.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFE44324),
                ),
              ),
            ),
          ],
        ),
      );
    }
    return InkWell(
      onTap: () {
        // 开启红包
        _openLuckGift(item, giftInfo);
      },
      child: Container(
        padding: EdgeInsets.only(left: 10.w),
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Image.network(
              "${Constant.msmdsAliCdn}/appNormal/Koiitembg4.png",
              width: 50.w,
              height: 69.h,
            ),
            Padding(
              padding: EdgeInsets.only(top: 15.h),
              child: Image.network(
                "${Constant.msmdsAliCdn}/appNormal/KoiitemKai.png",
                width: 24.w,
                height: 24.w,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var giftInfo = ref.watch(fetchLuckGiftInfoProvider);
    if (giftInfo == null ||
        giftInfo.luckyGifts == null ||
        giftInfo.luckyGifts!.isEmpty) {
      return const SizedBox();
    }
    return Container(
      padding: EdgeInsets.symmetric(vertical: 11.h),
      margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              SizedBox(width: 10.w),
              Text(
                "每日抽红包",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF202123),
                ),
              ),
              SizedBox(width: 13.w),
              Text(
                "有机会得会员/返现红包",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF9C9C9C),
                ),
              ),
            ],
          ),
          SizedBox(height: 10.h),
          SizedBox(
            height: 69.h,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(right: 10.w),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: giftInfo.luckyGifts!
                    .map((e) => _buildItem(e, giftInfo))
                    .toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
