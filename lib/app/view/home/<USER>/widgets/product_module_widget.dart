import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/provider/order/order_detail_provider.dart';

class ProductModuleWidget extends ConsumerWidget {
  const ProductModuleWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchNewestOrderProvider).when(
      data: (order) {
        if (order == null) return Container();

        var commission = "返现¥${order.userCommission}";
        if (order.redPacketAmount != null) {
          commission = "$commission + ${order.redPacketAmount}元红包";
        }
        return Container(
          margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
          child: InkWell(
            borderRadius: BorderRadius.circular(10.h),
            onTap: () {
              if (order.orderNo != null) {
                ref
                    .watch(currentOrderProvider.notifier)
                    .setCurrentOrder(order.orderNo!);
                Navigator.pushNamed(context, CsRouter.orderDetail);
              }
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.h),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: 10.w,
                vertical: 10.h,
              ),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.network(
                      "${order.goodsImg}",
                      width: 40.w,
                      fit: BoxFit.contain,
                      errorBuilder: (c, o, s) {
                        return Container(
                          width: 40.w,
                          height: 40.w,
                          color: Colors.grey.withAlpha(50),
                        );
                      },
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${order.goodsTitle}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFF3A3A3A),
                          ),
                        ),
                        SizedBox(
                          height: 4.h,
                        ),
                        Text(
                          commission,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFF93324),
                          ),
                        )
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 12.r,
                    color: const Color(0xFF9C9C9C),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
