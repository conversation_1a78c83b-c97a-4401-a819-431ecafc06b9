import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../../provider/fun/fun_list_provider.dart';
import '../../../../repository/modals/fun/fun_tab_config.dart';
import 'fun_activity_tab_widget.dart';

/// 动态高度 SliverPersistentHeader 解决方案演示
/// 
/// 核心解决思路：
/// 1. 使用 NotificationListener 监听内容变化
/// 2. 通过 LayoutBuilder 获取实际渲染尺寸
/// 3. 结合 ValueNotifier 实现高度动态更新
/// 4. 避免重复测量和无限重建
class DynamicSliverHeaderDemo extends ConsumerStatefulWidget {
  const DynamicSliverHeaderDemo({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DynamicSliverHeaderDemoState();
}

class _DynamicSliverHeaderDemoState extends ConsumerState<DynamicSliverHeaderDemo> {
  final ValueNotifier<double> _headerHeightNotifier = ValueNotifier<double>(0);
  final GlobalKey _headerKey = GlobalKey();
  bool _isFirstBuild = true;

  @override
  void dispose() {
    _headerHeightNotifier.dispose();
    super.dispose();
  }

  /// 测量并更新头部高度
  void _measureAndUpdateHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final box = _headerKey.currentContext?.findRenderObject() as RenderBox?;
      if (box != null && mounted) {
        final newHeight = box.size.height;
        if (_headerHeightNotifier.value != newHeight) {
          _headerHeightNotifier.value = newHeight;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 监听数据变化，触发高度重新测量
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider, (previous, next) {
      if (next.hasValue && next.value != null) {
        _measureAndUpdateHeight();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('动态高度 SliverHeader'),
        backgroundColor: Colors.blue,
      ),
      body: ValueListenableBuilder<double>(
        valueListenable: _headerHeightNotifier,
        builder: (context, headerHeight, child) {
          return CustomListView(
            sliverHeader: _buildSliverHeaders(headerHeight),
            data: List.generate(50, (index) => "商品 $index"),
            footerState: LoadState.noMore,
            renderItem: (context, index, item) => Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                item,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            empty: Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.only(top: 80),
              child: const Text("暂无数据"),
            ),
          );
        },
      ),
    );
  }

  /// 构建 Sliver Headers
  List<Widget> _buildSliverHeaders(double headerHeight) {
    return [
      // 顶部图片区域
      SliverToBoxAdapter(
        child: Container(
          height: 200,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.blue, Colors.lightBlue],
            ),
          ),
          child: const Center(
            child: Text(
              '顶部横幅区域',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
      
      // 动态高度的粘性头部
      if (headerHeight > 0)
        SliverPersistentHeader(
          pinned: true,
          delegate: _DynamicSliverHeaderDelegate(
            height: headerHeight,
            child: DynamicHeaderContent(
              key: _headerKey,
              onLayoutChanged: _measureAndUpdateHeight,
            ),
          ),
        )
      else
        // 初始占位，用于首次测量
        SliverToBoxAdapter(
          child: DynamicHeaderContent(
            key: _headerKey,
            onLayoutChanged: _measureAndUpdateHeight,
          ),
        ),
    ];
  }
}

/// 动态内容头部组件
class DynamicHeaderContent extends ConsumerWidget {
  final VoidCallback? onLayoutChanged;
  
  const DynamicHeaderContent({
    super.key,
    this.onLayoutChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);
    final data = tabState.value;

    // 监听数据变化，触发布局更新回调
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider, (previous, next) {
      if (next.hasValue && next.value != null && onLayoutChanged != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onLayoutChanged!();
        });
      }
    });

    return NotificationListener<SizeChangedLayoutNotification>(
      onNotification: (notification) {
        // 当布局大小发生变化时触发回调
        if (onLayoutChanged != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onLayoutChanged!();
          });
        }
        return true;
      },
      child: SizeChangedLayoutNotifier(
        child: Container(
          color: Colors.yellow,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 固定内容区域
              _buildFixedContent(),
              
              // 动态内容区域（根据异步数据变化）
              if (tabState.isLoading)
                _buildLoadingContent()
              else if (tabState.hasError)
                _buildErrorContent()
              else if (data != null)
                _buildDynamicContent(context, ref, data, tabSelection)
              else
                _buildEmptyContent(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建固定内容
  Widget _buildFixedContent() {
    return Column(
      children: [
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: const [
            Text("外卖霸王餐", style: TextStyle(fontWeight: FontWeight.bold)),
            Text("神抢手", style: TextStyle(fontWeight: FontWeight.bold)),
            Text("美团团购", style: TextStyle(fontWeight: FontWeight.bold)),
            Text("抖音团购", style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
        Container(
          margin: const EdgeInsets.all(8),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          height: 36,
          child: Row(
            children: const [
              Icon(Icons.search),
              SizedBox(width: 5),
              Text("搜索更多优惠，下单享返现"),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建加载状态内容
  Widget _buildLoadingContent() {
    return Container(
      color: Colors.red.withOpacity(0.3),
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// 构建错误状态内容
  Widget _buildErrorContent() {
    return Container(
      color: Colors.red.withOpacity(0.3),
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Text(
          "加载失败，请重试",
          style: TextStyle(color: Colors.red),
        ),
      ),
    );
  }

  /// 构建空状态内容
  Widget _buildEmptyContent() {
    return Container(
      color: Colors.red.withOpacity(0.3),
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: Text("暂无数据"),
      ),
    );
  }

  /// 构建动态内容（根据异步数据）
  Widget _buildDynamicContent(
    BuildContext context,
    WidgetRef ref,
    TabStateConfig data,
    TabSelection tabSelection,
  ) {
    return Container(
      color: Colors.red,
      padding: const EdgeInsets.all(8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          // 子标签列表
          ...data.childrenTab.tabs.map((e) {
            return _buildChildTabItem(
              ref,
              e,
              data.childrenTab.tabs.indexOf(e),
              tabSelection.childTabIndex,
            );
          }),
          
          // 排序按钮（如果存在）
          if (data.currentSort != null)
            _buildSortButton(data.currentSort!),
        ],
      ),
    );
  }

  /// 构建子标签项
  Widget _buildChildTabItem(
    WidgetRef ref,
    FunTabConfig item,
    int index,
    int selectedIndex,
  ) {
    return GestureDetector(
      onTap: () {
        if (selectedIndex != index) {
          ref.read(tabSelectionStateProvider.notifier).setChildTabIndex(index);
        }
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: const Color(0xFFBAA38C), width: 0.5),
          color: selectedIndex == index
              ? const Color(0xFFFFFAED)
              : Colors.transparent,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              item.tabName ?? "",
              style: TextStyle(
                color: selectedIndex == index
                    ? const Color(0xFFFE7801)
                    : const Color(0xFF676764),
                fontSize: 10,
              ),
            ),
            if (item.children != null)
              Container(
                margin: const EdgeInsets.only(left: 5, top: 5),
                child: CustomPaint(
                  size: const Size(8, 8),
                  painter: ArrowPainter(
                    color: selectedIndex == index
                        ? const Color(0xFFFE7801)
                        : const Color(0xFF676764),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建排序按钮
  Widget _buildSortButton(FunSortOption sortOption) {
    return Container(
      margin: const EdgeInsets.fromLTRB(5, 12, 5, 0),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            sortOption.sortName ?? "",
            style: const TextStyle(
              fontSize: 11,
              color: Color(0xFF676764),
            ),
          ),
          const SizedBox(width: 5),
          CustomPaint(
            size: const Size(8, 8),
            painter: ArrowPainter(color: const Color(0xFF676764)),
          ),
        ],
      ),
    );
  }
}

/// 动态高度的 SliverPersistentHeaderDelegate
class _DynamicSliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double height;
  final Widget child;

  _DynamicSliverHeaderDelegate({
    required this.height,
    required this.child,
  });

  @override
  double get minExtent => height;

  @override
  double get maxExtent => height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_DynamicSliverHeaderDelegate oldDelegate) {
    return height != oldDelegate.height || child != oldDelegate.child;
  }
}
