import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../provider/me/me_provider.dart';
import '../../main/widgets/main_swiper.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.me.widgets
/// @ClassName: my_swiper_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/22 16:34
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/22 16:34
/// @UpdateRemark: 更新说明
class MySwiperWidget extends ConsumerWidget {
  const MySwiperWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchMeSwiperConfigProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return Padding(
          padding: EdgeInsets.only(top: 10.h),
          child: SwiperWidget(list: data),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
