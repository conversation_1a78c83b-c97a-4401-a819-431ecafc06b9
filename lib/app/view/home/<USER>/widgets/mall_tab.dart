import 'package:flutter/material.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_item.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: mall_tab
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 11:09
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 11:09
/// @UpdateRemark: 更新说明

class MallTab extends StatefulWidget {
  const MallTab({super.key});

  @override
  MallTabState createState() => MallTabState();
}

class MallTabState extends State<MallTab> {
  List<int> data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

  LoadState loadState = LoadState.idle;

  @override
  Widget build(BuildContext context) {
    return CustomListView(
      header: const Padding(padding: EdgeInsets.only(top: 10)),
      separator: (context, index) {
        return const Padding(padding: EdgeInsets.only(bottom: 10));
      },
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 2), () {
          return true;
        });
        setState(() {
          data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        });
      },
      onLoadMore: () async {
        setState(() {
          loadState = LoadState.loading;
        });
        await Future.delayed(const Duration(seconds: 3), () {
          return true;
        });
        setState(() {
          data = [...data, ...List.generate(10, (index) => index).toList()];
          loadState = LoadState.idle;
        });
      },
      data: data,
      footerState: loadState,
      renderItem: (context, index, item) {
        return const MallItem();
      },
    );
  }
}
