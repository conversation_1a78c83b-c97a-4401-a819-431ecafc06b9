import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

class ShareBottomSheet extends StatelessWidget {
  final String shareContent;

  const ShareBottomSheet({
    super.key,
    required this.shareContent,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 12.h),

          // 标题
          Text(
            '分享赚收益',
            style: TextStyle(
              color: const Color(0xFF9E9D9E),
              fontSize: 12.sp,
            ),
          ),

          SizedBox(height: 12.h),

          // 分享内容区域
          Container(
            width: 325.w,
            padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 14.h),
            decoration: BoxDecoration(
              color: const Color(0xFFF4F4F4),
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Column(
              children: [
                // 分享内容文本
                Text(
                  shareContent,
                  style: TextStyle(
                    color: const Color(0xFF676764),
                    fontSize: 12.sp,
                  ),
                  textAlign: TextAlign.center,
                ),

                SizedBox(height: 10.h),

                // 复制按钮
                GestureDetector(
                  onTap: _copyToClipboard,
                  child: Container(
                    width: 150.w,
                    height: 24.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF93324),
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Center(
                      child: Text(
                        '复制分享内容',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 12.h),

          // 取消按钮
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 325.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: const Center(
                child: Text('取消'),
              ),
            ),
          ),

          SizedBox(height: 17.h + MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: shareContent));
    SmartDialog.showToast('复制成功');
  }

  static Future<void> show(BuildContext context, String shareContent) {
    return showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => ShareBottomSheet(shareContent: shareContent),
    );
  }
}