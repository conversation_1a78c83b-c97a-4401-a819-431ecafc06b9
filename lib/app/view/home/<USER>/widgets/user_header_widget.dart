import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/account/account_detail.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/constant.dart';

class UserHeaderWidget extends ConsumerWidget {
  const UserHeaderWidget({super.key});

  /// 用户信息
  Widget _buildUserInfo(AccountDetail? detail) {
    // var vipLevel = detail?.vipLevel ?? -1;
    // var isVip = vipLevel >= 0;
    // String showOverTime = detail?.overDueTime??"";
    // var overTimeList = detail?.overDueTime?.split(" ");
    // if (overTimeList != null && overTimeList.isNotEmpty) {
    //   showOverTime = overTimeList[0];
    // }
    // Widget vipWidget = const SizedBox();
    // if (isVip) {
    //   vipWidget = Container(
    //     decoration: BoxDecoration(
    //       color: const Color(0xFFF0DAC3),
    //       borderRadius: BorderRadius.circular(16.r),
    //     ),
    //     padding: EdgeInsets.only(right: 12.w),
    //     child: Row(
    //       children: [
    //         Image.asset(
    //           vipHeaderIcon,
    //           width: 26.w,
    //           height: 26.w,
    //         ),
    //         SizedBox(width: 4.w,),
    //         Column(
    //           crossAxisAlignment: CrossAxisAlignment.start,
    //           children: [
    //             SizedBox(height: 2.h,),
    //             Row(
    //               children: [
    //                 Text(
    //                   "超级会员",
    //                   style: TextStyle(
    //                     fontSize: 10.sp,
    //                     color: const Color(0xFF333333),
    //                   ),
    //                 ),
    //                 Icon(
    //                   Icons.arrow_forward_ios_rounded,
    //                   size: 8.r,
    //                   color: const Color(0xFF333333),
    //                 ),
    //               ],
    //             ),
    //             SizedBox(height: 2.h,),
    //             Text(
    //               "有效期：$showOverTime",
    //               style: TextStyle(
    //                 fontSize: 8.sp,
    //                 color: const Color(0xFF666666),
    //               ),
    //             ),
    //             SizedBox(height: 2.h,),
    //           ],
    //         )
    //       ],
    //     ),
    //   );
    // }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(25.w),
          child: Image.network(
            "${detail?.avatarUrl}",
            width: 50.w,
            height: 50.w,
            errorBuilder: (context, o, s) {
              return SizedBox(
                width: 50.w,
                height: 50.w,
              );
            },
          ),
        ),
        SizedBox(
          width: 16.w,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                detail?.nickName ?? "",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              // SizedBox(height: 12.h,),
              // Row(
              //   children: [vipWidget],
              // ),
            ],
          ),
        ),
      ],
    );
  }

  /// 未登录
  Widget _buildLogin(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, CsRouter.login);
      },
      child: Row(
        children: [
          Image.asset(
            noLoginAvatar,
            width: 50.w,
            height: 50.w,
          ),
          SizedBox(
            width: 16.w,
          ),
          Text(
            "注册/登录",
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    Widget userInfoWidget = _buildLogin(context);
    if (userData != null) {
      userInfoWidget = _buildUserInfo(userData.data);
    }
    return Container(
      padding: EdgeInsets.only(left: 10.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(
                  width: 10.w,
                ),
                Expanded(child: userInfoWidget),
              ],
            ),
          ),
          Row(
            children: [
              // Image.asset(
              //   messageIconImg,
              //   width: 21.r,
              //   fit: BoxFit.contain,
              // ),
              // SizedBox(
              //   width: 10.w,
              // ),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, CsRouter.setting);
                },
                child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Image.network(
                    "${Constant.msmdsAliCdn}/appNormal/my_page_seticon.png",
                    width: 20.w,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              SizedBox(
                width: 12.w,
              ),
            ],
          )
        ],
      ),
    );
  }
}
