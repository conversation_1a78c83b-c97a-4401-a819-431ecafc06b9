import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/utils/router_util.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

class MyBenefitsWidget extends ConsumerWidget {
  const MyBenefitsWidget({super.key});

  /// 收益item
  Widget _buildInfoItem(BuildContext context, String amount, String title) {
    return InkWell(
      onTap: () {
        RouterUtil.checkLogin(
          context,
          call: () {
            Navigator.pushNamed(context, CsRouter.bill);
          },
          execute: true,
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            amount,
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF202123),
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(
            height: 5.h,
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF9C9C9C),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: 10.w,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(10.h),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: 10.h,
              horizontal: 10.w,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "我的收益",
                  style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                      fontWeight: FontWeight.w600),
                ),
                GradientButton(
                  padding: EdgeInsets.fromLTRB(12.w, 4.h, 10.w, 4.h),
                  radius: 60,
                  shadow: false,
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xFFFF5640), Color(0xFFFB2D1A)],
                  ),
                  onPress: () {
                    RouterUtil.checkLogin(
                      context,
                      call: () {
                        Navigator.pushNamed(context, CsRouter.withdrawal);
                      },
                      execute: true,
                    );
                  },
                  child: Row(
                    children: [
                      Text(
                        "0手续费提现",
                        style: TextStyle(fontSize: 13.sp, color: Colors.white),
                      ),
                      SizedBox(
                        width: 4.w,
                      ),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 10.r,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 1.h,
            color: const Color(0xFFF5F5F5),
          ),
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: 20.h,
              horizontal: 20.w,
            ),
            child: IntrinsicHeight(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildInfoItem(
                    context,
                    "${ref.watch(
                      walletInfoProvider.select(
                        (value) => value?.canCash ?? "0.0",
                      ),
                    )}",
                    "可提现 (元)",
                  ),
                  const VerticalDivider(
                    width: 1,
                    color: Color(0x1A000000),
                  ),
                  _buildInfoItem(
                    context,
                    "${ref.watch(
                      walletInfoProvider.select(
                        (value) => value?.waitCash ?? "0.0",
                      ),
                    )}",
                    "即将入账 (元)",
                  ),
                  const VerticalDivider(
                    width: 1,
                    color: Color(0x1A000000),
                  ),
                  _buildInfoItem(
                    context,
                    "${ref.watch(
                      walletInfoProvider.select(
                        (value) => value?.totalEconomize ?? "0.0",
                      ),
                    )}",
                    "累计收益 (元)",
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
