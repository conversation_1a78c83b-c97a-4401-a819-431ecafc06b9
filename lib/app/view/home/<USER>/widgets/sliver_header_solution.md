# Flutter SliverPersistentHeader 动态高度解决方案

## 问题描述

当 SliverPersistentHeader 内的 Wrap 组件包含动态加载的网络数据时，会出现高度计算错误和内容溢出的问题。

### 具体场景对比

1. **静态数据场景（正常工作）**：
   - HeaderContent 在初始化时就知道 Wrap 组件的确切高度
   - _updateHeight 方法能够正确获取到 _headerHeight 值
   - SliverPersistentHeader 能够正确设置和维持高度

2. **网络数据场景（出现问题）**：
   - 第一次测量时，Wrap 组件内还没有异步加载的数据，导致测量高度很小
   - 当异步数据加载完成后，Wrap 组件重新构建（rebuild），实际内容高度变大
   - 但此时 SliverPersistentHeader 的高度已经被锁定为第一次测量的较小高度
   - 结果导致：内容溢出、渲染错误、内容显示不完整

## 解决方案

### 方案一：监听数据变化 + 高度重新测量

```dart
class _StickyPageState extends ConsumerState<StickyPage> {
  final GlobalKey _headerKey = GlobalKey();
  double _headerHeight = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateHeight());
  }

  void _updateHeight() {
    final box = _headerKey.currentContext?.findRenderObject() as RenderBox?;
    if (box != null && mounted) {
      final newHeight = box.size.height;
      if (_headerHeight != newHeight) {
        setState(() {
          _headerHeight = newHeight;
        });
      }
    }
  }

  void _scheduleHeightUpdate() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateHeight();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 监听数据变化，当异步数据加载完成后重新测量高度
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider, (previous, next) {
      if (next.hasValue && next.value != null) {
        _scheduleHeightUpdate();
      }
    });

    return Scaffold(
      body: CustomListView(
        sliverHeader: _buildSliverHeaders(),
        // ... 其他配置
      ),
    );
  }

  List<Widget> _buildSliverHeaders() {
    final header = HeaderContent(
      key: _headerKey,
      onHeightChanged: _scheduleHeightUpdate,
    );

    return [
      SliverToBoxAdapter(
        child: Container(color: Colors.blue, height: 200),
      ),
      if (_headerHeight > 0)
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverAppBarDelegate(
            minHeight: _headerHeight,
            maxHeight: _headerHeight,
            child: header,
          ),
        )
      else
        SliverToBoxAdapter(child: header),
    ];
  }
}
```

### 方案二：使用 ValueNotifier + NotificationListener

```dart
class _DynamicSliverHeaderDemoState extends ConsumerState<DynamicSliverHeaderDemo> {
  final ValueNotifier<double> _headerHeightNotifier = ValueNotifier<double>(0);
  final GlobalKey _headerKey = GlobalKey();

  void _measureAndUpdateHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final box = _headerKey.currentContext?.findRenderObject() as RenderBox?;
      if (box != null && mounted) {
        final newHeight = box.size.height;
        if (_headerHeightNotifier.value != newHeight) {
          _headerHeightNotifier.value = newHeight;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider, (previous, next) {
      if (next.hasValue && next.value != null) {
        _measureAndUpdateHeight();
      }
    });

    return ValueListenableBuilder<double>(
      valueListenable: _headerHeightNotifier,
      builder: (context, headerHeight, child) {
        return CustomListView(
          sliverHeader: _buildSliverHeaders(headerHeight),
          // ... 其他配置
        );
      },
    );
  }
}

class DynamicHeaderContent extends ConsumerWidget {
  final VoidCallback? onLayoutChanged;
  
  const DynamicHeaderContent({super.key, this.onLayoutChanged});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听数据变化
    ref.listen<AsyncValue<TabStateConfig?>>(manageFunTabConfigProvider, (previous, next) {
      if (next.hasValue && next.value != null && onLayoutChanged != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onLayoutChanged!();
        });
      }
    });

    return NotificationListener<SizeChangedLayoutNotification>(
      onNotification: (notification) {
        if (onLayoutChanged != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onLayoutChanged!();
          });
        }
        return true;
      },
      child: SizeChangedLayoutNotifier(
        child: Container(
          // 动态内容
        ),
      ),
    );
  }
}
```

## 核心解决思路

1. **监听数据变化**：使用 `ref.listen` 监听异步数据的加载状态
2. **延迟测量**：使用 `addPostFrameCallback` 确保在布局完成后再测量高度
3. **高度比较**：只有当新高度与当前高度不同时才更新状态，避免无限重建
4. **回调机制**：通过回调函数在内容变化时通知父组件重新测量
5. **通知监听**：使用 `NotificationListener` 和 `SizeChangedLayoutNotifier` 监听布局变化

## 关键要点

1. **避免重复测量**：通过高度比较避免不必要的 setState 调用
2. **时机控制**：使用 `addPostFrameCallback` 确保在正确的时机进行测量
3. **状态管理**：合理使用 `setState` 或 `ValueNotifier` 管理高度状态
4. **内存管理**：及时释放 `ValueNotifier` 等资源

## 使用建议

- 对于简单场景，推荐使用方案一
- 对于复杂场景或需要更精细控制的情况，推荐使用方案二
- 可以根据具体需求选择合适的状态管理方式
- 注意处理边界情况，如组件销毁时的清理工作
