import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/provider/sign/sign_provider.dart';
import 'package:msmds_platform/utils/router_util.dart';

class MyCollectionWidget extends ConsumerWidget {
  const MyCollectionWidget({super.key});

  Widget _buildItem(String value, String title, Function() onPress) {
    return InkWell(
      onTap: onPress,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var numberData = ref.watch(myNumberShowingProvider);
    var collectCount = ref.watch(collectCountShowingProvider);
    var browsingCount = ref.watch(browsingCountShowingProvider);
    var userIntegral = ref.watch(userSignIntegralProvider);
    debugPrint("simpleIntegral: ${userIntegral?.todayIntegral}");
    return Container(
      margin: EdgeInsets.only(left: 10.w, right: 10.w),
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 25.w),
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(10.h),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildItem("${userIntegral?.simpleIntegral ?? 0}", "我的积分", () {
            RouterUtil.checkLogin(
              context,
              call: () {
                Navigator.pushNamed(context, CsRouter.myIntegralPage);
              },
              execute: true,
            );
          }),
          _buildItem("${numberData?.giftsNum ?? 0}", "我的红包", () {
            RouterUtil.checkLogin(
              context,
              call: () {
                Navigator.pushNamed(context, CsRouter.redPacketsPage);
              },
              execute: true,
            );
          }),
          _buildItem("${collectCount ?? 0}", "我的收藏", () {
            RouterUtil.checkLogin(
              context,
              call: () {
                Navigator.pushNamed(context, CsRouter.favoritesPage);
              },
              execute: true,
            );
          }),
          _buildItem("${browsingCount ?? 0}", "浏览足迹", () {
            RouterUtil.checkLogin(
              context,
              call: () {
                Navigator.pushNamed(context, CsRouter.historyPage);
              },
              execute: true,
            );
          }),
        ],
      ),
    );
  }
}
