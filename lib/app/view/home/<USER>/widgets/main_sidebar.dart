import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';
import 'package:msmds_platform/app/repository/modals/config/icon_config.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: main_sidebar
/// @Description: 首页侧边拦icon
/// @Author: frankylee
/// @CreateDate: 2024/4/8 16:45
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/8 16:45
/// @UpdateRemark: 更新说明
class MainSidebar extends ConsumerWidget {
  const MainSidebar({super.key});

  Widget _buildItem(BuildContext context, WidgetRef ref, IconConfig config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            InkWell(
              onTap: () {
                ref
                    .read(mainSidebarConfigProvider.notifier)
                    .closeSidebar(config);
              },
              child: Image.asset(
                sidebarClose,
                width: 12.w,
                height: 12.w,
              ),
            ),
            SizedBox(
              width: 4.w,
            ),
          ],
        ),
        SizedBox(
          height: 6.h,
        ),
        InkWell(
          onTap: () {
            ref
                .read(configItemClickProvider.notifier)
                .configItemClick(context, config);
          },
          child: Image.network(
            config.pictureUrl ?? "",
            width: 90.w,
            fit: BoxFit.contain,
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var sidebarData = ref.watch(mainSidebarConfigProvider);
    if (sidebarData == null || sidebarData.isEmpty) {
      return const SizedBox();
    }
    var children = sidebarData.map((e) => _buildItem(context, ref, e)).toList();
    return SizedBox(
      width: 90.w,
      child: Wrap(
        runSpacing: 15.h,
        children: children,
      ),
    );
  }
}
