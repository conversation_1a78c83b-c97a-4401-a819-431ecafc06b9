import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_meituan_response.dart';

class MeituanGroupItemWidget extends ConsumerWidget {
  final MeituanCouponItem item;
  final int index;
  final VoidCallback? onShareTap;
  final VoidCallback? onBuyTap;

  const MeituanGroupItemWidget({
    super.key,
    required this.item,
    required this.index,
    this.onShareTap,
    this.onBuyTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final originalPrice = item.originalPrice ?? 0;
    final sellPrice = item.sellPrice ?? 0;
    final discount = (originalPrice - sellPrice);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 主要内容行
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 商品图片
              _buildProductImage(),
              const SizedBox(width: 10),
              // 商品信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 品牌信息和标签行
                    _buildBrandAndLabel(),
                    const SizedBox(height: 4),
                    // 商品名称
                    _buildProductName(),
                    const SizedBox(height: 7),
                    // 优惠背景图片区域
                    _buildDiscountArea(discount),
                    const SizedBox(height: 4),
                    // 价格和返利区域
                    _buildPriceArea(),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 按钮行
          _buildButtonRow(),
        ],
      ),
    );
  }

  Widget _buildProductImage() {
    return Container(
      width: 115,
      height: 115,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: const Color(0xFFF2F2F2),
      ),
      child: item.headUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.network(
                item.headUrl!,
                width: 115,
                height: 115,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 115,
                  height: 115,
                  color: const Color(0xFFF2F2F2),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildBrandAndLabel() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 品牌信息
        if (item.brandName != null)
          Expanded(
            child: Row(
              children: [
                if (item.brandLogoUrl != null)
                  Container(
                    width: 12,
                    height: 12,
                    margin: const EdgeInsets.only(right: 3),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Image.network(
                      item.brandLogoUrl!,
                      width: 12,
                      height: 12,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(),
                    ),
                  ),
                Expanded(
                  child: Text(
                    item.brandName!,
                    style: const TextStyle(
                      color: Color(0xFF77777C),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          )
        else
          const Expanded(child: SizedBox()),
        // 标签
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFF93324), width: 0.6),
            borderRadius: BorderRadius.circular(1),
          ),
          child: Text(
            item.platform == 2 ? '到店' : '外卖',
            style: const TextStyle(
              color: Color(0xFFF93324),
              fontSize: 10,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProductName() {
    return RichText(
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        children: [
          if (item.couponNum != null)
            TextSpan(
              text: '${item.couponNum}张',
              style: const TextStyle(
                color: Color(0xFFFF3511),
                fontSize: 14,
              ),
            ),
          TextSpan(
            text: item.goodsName ?? '',
            style: const TextStyle(
              color: Color(0xFF3D3D47),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountArea(double discount) {
    return Container(
      height: 40,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: NetworkImage('https://alicdn.aimyself.cc/APPSHOW/fun_activity_price_new_bg.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // 中央优惠信息
          Center(
            child: discount == 0
                ? const Text(
                    '限时优惠',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        '优惠',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                      Text(
                        '¥${discount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
          ),
          // 左下角原价
          Positioned(
            bottom: 0,
            left: 7,
            child: Text(
              '¥${item.originalPrice ?? 0}',
              style: const TextStyle(
                color: Color(0xFFB0B8C8),
                fontSize: 10,
                decoration: TextDecoration.lineThrough,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceArea() {
    return Container(
      height: 30,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFFFEFEB),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // 售价
          RichText(
            text: TextSpan(
              children: [
                const TextSpan(
                  text: '¥',
                  style: TextStyle(
                    color: Color(0xFFFF3511),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                  text: '${item.sellPrice ?? 0}',
                  style: const TextStyle(
                    color: Color(0xFFFF3511),
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 6),
          // 返利标签
          Container(
            padding: const EdgeInsets.all(3),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF0F3),
              border: Border.all(color: const Color(0xFFF93324), width: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Text(
              '约返${item.commission ?? 0}元',
              style: const TextStyle(
                color: Color(0xFFF93324),
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 分享按钮
        GestureDetector(
          onTap: onShareTap,
          child: Container(
            width: 156,
            height: 31,
            decoration: BoxDecoration(
              color: const Color(0xFFFFF0F3),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFFFBEC9), width: 0.6),
            ),
            child: const Center(
              child: Text(
                '分享赚收益',
                style: TextStyle(
                  color: Color(0xFF000000),
                  fontSize: 13,
                ),
              ),
            ),
          ),
        ),
        // 购买按钮
        GestureDetector(
          onTap: onBuyTap,
          child: Container(
            width: 156,
            height: 31,
            decoration: BoxDecoration(
              color: const Color(0xFFF93324),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: Text(
                '领券购买',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}