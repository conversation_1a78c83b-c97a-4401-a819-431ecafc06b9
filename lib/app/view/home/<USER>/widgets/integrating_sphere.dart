import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/sign/sign_provider.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/integrating_sphere_item.dart';

import '../../../../repository/modals/sign/sign_layout_ball_item.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: integrating_sphere
/// @Description: 积分浮球
/// @Author: frankylee
/// @CreateDate: 2023/11/20 14:18
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 14:18
/// @UpdateRemark: 更新说明
class IntegratingSphere extends ConsumerWidget {
  const IntegratingSphere({
    super.key,
    this.signLayout,
  });

  final SignLayout? signLayout;

  // 积分球组装
  List<SignLayoutBallItem> _list(WidgetRef ref) {
    List<SignLayoutBallItem> balls = [];
    // var balls = signLayout?.mainRecoTaskList ?? [];
    // 筛选状态为未领取的积分球
    // balls = balls.where((e) => e.newStatus == 1).toList();
    // 合并购物返利的积分球
    var integralBulls = ref.watch(signLayoutBallProvider);
    if (integralBulls != null && integralBulls.isNotEmpty) {
      balls.addAll(integralBulls);
    }
    // 对列表进行裁剪，最多显示5个积分球
    if (balls.length > 5) {
      balls = balls.sublist(0, 5);
    }
    return balls;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var list = _list(ref);
    if (list.isEmpty) {
      return const SizedBox();
    }
    return SizedBox(
      height: 62.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: list
            .map((e) => IntegratingSphereItem(signLayoutBallItem: e))
            .toList(),
      ),
    );
  }
}
