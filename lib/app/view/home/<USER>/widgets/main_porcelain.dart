import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';
import 'package:msmds_platform/app/repository/modals/config/icon_config.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: main_porcelain
/// @Description: 首页中间卡片区
/// @Author: frankylee
/// @CreateDate: 2023/11/16 15:52
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/16 15:52
/// @UpdateRemark: 更新说明
class MainPorcelain extends ConsumerStatefulWidget {
  const MainPorcelain({super.key});

  @override
  MainPorcelainState createState() => MainPorcelainState();
}

class MainPorcelainState extends ConsumerState<MainPorcelain> {
  final ScrollController _scrollController = ScrollController();

  double _scrollbarPosition = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_updateScrollbarPosition);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateScrollbarPosition);
    _scrollController.dispose();
    super.dispose();
  }

  void _updateScrollbarPosition() {
    setState(() {
      var position = _scrollController.position.pixels /
          _scrollController.position.maxScrollExtent;
      if (position < 0) {
        position = 0;
      } else if (position > 1.0) {
        position = 1.0;
      }

      _scrollbarPosition = position * 17.w;
    });
  }

  /// item
  Widget _buildItem(IconConfig config) {
    return InkWell(
      onTap: () {
        ref
            .read(configItemClickProvider.notifier)
            .configItemClick(context, config);
      },
      child: Container(
        width: 70.w,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xFFE0E0E0), width: 1),
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "${config.title}",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            Image.network(
              config.pictureUrl ?? "",
              width: 40.w,
              height: 31.h,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ref.watch(fetchMainPorcelainConfigProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return Container(
          height: 76.h,
          margin: EdgeInsets.only(bottom: 12.h),
          child: Column(
            children: [
              Expanded(
                child: NotificationListener<ScrollNotification>(
                  onNotification: (notification) {
                    if (notification is ScrollUpdateNotification ||
                        notification is ScrollEndNotification) {
                      _updateScrollbarPosition();
                    }
                    return false;
                  },
                  child: ListView.separated(
                    controller: _scrollController,
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    itemBuilder: (context, index) {
                      return _buildItem(data[index]);
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return Container(
                        width: 9.w,
                      );
                    },
                    itemCount: data.length,
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 8.h)),
              Container(
                width: 29.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFEAEAEA),
                  borderRadius: BorderRadius.circular(2.h),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 12.w,
                      height: 4.h,
                      margin: EdgeInsets.only(left: _scrollbarPosition),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF0E37),
                        borderRadius: BorderRadius.circular(2.h),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
