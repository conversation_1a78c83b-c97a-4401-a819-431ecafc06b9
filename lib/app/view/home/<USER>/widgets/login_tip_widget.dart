import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/account/account.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: login_tip_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/14 14:19
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/14 14:19
/// @UpdateRemark: 更新说明
class LoginTipWidget extends ConsumerWidget {
  const LoginTipWidget({super.key});

  /// 点击登录
  void _clickLogin(BuildContext context) {
    Navigator.pushNamed(context, CsRouter.login);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Account? account = ref.watch(authProvider);
    if (account != null) {
      return Container();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      width: MediaQuery.of(context).size.width,
      height: 50.h,
      color: Colors.black.withAlpha(125),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "登录领取淘宝大额优惠券",
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
            ),
          ),
          SizedBox(
            width: 99.w,
            height: 34.h,
            child: GradientButton(
              onPress: () => _clickLogin(context),
              radius: 17.h,
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFE5640),
                  Color(0xFFFA2E1B),
                ],
              ),
              child: Text(
                "登录/注册",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
