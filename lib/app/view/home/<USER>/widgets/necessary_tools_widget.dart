import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/repository/modals/config/icon_config.dart';

class NecessaryToolsWidget extends ConsumerWidget {
  const NecessaryToolsWidget({super.key});

  Widget _buildItem(BuildContext context, WidgetRef ref, IconConfig item) {
    return SizedBox(
      width: (MediaQuery.of(context).size.width - 20.w) / 4,
      child: InkWell(
        onTap: () {
          ref
              .read(configItemClickProvider.notifier)
              .configItemClick(context, item);
        },
        child: Column(
          children: [
            Image.network(
              "${item.pictureUrl}",
              width: 24.w,
              fit: BoxFit.contain,
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              "${item.title}",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF333333),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchMeToolConfigProvider).when(
      data: (tools) {
        if (tools == null || tools.isEmpty) {
          return Container();
        }
        return Container(
          margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(10.h),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: 19.w,
                  top: 10.h,
                  bottom: 12.h,
                ),
                child: Text(
                  "必备工具",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF202123),
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              Divider(
                height: 1.h,
                color: const Color(0xFFF8F8F8),
              ),
              SizedBox(height: 5.h),
              Wrap(
                runSpacing: 12.h,
                children: tools
                    .map(
                      (item) => _buildItem(context, ref, item),
                    )
                    .toList(),
              ),
              SizedBox(height: 12.h),
            ],
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
