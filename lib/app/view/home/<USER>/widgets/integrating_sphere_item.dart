import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout_ball_item.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

import '../../../../provider/sign/sign_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: integrating_sphere_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 17:01
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 17:01
/// @UpdateRemark: 更新说明
class IntegratingSphereItem extends ConsumerStatefulWidget {
  const IntegratingSphereItem({
    super.key,
    required this.signLayoutBallItem,
  });

  final SignLayoutBallItem? signLayoutBallItem;

  @override
  IntegratingSphereItemState createState() => IntegratingSphereItemState();
}

class IntegratingSphereItemState extends ConsumerState<IntegratingSphereItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    /// 创建动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    /// 创建动画
    final random = Random();
    int randomNumber = random.nextInt(100) + 1;
    _animation = Tween<double>(
      begin: randomNumber % 2 == 0 ? 0 : 10.h,
      end: randomNumber % 2 == 0 ? 10.h : 0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    /// 循环执行动画
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var item = widget.signLayoutBallItem;
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return InkWell(
          onTap: () {
            ref.read(signLayoutBallProvider.notifier).getBallIntegral();
          },
          child: Container(
            margin: EdgeInsets.only(top: _animation.value),
            child: Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Image.network(
                      signIngBg,
                      width: 38.w,
                      height: 36.h,
                    ),
                    Text(
                      "+${item?.collectId != null ? item?.modifyNo?.toInt() : item?.integral?.toInt()}",
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: const Color(0xFF673812),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Text(
                    "${item?.collectId != null ? item?.modifyReason : item?.taskName}",
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF673812),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
