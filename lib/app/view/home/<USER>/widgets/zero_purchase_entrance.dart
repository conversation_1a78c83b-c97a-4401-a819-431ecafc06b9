import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/view/home/<USER>/attach/zero_purchase_tutorial.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: zero_purchase_entrance
/// @Description: 0元购入口
/// @Author: frankylee
/// @CreateDate: 2024/3/28 11:52
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 11:52
/// @UpdateRemark: 更新说明
class ZeroPurchaseEntrance extends ConsumerStatefulWidget {
  const ZeroPurchaseEntrance({super.key});

  @override
  ZeroPurchaseEntranceState createState() => ZeroPurchaseEntranceState();
}

class ZeroPurchaseEntranceState extends ConsumerState<ZeroPurchaseEntrance>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween(
      begin: 0.8,
      end: 1.2,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 倒计时显示
  Widget _buildCountdown() {
    var difference = ref.watch(validFreeBuyTimerProvider);
    String countdownStr = "";
    if (difference != null) {
      String days = "${difference.inDays}天";
      int hours = difference.inHours % 24;
      String hourStr = "${hours < 10 ? 0 : ''}$hours时";
      int minute = difference.inMinutes % 60;
      String minuteStr = "${minute < 10 ? 0 : ''}$minute分";
      int seconds = difference.inSeconds % 60;
      String secondsStr = "${seconds < 10 ? 0 : ''}$seconds秒";
      countdownStr = "$days$hourStr$minuteStr$secondsStr";
    }
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white, width: 0.5),
        borderRadius: BorderRadius.circular(11.r),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 3.h, horizontal: 5.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(11.r),
            ),
            child: Text(
              "距离过期",
              style: TextStyle(
                fontSize: 9.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFFFF0032),
              ),
            ),
          ),
          Padding(padding: EdgeInsets.only(right: 7.w)),
          Text(
            countdownStr,
            style: TextStyle(
              fontSize: 9.sp,
              color: Colors.white,
            ),
          ),
          Padding(padding: EdgeInsets.only(right: 9.w)),
        ],
      ),
    );
  }

  /// 商品显示
  Widget _buildGoods() {
    var data = ref.watch(freeBuyEntranceProvider);
    if (data == null || data.isEmpty) {
      return Container();
    }
    return Row(
      children: data
          .map((e) => Container(
                margin: EdgeInsets.only(left: 5.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                width: 76.w,
                child: Column(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.r),
                        topRight: Radius.circular(8.r),
                      ),
                      child: Image.network(
                        e?.cover ?? "",
                        width: 76.w,
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 2.h)),
                    Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: const Color(0xFFF93324),
                        ),
                        children: [
                          const TextSpan(text: "¥"),
                          TextSpan(
                            text: "0",
                            style: TextStyle(fontSize: 18.sp),
                          ),
                          WidgetSpan(
                            child: Padding(
                              padding: EdgeInsets.only(right: 5.w),
                            ),
                          ),
                          TextSpan(
                            text: "¥${e?.wlPrice}",
                            style: TextStyle(
                              decoration: TextDecoration.lineThrough,
                              fontSize: 10.sp,
                              color: const Color(0xFF999999),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 2.h)),
                  ],
                ),
              ))
          .toList(),
    );
  }

  /// 右边文本按钮
  Widget _buildRightText() {
    return Padding(
      padding: EdgeInsets.fromLTRB(10.w, 5.h, 10.w, 0),
      child: Column(
        children: [
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (rect) {
              return const LinearGradient(
                colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFFCFDDF),
                  Color(0xFFFDFFA7),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                tileMode: TileMode.mirror,
              ).createShader(rect);
            },
            child: Text.rich(
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                letterSpacing: 2,
              ),
              TextSpan(
                children: [
                  TextSpan(
                    text: "0",
                    style: TextStyle(fontSize: 20.sp),
                  ),
                  const TextSpan(text: "元购好物"),
                ],
              ),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 4.h)),
          Text(
            "限时抢购",
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFFFFFFDD),
            ),
          ),
          Padding(padding: EdgeInsets.only(bottom: 13.h)),
          ScaleTransition(
            scale: _animation,
            child: Image.network(
              homeZeroBtn,
              width: 75.w,
              height: 22.h,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var showAttach = ref.watch(freeBuyEntranceTutorialProvider);
    if (showAttach) {
      ZeroPurchaseTutorial.showAttach(context);
      ref.invalidate(freeBuyEntranceTutorialProvider);
    }
    return ref.watch(fetchValidFreeBuyProvider).when(
      data: (data) {
        if (data == null || data.state != 1) {
          /// 无0元购资格、已使用或者资格过期
          return Container();
        }
        return InkWell(
          onTap: () {
            Navigator.pushNamed(context, CsRouter.zeroPurchaseActivity);
          },
          child: Container(
            width: 355.w,
            height: 158.h,
            margin: EdgeInsets.only(bottom: 13.h),
            decoration: BoxDecoration(
              image: DecorationImage(
                onError: (o, s) {},
                image: const NetworkImage(homeZeroBg),
                fit: BoxFit.contain,
              ),
            ),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 12.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Image.network(
                            homeZeroTitle,
                            width: 65.w,
                            height: 18.h,
                            fit: BoxFit.contain,
                          ),
                          Padding(padding: EdgeInsets.only(right: 7.w)),
                          Text(
                            "每人限购1件",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      _buildCountdown(),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Padding(padding: EdgeInsets.only(left: 3.w)),
                    _buildGoods(),
                    _buildRightText(),
                  ],
                ),
              ],
            ),
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
