import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/utils/router_util.dart';
import '../../../../../config/constant.dart';
import '../../../../../widgets/button/gradient_button.dart';
import '../../../../provider/account/auth_provider.dart';
import '../../../../provider/config/config_provider.dart';
import '../../../../provider/conversion/link_conversion_provider.dart';
import '../../../../repository/modals/goods/goods_tb_pkg.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: main_informer_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/17 12:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/17 12:12
/// @UpdateRemark: 更新说明
class MainInformerItem extends ConsumerWidget {
  const MainInformerItem({
    super.key,
    required this.index,
    required this.goodsTbPkg,
  });

  final int index;
  final GoodsTbPkg? goodsTbPkg;

  // 显示徽标
  Widget _buildTopRank() {
    if (index <= 3) {
      return Image.network(
        "${Constant.msmdsAliCdn}/APPSHOW/home_item_top_0$index.png",
        width: 27.w,
        height: 34.h,
      );
    }
    if (index <= 99) {
      return Stack(
        alignment: Alignment.topCenter,
        children: [
          Image.network(
            "${Constant.msmdsAliCdn}/APPSHOW/home_item_rank_bg.png",
            width: 24.w,
            height: 29.h,
          ),
          Positioned(
            top: 2.h,
            child: Text(
              "$index",
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
              ),
            ),
          ),
        ],
      );
    }
    return SizedBox(
      width: 24.w,
      height: 29.h,
    );
  }

  // 显示2小时成交量
  Widget _buildTwoHourSale() {
    if (goodsTbPkg?.twoHourSale == null) {
      return const SizedBox();
    }
    return Text(
      "近2小时成交${goodsTbPkg?.twoHourSale}件",
      style: TextStyle(
        fontSize: 12.sp,
        color: const Color(0xFFBA7D52),
      ),
    );
  }

  // 标题和购买按钮
  Widget _buildTitleAndBuy(BuildContext context, WidgetRef ref) {
    Widget buyBtn = Row(
      children: [
        GradientButton(
          onPress: () {
            RouterUtil.checkLogin(
              context,
              call: () {
                ref.read(tbConversionProvider.notifier).tbChangeUrlByGoodsId(
                      goodsTbPkg?.goodsUrl,
                      couponUrl: goodsTbPkg?.couponUrl,
                    );
              },
              execute: true,
            );
          },
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          gradient: const LinearGradient(colors: [
            Color(0xFFE5EFFF),
            Color(0xFFE5EFFF),
          ]),
          border: Border.all(
            color: const Color(0x901E3FF7),
            width: 1,
          ),
          shadow: false,
          child: Text(
            "去购买>",
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF1E3FF7),
            ),
          ),
        ),
      ],
    );
    return Column(
      children: [
        Text(
          "${goodsTbPkg?.title}",
          style: TextStyle(
            fontSize: 13.sp,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(
          height: 2.h,
        ),
        buyBtn,
        SizedBox(
          height: 2.h,
        ),
        if (goodsTbPkg?.purchaseDescription != null)
          Text(
            "${goodsTbPkg?.purchaseDescription}",
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF333333),
            ),
          ),
        if (goodsTbPkg?.purchaseDescription != null) buyBtn,
      ],
    );
  }

  // 价格和返利
  Widget _buildPriceAndCommission(WidgetRef ref) {
    var userInfo = ref.read(authProvider);
    var vipLevel = userInfo?.data?.vipLevel ?? -1;
    var isVip = vipLevel >= 0;
    return Row(
      children: [
        Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF999999),
            ),
            children: [
              const TextSpan(text: "到手价:"),
              TextSpan(
                text: "${goodsTbPkg?.priceAfterReceive}元",
                style: const TextStyle(
                  color: Color(0xFFFA321F),
                ),
              ),
            ],
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 8.w)),
        Expanded(
          child: Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF999999),
              ),
              children: [
                const TextSpan(text: "约返:"),
                TextSpan(
                  text:
                      "${isVip ? goodsTbPkg?.commission : goodsTbPkg?.noVipCommission}元",
                  style: const TextStyle(
                    color: Color(0xFFFA321F),
                  ),
                ),
              ],
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // 底部操作按钮，保存图片&分享
  Widget _buildBottomAction(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color(0xFFCCCCCC),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(17.r),
          ),
          child: InkWell(
            onTap: () {
              ref
                  .read(homeTabPkgGoodsProvider.notifier)
                  .saveGoodsImageToGallery(
                    goodsTbPkg?.cover ?? goodsTbPkg?.goodsImg,
                  );
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 9.w,
                vertical: 7.h,
              ),
              child: Row(
                children: [
                  Image.network(
                    "${Constant.msmdsAliCdn}/APPSHOW/home_item_download.png",
                    width: 12.w,
                    height: 12.w,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    "保存图片",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 4.w)),
        Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xFFFF6B2B),
                Color(0xFFFF0F77),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(17.r),
          ),
          child: InkWell(
            onTap: () {
              RouterUtil.checkLogin(
                context,
                call: () {
                  ref
                      .read(tbConversionProvider.notifier)
                      .tbShareChangeUrlByGoodsId(goodsTbPkg);
                },
                execute: true,
              );
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 14.w,
                vertical: 7.h,
              ),
              child: Row(
                children: [
                  Image.network(
                    "${Constant.msmdsAliCdn}/appNormal/good_detail_share.png",
                    width: 13.w,
                    height: 13.w,
                    color: Colors.white,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    "分享赚",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFFFFFFFF),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      color: const Color(0xFFF9F9F9),
      child: Container(
        margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 6.h),
        padding: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildTopRank(),
                _buildTwoHourSale(),
              ],
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: _buildTitleAndBuy(context, ref),
                ),
                Padding(padding: EdgeInsets.only(right: 10.w)),
                Image.network(
                  "${goodsTbPkg?.cover ?? goodsTbPkg?.goodsImg}",
                  width: 104.w,
                  height: 104.w,
                ),
              ],
            ),
            Padding(padding: EdgeInsets.only(bottom: 2.h)),
            Row(
              children: [
                Expanded(
                  child: _buildPriceAndCommission(ref),
                ),
                _buildBottomAction(context, ref),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
