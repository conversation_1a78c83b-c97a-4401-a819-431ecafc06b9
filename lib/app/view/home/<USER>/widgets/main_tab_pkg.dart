import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/config/config_provider.dart';

import '../../../../../widgets/tabbar/rect_tab_indicator.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.main.widgets
/// @ClassName: main_tab_pkg
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/8/26 17:00
/// @UpdateUser: frankylee
/// @UpdateData: 2024/8/26 17:00
/// @UpdateRemark: 更新说明
class MainTabPkg extends ConsumerWidget {
  const MainTabPkg({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchMainPkgConfigProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return Container();
        }
        return DefaultTabController(
          length: data.length,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            color: const Color(0xFFF9F9F9),
            height: 40.h,
            child: TabBar(
              indicatorSize: TabBarIndicatorSize.label,
              indicator: RectTabIndicator(
                indicatorSize: 5.h,
                offset: 5,
                gradient: const LinearGradient(
                  colors: [
                    Colors.white,
                    Color(0xFFFF0E38),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              unselectedLabelColor: const Color(0xFF333333),
              unselectedLabelStyle: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
              labelStyle: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w800,
              ),
              labelColor: const Color(0xFFF93324),
              tabs: data.map((e) => Tab(text: e.pkgName)).toList(),
              onTap: (index) {
                ref
                    .read(homeTabPkgGoodsProvider.notifier)
                    .setCurrentHomeTab(data[index]);
              },
            ),
          ),
        );
      },
      error: (o, s) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
