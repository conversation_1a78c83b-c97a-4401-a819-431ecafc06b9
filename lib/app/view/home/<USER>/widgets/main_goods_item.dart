import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/conversion/link_conversion_provider.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods.dart';
import 'package:msmds_platform/utils/router_util.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: main_informer_item
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/17 12:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/17 12:12
/// @UpdateRemark: 更新说明
class MainGoodsItem extends ConsumerWidget {
  const MainGoodsItem({
    super.key,
    required this.index,
    required this.goods,
    this.source = "main",
  });

  final int index;

  final Goods? goods;

  final String? source;

  /// 商品图片
  Widget _buildCover() {
    if (goods?.cover == null) {
      return Container(
        width: 110.w,
        height: 110.w,
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(60),
          borderRadius: BorderRadius.circular(6),
        ),
      );
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(6),
      child: Image.network(
        "${goods?.cover}",
        width: 110.w,
        height: 110.w,
        fit: BoxFit.contain,
        errorBuilder: (c, o, s) {
          return Container(
            width: 110.w,
            height: 110.w,
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(60),
              borderRadius: BorderRadius.circular(6),
            ),
          );
        },
      ),
    );
  }

  /// 商品标题
  Widget _buildTitle() {
    var platformLogo = "";
    if (goods?.goodsType == 2) {
      platformLogo = "京东";
    } else if (goods?.goodsType == 3) {
      platformLogo = "拼多多";
    } else if (goods?.goodsType == 1) {
      platformLogo = "淘宝";
    }
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF333333),
          height: 1.2,
        ),
        children: [
          const TextSpan(text: ""),
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Container(
              margin: const EdgeInsets.only(right: 4),
              padding: const EdgeInsets.symmetric(horizontal: 2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFF3F4E),
                    Color(0xFFFA2C1C),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: Text(
                platformLogo,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          TextSpan(text: "${goods?.goodsName}"),
        ],
      ),
      style: TextStyle(
        fontSize: 14.sp,
        color: const Color(0xFF333333),
        height: 1.2,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 优惠券返利信息
  Widget _buildCouponInfo() {
    List<Widget> child = [];
    if (goods != null &&
        goods?.goodsCoupon != null &&
        goods!.goodsCoupon!.isNotEmpty) {
      child.add(
        Container(
          margin: EdgeInsets.only(right: 4.w),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFF93324)),
            borderRadius: BorderRadius.circular(2),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 2),
                decoration: const BoxDecoration(
                  color: Color(0xFFF93324),
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Text(
                  "券",
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 3.w)),
              Text(
                "${goods?.goodsCoupon?.first.couponAmountInfo}",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFFF93324),
                ),
              ),
              Padding(padding: EdgeInsets.only(right: 5.w)),
            ],
          ),
        ),
      );
    }
    child.add(
      Container(
        margin: EdgeInsets.only(right: 4.w),
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFF93324)),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Text(
          "约返${goods?.userCommission}元",
          style: TextStyle(
            fontSize: 10.sp,
            color: const Color(0xFFF93324),
          ),
        ),
      ),
    );
    return Row(
      children: child,
    );
  }

  /// 价格显示
  Widget _buildPrice() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF999999),
        ),
        children: [
          TextSpan(
            text: "${goods?.receivedPrice}元",
            style: TextStyle(
              fontSize: 15.sp,
              color: const Color(0xFFF93324),
            ),
          ),
          WidgetSpan(
            child: Padding(
              padding: EdgeInsets.only(right: 4.w),
            ),
          ),
          TextSpan(
            text: "¥${goods?.originalPrice}",
            style: const TextStyle(
              decoration: TextDecoration.lineThrough,
              decorationThickness: 1.2,
              decorationColor: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 店铺信息
  Widget _buildShop() {
    var platformLogo = "";
    if (goods?.goodsType == 2) {
      platformLogo = "京东";
    } else if (goods?.goodsType == 3) {
      platformLogo = "拼多多";
    } else if (goods?.goodsType == 1) {
      platformLogo = "淘宝";
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            "$platformLogo｜${goods?.shopName}",
            style: TextStyle(
              fontSize: 10.sp,
              color: const Color(0xFF999999),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        // Text(
        //   "已售${goods?.saleVolume}",
        //   style: TextStyle(
        //     fontSize: 10.sp,
        //     color: const Color(0xFF999999),
        //   ),
        // ),
      ],
    );
  }

  /// 商品点击
  void _goodsItemTap(WidgetRef ref) {
    var couponList = goods?.goodsCoupon;
    var materialId = couponList != null && couponList.isNotEmpty
        ? goods?.goodsUrl
        : goods?.completeGoodsId;
    var couponUrl = couponList != null && couponList.isNotEmpty
        ? couponList.first.couponUrl
        : null;
    ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
          goods?.goodsType,
          materialId: materialId,
          couponUrl: couponUrl,
          goodsSign: goods?.completeGoodsId,
          goodsId: goods?.completeGoodsId,
        );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: index == 0 && source == "main" ? null : const Color(0xFFF9F9F9),
        gradient: index == 0 && source == "main"
            ? const LinearGradient(
                colors: [Colors.white, Color(0xFFF9F9F9)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              )
            : null,
        // borderRadius: BorderRadius.only(
        //   topLeft: Radius.circular(index == 0 ? 16 : 0),
        //   topRight: Radius.circular(index == 0 ? 16 : 0),
        // ),
      ),
      child: InkWell(
        onTap: () {
          RouterUtil.checkLogin(
            context,
            call: () => _goodsItemTap(ref),
          );
        },
        // borderRadius: BorderRadius.only(
        //   topLeft: Radius.circular(index == 0 ? 16 : 0),
        //   topRight: Radius.circular(index == 0 ? 16 : 0),
        // ),
        child: Container(
          margin: EdgeInsets.only(
            top: index == 0 && source != "main" ? 10.h : 0,
          ),
          padding: const EdgeInsets.fromLTRB(8, 0, 8, 10),
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCover(),
                Padding(padding: EdgeInsets.only(right: 10.w)),
                Expanded(
                  child: SizedBox(
                    height: 110.w,
                    child: Column(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _buildTitle(),
                              _buildCouponInfo(),
                              _buildPrice(),
                            ],
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(bottom: 10.h)),
                        _buildShop(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
