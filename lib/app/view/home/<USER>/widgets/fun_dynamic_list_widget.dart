import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../provider/fun/fun_list_provider.dart';
import 'meituan_group_list_widget.dart';

class FunDynamicListWidget extends ConsumerStatefulWidget {
  const FunDynamicListWidget({super.key});

  @override
  ConsumerState<FunDynamicListWidget> createState() => _FunDynamicListWidgetState();
}

class _FunDynamicListWidgetState extends ConsumerState<FunDynamicListWidget> {
  ScrollController? _scrollController;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 获取外层CustomScrollView的ScrollController
    _scrollController = PrimaryScrollController.maybeOf(context);
  }

  @override
  Widget build(BuildContext context) {
    final tabConfigState = ref.watch(manageFunTabConfigProvider);
    final tabSelection = ref.watch(tabSelectionStateProvider);

    return tabConfigState.when(
      data: (tabConfig) {
        if (tabConfig == null) {
          return SliverToBoxAdapter(
            child: _buildErrorWidget('标签配置加载失败'),
          );
        }

        // 获取当前选中的主标签和子标签
        final mainTabIndex = tabSelection.mainTabIndex;
        final childTabIndex = tabSelection.childTabIndex;
        print("当前标签索引: $mainTabIndex, $childTabIndex");

        if (mainTabIndex >= tabConfig.mainTab.tabs.length ||
            childTabIndex >= tabConfig.childrenTab.tabs.length) {
          return SliverToBoxAdapter(
            child: _buildErrorWidget('标签索引超出范围'),
          );
        }

        final currentMainTab = tabConfig.mainTab.tabs[mainTabIndex];
        final currentChildTab = tabConfig.childrenTab.tabs[childTabIndex];
        final currentSort = tabConfig.currentSort;
        print("当前标签信息${currentMainTab.code} ${currentSort?.sortName}");

        // 检查是否是美团相关的标签
        final tabCode = currentMainTab.code ?? '';
        print('tabCode: $tabCode ');
        if (tabCode == 'meituan_groupBuying' || tabCode == 'meituan_sharpshooter') {
          // 生成唯一的key来确保标签切换时重新创建widget
          final widgetKey = ValueKey('${currentMainTab.code}_${currentChildTab.code}_${currentSort?.sortField ?? 'default'}');

          return MeituanGroupListWithPaginationWidget(
            key: widgetKey,
            mainTabConfig: currentMainTab,
            childTabConfig: currentChildTab,
            latitude: 39.9042, // TODO: 使用真实定位数据
            longitude: 116.4074, // TODO: 使用真实定位数据
            sortOption: currentSort,
            scrollController: _scrollController, // 传入外层的ScrollController
          );
        } else {
          // 非美团标签显示占位符
          return SliverToBoxAdapter(
            child: _buildPlaceholderContent('请选择美团相关标签查看内容'),
          );
        }
      },
      loading: () => SliverToBoxAdapter(
        child: Container(
          height: 200,
          alignment: Alignment.center,
          child: const CircularProgressIndicator(),
        ),
      ),
      error: (error, stackTrace) => SliverToBoxAdapter(
        child: _buildErrorWidget('加载失败: $error'),
      ),
    );
  }
    /// 构建错误提示组件
  Widget _buildErrorWidget(String message) {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        message,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }
    Widget _buildPlaceholderContent(String text) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Text(
          text,
          style: const TextStyle(fontSize: 16, color: Colors.grey),
        ),
      ),
    );
  }
}