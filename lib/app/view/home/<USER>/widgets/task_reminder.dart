import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: task_reminder
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 15:11
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 15:11
/// @UpdateRemark: 更新说明
class TaskReminder extends StatelessWidget {
  const TaskReminder({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50.h,
      margin: EdgeInsets.fromLTRB(10.w, 10.h, 10.w, 0),
      padding: EdgeInsets.only(left: 38.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Row(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(
                    TextSpan(
                      children: const [
                        TextSpan(text: "再完成"),
                        TextSpan(
                          text: "2",
                          style: TextStyle(
                            color: Color(0xFFF93324),
                          ),
                        ),
                        TextSpan(text: "个任务 开箱得红包"),
                      ],
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 5.h)),
                  Container(
                    width: 252.w,
                    height: 12.h,
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFDFDB),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(9),
                        bottomLeft: Radius.circular(9),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 2 / 4 * 252.w,
                          height: 12.h,
                          alignment: Alignment.centerRight,
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFFFF5640),
                                Color(0xFFFF786A),
                                Color(0xFFFB2D1A),
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.all(Radius.circular(9)),
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(right: 6.w),
                            child: Text(
                              "2/4",
                              style: TextStyle(
                                fontSize: 8.sp,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ],
          ),
          Positioned(
            right: 33.w,
            child: Image.network(
              signBag,
              width: 40.w,
              height: 40.h,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}
