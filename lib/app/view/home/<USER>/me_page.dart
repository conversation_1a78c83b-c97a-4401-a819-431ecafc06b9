import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/my_benefits_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/my_swiper_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/order_module_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/product_module_widget.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/user_header_widget.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import 'widgets/my_collection_widget.dart';
import 'widgets/necessary_tools_widget.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: me_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/10 14:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/10 14:06
/// @UpdateRemark: 更新说明
class MePage extends ConsumerStatefulWidget {
  const MePage({super.key});

  @override
  MePageState createState() => MePageState();
}

class MePageState extends ConsumerState<MePage> {
  @override
  void initState() {
    super.initState();

    // WidgetsBinding.instance.addPostFrameCallback(
    //       (timeStamp) {
    //     /// 显示我的页配置弹窗
    //     if (GlobalConfig.account != null) {
    //       ref.read(mainAndMeOrdinaryDialogProvider.notifier).showMeDialog();
    //     }
    //   },
    // );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9F9F9),
      body: AnnotatedRegion(
        value: const SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
        ),
        child: CustomListView(
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          onRefresh: () async {
            ref.read(walletInfoProvider.notifier).loadWalletInfo();
            ref.read(myNumberShowingProvider.notifier).getNumber();
            ref.read(collectCountShowingProvider.notifier).getCollectCount();
            ref.read(browsingCountShowingProvider.notifier).getBrowsingCount();
            ref.refresh(fetchNewestOrderProvider).unwrapPrevious();
          },
          header: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 20.h,
                ),
                child: const UserHeaderWidget(),
              ),
              Padding(
                padding: EdgeInsets.only(top: 20.h),
                child: const MyCollectionWidget(),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.h),
                child: const MyBenefitsWidget(),
              ),
              Padding(
                padding: EdgeInsets.only(top: 10.h),
                child: const OrderModuleWidget(),
              ),
              const ProductModuleWidget(),
              const MySwiperWidget(),
              const NecessaryToolsWidget(),
              SizedBox(height: 16.h),
            ],
          ),
        ),
      ),
    );
  }
}
