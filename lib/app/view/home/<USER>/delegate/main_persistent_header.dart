import 'package:flutter/material.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/main_header.dart';

class MainPersistentHeader extends SliverPersistentHeaderDelegate {
  final double max;
  final double min;
  final ScrollController scrollController;

  MainPersistentHeader({
    this.max = 50,
    this.min = 40,
    required this.scrollController,
  }) : assert(max >= min);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    final double deltaExtent = max - min;
    // 0.0 -> Expanded
    // 1.0 -> Collapsed to toolbar
    final double t = (shrinkOffset / deltaExtent).clamp(0.0, 1.0);

    final double scaleValue = Tween<double>(begin: 1, end: 1.42).transform(t);

    return Align(
      child: AnimatedCrossFade(
        firstChild: MainHeader(
          t: t,
          deltaExtent: deltaExtent,
          scaleValue: scaleValue,
          scrollController: scrollController,
        ),
        secondChild: const SizedBox(),
        crossFadeState: CrossFadeState.showFirst,
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  @override
  double get maxExtent => max;

  @override
  double get minExtent => min;

  @override
  bool shouldRebuild(covariant MainPersistentHeader oldDelegate) =>
      max != oldDelegate.max || min != oldDelegate.min;
}
