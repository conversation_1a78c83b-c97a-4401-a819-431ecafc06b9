import 'package:flutter/material.dart';

class MainTabPersistentBuilder extends SliverPersistentHeaderDelegate {
  final PreferredSizeWidget? child;

  MainTabPersistentBuilder({this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.white, Color(0xFFF5F5F5)]
        )
      ),
      // color: overlapsContent ? Colors.white : Colors.transparent,
      child: child!
    ) ;
  }

  @override
  double get maxExtent => child!.preferredSize.height;

  @override
  double get minExtent => child!.preferredSize.height;

  @override
  bool shouldRebuild(covariant MainTabPersistentBuilder oldDelegate) =>
      child != oldDelegate.child;
}
