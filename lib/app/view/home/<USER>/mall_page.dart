import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/mall_tab.dart';
import 'package:msmds_platform/widgets/keepalive/keep_alive_wrapper.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: mall_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/10 14:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/10 14:06
/// @UpdateRemark: 更新说明
class MallPage extends StatefulWidget {
  const MallPage({super.key});

  @override
  MallPageState createState() => MallPageState();
}

class MallPageState extends State<MallPage> {
  final List<String> tabs = ["推荐", "美食/点餐", "购物商城", "出行娱乐"];

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          titleSpacing: 0,
          toolbarHeight: kMinInteractiveDimension,
          title: TabBar(
            isScrollable: true,
            indicator: const BoxDecoration(),
            unselectedLabelColor: const Color(0xFF000000),
            unselectedLabelStyle:
                TabBarTheme.of(context).unselectedLabelStyle?.copyWith(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
            labelStyle: TabBarTheme.of(context).labelStyle?.copyWith(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w800,
                ),
            labelColor: const Color(0xFFFF0E38),
            tabs: tabs.map((e) => Tab(text: e)).toList(),
          ),
        ),
        body: TabBarView(
          children: tabs
              .map(
                (e) => const KeepAliveWrapper(child: MallTab()),
              )
              .toList(),
        ),
      ),
    );
  }
}
