import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: bind_phone_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 14:21
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 14:21
/// @UpdateRemark: 更新说明
class BindPhoneDialog {
  /// 绑定手机号
  static void showBindPhoneDialog(String title) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "bind_phone_dialog",
      builder: (context) {
        return AnimatedPadding(
          padding: MediaQuery.of(context).viewInsets,
          duration: const Duration(milliseconds: 100),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 295.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.fromLTRB(0, 10.h, 10.w, 0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          InkWell(
                            child: Icon(
                              Icons.close_rounded,
                              size: 16.r,
                              color: const Color(0xFFC8C8C8),
                            ),
                            onTap: () {
                              SmartDialog.dismiss(tag: "bind_phone_dialog");
                            },
                          )
                        ],
                      ),
                    ),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 24.h)),
                    const PhoneNumber(),
                    Padding(padding: EdgeInsets.only(bottom: 16.h)),
                    Row(
                      children: [
                        Padding(padding: EdgeInsets.only(left: 21.w)),
                        const Expanded(
                          child: VerificationCode(),
                        ),
                        const VerCodeWidget(),
                        Padding(padding: EdgeInsets.only(right: 21.w)),
                      ],
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 30.h)),
                    Consumer(
                      builder: (context, ref, child) {
                        return GradientButton(
                          onPress: () {
                            ref.read(bindPhoneProvider.notifier).bindPhone();
                          },
                          margin: EdgeInsets.symmetric(horizontal: 28.w),
                          padding: EdgeInsets.symmetric(vertical: 10.h),
                          radius: 25,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFE5640),
                              Color(0xFFFA2E1B),
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          child: Text(
                            "确认绑定",
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                    ),
                    Padding(padding: EdgeInsets.only(bottom: 20.h)),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 手机号输入
class PhoneNumber extends ConsumerWidget {
  const PhoneNumber({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 21.w),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(6),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: TextFormField(
          onChanged: (value) {
            debugPrint("手机号: $value");
            ref.watch(bindPhoneEditingProvider.notifier).setPhone(value);
          },
          style: TextStyle(fontSize: 14.sp),
          decoration: InputDecoration(
            border: InputBorder.none,
            counterText: "",
            hintStyle: TextStyle(
              color: const Color(0xFFB4B4B4),
              fontSize: 14.sp,
            ),
            hintText: "请输入手机号",
          ),
        ),
      ),
    );
  }
}

/// 验证码输入
class VerificationCode extends ConsumerWidget {
  const VerificationCode({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: EdgeInsets.only(right: 23.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(6),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: TextFormField(
        // controller: _editingController,
        onChanged: (value) {
          debugPrint("验证码: $value");
          ref
              .watch(bindPhoneVerificationEditingProvider.notifier)
              .setVerification(value);
        },
        style: TextStyle(fontSize: 14.sp),
        decoration: InputDecoration(
          border: InputBorder.none,
          counterText: "",
          hintStyle: TextStyle(
            color: const Color(0xFFB4B4B4),
            fontSize: 14.sp,
          ),
          hintText: "请输入验证码",
        ),
      ),
    );
  }
}

/// 验证码获取按钮
class VerCodeWidget extends ConsumerWidget {
  const VerCodeWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    int ms = ref.watch(verCodeTimerProvider);
    if (ms != 0) {
      return Container(
        alignment: Alignment.center,
        width: 72.w,
        child: Text(
          "${ms}s",
          style: TextStyle(
            fontSize: 15.sp,
            color: const Color(0xFF007AFF),
          ),
        ),
      );
    }
    return InkWell(
      onTap: () {
        ref.read(bindPhoneProvider.notifier).getVerification();
      },
      child: Text(
        "发送验证码",
        style: TextStyle(
          fontSize: 15.sp,
          color: const Color(0xFF007AFF),
        ),
      ),
    );
  }
}
