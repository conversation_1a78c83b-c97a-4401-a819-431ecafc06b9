import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/sign/koi_red_provider.dart';
import 'package:msmds_platform/config/constant.dart';

import '../../../../../common/img/icon_addres.dart';
import '../../../../repository/modals/sign/luck_gift_item.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> <PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.dialog
/// @ClassName: koi_red_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 17:32
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 17:32
/// @UpdateRemark: 更新说明
class KoiRedDialog {
  static void show(LuckGiftItem? item) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "koi_red_dialog",
      builder: (context) {
        return KoiDialogWidget(item: item);
      },
    );
  }
}

// 弹窗显示组件
class KoiDialogWidget extends ConsumerWidget {
  const KoiDialogWidget({
    super.key,
    this.item,
  });

  final LuckGiftItem? item;

  // 未开启
  Widget _buildContentNotOpen(WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.only(top: 60.h),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              ref
                  .read(receiveKoiRedPacketProvider.notifier)
                  .receive(item?.localIndex);
            },
            child: Image.network(
              "${Constant.msmdsAliCdn}/appNormal/Koiopen.png",
              width: 170.w,
              height: 170.w,
            ),
          ),
          SizedBox(height: 40.h),
          Image.network(
            "${Constant.msmdsAliCdn}/APPSHOW/koi_need_integral.png",
            width: 207.w,
            height: 28.h,
          )
        ],
      ),
    );
  }

  // 已经开启
  Widget _buildContentOpen(LuckGiftItem? luckGiftItem) {
    return Column(
      children: [
        SizedBox(height: 34.h),
        Text(
          "抢到",
          style: TextStyle(
            fontSize: 60.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFFE44324),
          ),
        ),
        SizedBox(height: 33.h),
        Text(
          "${luckGiftItem?.name}",
          style: TextStyle(
            fontSize: 48.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFFE44324),
          ),
        ),
        SizedBox(height: 100.h),
        InkWell(
          onTap: () {
            SmartDialog.dismiss(tag: "koi_red_dialog");
          },
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.network(
                "${Constant.msmdsAliCdn}/appNormal/KoiitemBtn.png",
                width: 196.w,
                height: 50.h,
              ),
              Text(
                "继续开红包",
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFFE44324),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var prizeData = ref.watch(receiveKoiRedPacketProvider);

    Widget child = prizeData == null
        ? _buildContentNotOpen(ref)
        : _buildContentOpen(prizeData);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          alignment: Alignment.topCenter,
          children: [
            Container(
              width: 375.w,
              height: 445.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(
                    "${Constant.msmdsAliCdn}/appNormal/Koiopenbg1.png",
                  ),
                ),
              ),
            ),
            Stack(
              alignment: Alignment.topCenter,
              children: [
                Image.network(
                  "${Constant.msmdsAliCdn}/appNormal/${prizeData != null ? "Koiopenbg3" : "Koiopenbg2"}.png",
                  width: 296.w,
                  height: 379.h,
                ),
                child,
              ],
            ),
            Positioned(
              bottom: 25.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 12.h, right: 12.w),
                    child: InkWell(
                      onTap: () {
                        SmartDialog.dismiss(tag: "koi_red_dialog");
                      },
                      child: Image.asset(
                        closeBlack,
                        width: 14.w,
                        height: 14.h,
                        color: Colors.white,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
