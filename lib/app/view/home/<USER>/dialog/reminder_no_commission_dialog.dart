import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../../../../widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: reminder_no_commission_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 16:01
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 16:01
/// @UpdateRemark: 更新说明
class ReminderNoCommissionDeleteDialog {
  /// 无返现提醒
  static void reminderNoCommissionDialog(Function onPress) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: true,
      tag: "reminder_no_commission_dialog",
      builder: (context) {
        return Container(
          width: 296.w,
          padding: EdgeInsets.symmetric(horizontal: 39.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 28.h, bottom: 30.h),
                child: Text(
                  "现在购买将失去返利哦",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
              GradientButton(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                onPress: () {
                  SmartDialog.dismiss(tag: "reminder_no_commission_dialog");
                },
                shadow: false,
                radius: 20,
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFE553F),
                    Color(0xFFFA2E1B),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                child: Text(
                  "再等等",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
              ),
              SizedBox(
                height: 8.h,
              ),
              InkWell(
                onTap: () {
                  SmartDialog.dismiss(tag: "reminder_no_commission_dialog");
                  onPress();
                },
                child: Text(
                  "不要返利，直接购买",
                  style: TextStyle(
                    fontSize: 14.sp,
                    decoration: TextDecoration.underline,
                    color: const Color(0xFF999999),
                  ),
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 20.h)),
            ],
          ),
        );
      },
    );
  }
}
