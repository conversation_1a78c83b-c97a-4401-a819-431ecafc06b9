import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/config/constant.dart';

import '../../../../../common/img/icon_addres.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.dialog
/// @ClassName: koi_red_empty_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 17:32
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 17:32
/// @UpdateRemark: 更新说明
class KoiRedEmptyDialog {
  static void show() {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "koi_red_empty_dialog",
      builder: (context) {
        return const KoiEmptyDialogWidget();
      },
    );
  }
}

// 弹窗显示组件
class KoiEmptyDialogWidget extends ConsumerWidget {
  const KoiEmptyDialogWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: 296.w,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.topCenter,
            children: [
              Image.network(
                "${Constant.msmdsAliCdn}/appNormal/Koiopenbg3.png",
                width: 295.w,
                height: 379.h,
              ),
              Column(
                children: [
                  Image.network(
                    "${Constant.msmdsAliCdn}/appNormal/Koinone.png",
                    width: 199.w,
                    height: 177.h,
                  ),
                  Text(
                    "今日开红包次数已用完\n您可以玩玩其他小游戏继续赚积分",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14.sp,
                      height: 1.5,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFFE44324),
                    ),
                  ),
                ],
              ),
              Positioned(
                bottom: 25.h,
                child: InkWell(
                  onTap: () {
                    SmartDialog.dismiss(tag: "koi_red_empty_dialog");
                  },
                  child: Image.network(
                    "${Constant.msmdsAliCdn}/koiok.png",
                    width: 165.w,
                    height: 48.h,
                  ),
                ),
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 12.h, right: 12.w),
                child: InkWell(
                  onTap: () {
                    SmartDialog.dismiss(tag: "koi_red_empty_dialog");
                  },
                  child: Image.asset(
                    closeBlack,
                    width: 14.w,
                    height: 14.h,
                    color: Colors.white,
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
