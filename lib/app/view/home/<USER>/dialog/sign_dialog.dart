import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../../navigation/coosea.dart';
import '../../../../repository/modals/sign/sign_item.dart';
import '../../../../repository/modals/sign/sign_record.dart';
import '../widgets/sign_dialog_animation.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.dialog
/// @ClassName: sign_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/12 11:18
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/12 11:18
/// @UpdateRemark: 更新说明
class SignDialog {
  static void show(SignRecord? signRecord) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "sign_dialog",
      animationTime: const Duration(milliseconds: 300),
      animationBuilder: (
        AnimationController controller,
        Widget child,
        AnimationParam animationParam,
      ) {
        return CustomDialogAnimation(
            animationParam: animationParam, child: child);
      },
      builder: (context) {
        return SignDialogWidget(
          signRecord: signRecord,
        );
      },
    );
  }
}

// 弹窗显示组件
class SignDialogWidget extends ConsumerStatefulWidget {
  const SignDialogWidget({
    super.key,
    required this.signRecord,
  });

  final SignRecord? signRecord;

  @override
  SignDialogWidgetState createState() => SignDialogWidgetState();
}

class SignDialogWidgetState extends ConsumerState<SignDialogWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var signCount = widget.signRecord?.signModel?.signCount ?? 1;
    var signList = widget.signRecord?.signList ?? [];
    var showIntegral = "";
    if (signList.isNotEmpty) {
      showIntegral = "${signList[signCount - 1].integralNum}";
    }
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        RotationTransition(
          turns: _controller,
          child: Image.network(
            "${Constant.msmdsAliCdn}/signOpti/opti_sign_success_gh.png",
            width: 139.w,
            height: 140.h,
          ),
        ),
        Container(
          width: 295.w,
          margin: EdgeInsets.only(top: 60.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.topCenter,
            children: [
              Positioned(
                top: -30.h,
                child: Image.network(
                  "${Constant.msmdsAliCdn}/signOpti/opti_sign_integarl_header.png",
                  width: 60.w,
                  height: 60.h,
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 43.h),
                    child: Text.rich(
                      TextSpan(
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF333333),
                        ),
                        children: [
                          const TextSpan(text: "签到成功"),
                          const TextSpan(
                            text: " +",
                            style: TextStyle(
                              color: Color(0xFFF93324),
                            ),
                          ),
                          TextSpan(
                            text: showIntegral,
                            style: TextStyle(
                              color: const Color(0xFFF93324),
                              fontSize: 20.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 19.h),
                  // 虚线
                  Row(
                    children: const [
                      Expanded(
                        child: DashedLine(
                          direction: Axis.horizontal,
                          dashWidth: 3.0,
                          dashHeight: 0.5,
                          dashSpace: 2.0,
                          color: Color(0xFFCCCCCC),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 17.h),
                  Text(
                    "连续签到大奖不断",
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  // 签到列表
                  SignListWidget(signCount: signCount, signList: signList),
                  SizedBox(height: 12.h),
                  GradientButton(
                    onPress: () {
                      SmartDialog.dismiss(tag: "sign_dialog");
                      navigatorKey.currentState
                          ?.pushNamed(CsRouter.myIntegralPage);
                    },
                    radius: 19.r,
                    shadow: false,
                    margin: EdgeInsets.symmetric(horizontal: 45.w),
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFFFB2D1A),
                        Color(0xFFFB2D1A),
                      ],
                    ),
                    child: Text(
                      "去兑换",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),
                ],
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: InkWell(
                  onTap: () {
                    SmartDialog.dismiss(tag: "sign_dialog");
                  },
                  child: Image.asset(
                    closeBlack,
                    width: 14.w,
                    height: 14.h,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// 签到列表组件
class SignListWidget extends ConsumerStatefulWidget {
  const SignListWidget({
    super.key,
    required this.signCount,
    required this.signList,
  });

  final int signCount;
  final List<SignItem>? signList;

  @override
  SignListWidgetState createState() => SignListWidgetState();
}

class SignListWidgetState extends ConsumerState<SignListWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      scrollToInit();
    });
  }

  void scrollToInit() {
    if (_scrollController.hasClients) {
      _scrollController.jumpTo(50.w * (widget.signCount - 1));
    }
  }

  Widget _buildItem(SignItem signItem) {
    var index = signItem.dayNum ?? 0;
    var dayColor = widget.signCount == signItem.dayNum
        ? const Color(0xFFFFFFFF)
        : widget.signCount > index
            ? const Color(0xFF6B2323)
            : const Color(0xFF999999);

    String bgImage = "${Constant.msmdsAliCdn}/appNormal/sign_are_bg.png";
    if (widget.signCount >= index) {
      bgImage = "${Constant.msmdsAliCdn}/appNormal/sign_are_bg.png";
    } else {
      bgImage = "${Constant.msmdsAliCdn}/appNormal/sign_unsign_bg.png";
    }

    String showText = "${signItem.day}";
    if (widget.signCount > index || widget.signCount == index) {
      showText = "${signItem.integralNum}积分";
    }

    return Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Image.network(
              bgImage,
              width: 36.w,
              height: 36.h,
            ),
            Text(
              widget.signCount >= index ? "" : "${signItem.integralNum}",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFFFEF9BA),
              ),
            ),
          ],
        ),
        SizedBox(height: 6.h),
        Stack(
          children: [
            if (widget.signCount == index && index != 1)
              Container(
                width: 25.w,
                height: 16.h,
                decoration: const BoxDecoration(
                  color: Color(0xFFFFD5D5),
                ),
              ),
            Container(
              width: 50.w,
              height: 16.h,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: widget.signCount == index
                    ? const Color(0xFFFE0A0A)
                    : widget.signCount > index
                        ? const Color(0xFFFFD5D5)
                        : const Color(0x00FFD5D5),
                borderRadius: widget.signCount == index
                    ? BorderRadius.circular(8.h)
                    : BorderRadius.only(
                        topLeft: Radius.circular(index == 1 ? 8.h : 0),
                        bottomLeft: Radius.circular(index == 1 ? 8.h : 0),
                      ),
              ),
              child: Text(
                showText,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: dayColor,
                ),
              ),
            ),
          ],
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.signList == null ||
        (widget.signList != null && widget.signList!.isEmpty)) {
      return const SizedBox();
    }
    return Container(
      height: 86.h,
      padding: EdgeInsets.symmetric(vertical: 14.h),
      color: const Color(0xFFF5F5F5),
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        child: Row(
          children: widget.signList!.map((e) => _buildItem(e)).toList(),
        ),
      ),
    );
  }
}

// 虚线
class DashedLine extends StatelessWidget {
  final Axis direction;
  final double dashWidth;
  final double dashHeight;
  final double dashSpace;
  final Color color;

  const DashedLine({
    Key? key,
    this.direction = Axis.horizontal,
    this.dashWidth = 5.0,
    this.dashHeight = 1.0,
    this.dashSpace = 3.0,
    this.color = Colors.black,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: DashedLinePainter(
        direction: direction,
        dashWidth: dashWidth,
        dashHeight: dashHeight,
        dashSpace: dashSpace,
        color: color,
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Axis direction;
  final double dashWidth;
  final double dashHeight;
  final double dashSpace;
  final Color color;

  DashedLinePainter({
    required this.direction,
    required this.dashWidth,
    required this.dashHeight,
    required this.dashSpace,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = dashHeight;

    double startX = 0;
    double startY = 0;

    if (direction == Axis.horizontal) {
      while (startX < size.width) {
        canvas.drawLine(
          Offset(startX, 0),
          Offset(startX + dashWidth, 0),
          paint,
        );
        startX += dashWidth + dashSpace;
      }
    } else {
      while (startY < size.height) {
        canvas.drawLine(
          Offset(0, startY),
          Offset(0, startY + dashHeight),
          paint,
        );
        startY += dashHeight + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
