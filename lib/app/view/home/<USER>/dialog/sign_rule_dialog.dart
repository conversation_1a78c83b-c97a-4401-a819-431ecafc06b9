import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../../../../common/img/icon_addres.dart';
import '../widgets/sign_dialog_animation.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.dialog
/// @ClassName: sign_rule_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/13 17:27
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/13 17:27
/// @UpdateRemark: 更新说明
class SignRuleDialog {
  static void show() {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "sign_rule_dialog",
      animationTime: const Duration(milliseconds: 300),
      animationBuilder: (
        AnimationController controller,
        Widget child,
        AnimationParam animationParam,
      ) {
        return CustomDialogAnimation(
          animationParam: animationParam,
          dy: 0,
          child: child,
        );
      },
      builder: (context) {
        return const SignDialogWidget();
      },
    );
  }
}

// 弹窗显示组件
class SignDialogWidget extends ConsumerWidget {
  const SignDialogWidget({
    super.key,
  });

  Widget _titleText(String text) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          text,
          style: TextStyle(
            fontSize: 20.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _text(String text) {
    return Text(
      text,
      style: TextStyle(
        fontSize: 12.sp,
        height: 1.6,
        color: const Color(0xFF333333),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 295.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 12.h, right: 12.w),
                child: InkWell(
                  onTap: () {
                    SmartDialog.dismiss(tag: "sign_rule_dialog");
                  },
                  child: Image.asset(
                    closeBlack,
                    width: 14.w,
                    height: 14.h,
                    color: Colors.grey,
                  ),
                ),
              )
            ],
          ),
          SizedBox(
            height: 321.h,
            child: SingleChildScrollView(
              padding: EdgeInsets.fromLTRB(18.w, 0, 18.w, 15.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 10.h),
                  _titleText("【活动规则】"),
                  SizedBox(height: 9.h),
                  _text(
                    "1.用户每日签到即可参与做任务领红包活动，用户需完成当日实际的每日任务后，即可随机开启当日的红包奖励，红包当日到账即可使用。",
                  ),
                  _text(
                    "2.新用户可进行连续签到领实物奖励（具体连签规则以实际的奖励为准）；若未能连续签到，则下次进入活动则从第一天开始重新签到。",
                  ),
                  _text(
                    "3.当日获得的红包展示在活动页中部进行领取，可在“我的-我的红包”处查看当日获取的红包的发放记录。",
                  ),
                  SizedBox(height: 10.h),
                  _titleText("【活动奖品】"),
                  SizedBox(height: 9.h),
                  _text(
                    "1.返利满返红包：每笔订单返利金额满足一定门槛即可激活红包（红包有效期为7日）",
                  ),
                  _text(
                    "2.实物奖励具体以实际为准",
                  ),
                  SizedBox(height: 10.h),
                  _titleText("【注意事项】"),
                  SizedBox(height: 9.h),
                  _text(
                    "1.活动过程中，用户不得使用任何外挂、插件以及其它破坏活动规则、违背活动公平原则的方式参与本次活动（批量注册、恶意刷助力、虚假分享、倒买倒卖等），不得进行任何作弊行为，否则我司有权取消用户参与活动资格，取消已经领取或者使用的福利，必要时取消后续参与任意活动的权利，并追究法律责任。",
                  ),
                  _text(
                    "2.由于用户自身设备或网络等原因导致参与活动不成功的，由用户自行负责。参与过程中，如用户出现违规行为（如利用黑客工具、软件作弊等），一经发现，不予发放奖品；对已发放奖品的，有权要求用户返还。",
                  ),
                  _text(
                    "3.活动方可根据活动举办的实际情况，在法律允许的范围内，对本活动说明进行变动或者调整，相关变动或者调整将公布在活动页面上。",
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
