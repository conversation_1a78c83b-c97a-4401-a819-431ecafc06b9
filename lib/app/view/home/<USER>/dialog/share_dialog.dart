import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/config/constant.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.home.sign.dialog
/// @ClassName: share_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/12 11:18
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/12 11:18
/// @UpdateRemark: 更新说明
class ShareDialog {
  static void show(String commissionText) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "share_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            InkWell(
              onTap: () {
                SmartDialog.dismiss(tag: "share_dialog");
              },
              child: Image.asset(
                cancel,
                width: 20.w,
                height: 20.w,
              ),
            ),
            SizedBox(height: 80.h),
            Container(
              width: 296.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 24.h),
                  Image.network(
                    "${Constant.msmdsAliCdn}/APPSHOW/copy_success_icon.png",
                    width: 60.w,
                    height: 60.w,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    "复制成功",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text.rich(
                    TextSpan(
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                      ),
                      children: [
                        const TextSpan(text: "发给好友下单，约赚 "),
                        TextSpan(
                          text: "¥$commissionText",
                          style: const TextStyle(
                            color: Color(0xFFFB311E),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 22.h),
                ],
              ),
            ),
            SizedBox(height: 40.h),
          ],
        );
      },
    );
  }
}
