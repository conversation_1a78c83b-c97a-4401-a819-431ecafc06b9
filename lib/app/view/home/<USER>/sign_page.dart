import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/sign/koi_red_provider.dart';
import 'package:msmds_platform/app/provider/sign/sign_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/earn_points.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/exchange.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/integrating_sphere.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/sign_koi_red_packet.dart';
import 'package:msmds_platform/app/view/home/<USER>/widgets/sign_title.dart';
import 'package:msmds_platform/common/widgets/back/back_widget.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: sign_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/10 14:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/10 14:06
/// @UpdateRemark: 更新说明
class SignPage extends ConsumerStatefulWidget {
  const SignPage({super.key});

  @override
  SignPageState createState() => SignPageState();
}

class SignPageState extends ConsumerState<SignPage> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 签到背景图
  Widget _buildBack() {
    return ref.watch(fetchSignBackImgProvider).when(
      data: (img) {
        if (img != null && img.backGroundUrl != null) {
          return BackWidget(
            scrollController: _scrollController,
            assets: img.backGroundUrl,
          );
        }
        return const SizedBox();
      },
      error: (o, s) {
        return const SizedBox();
      },
      loading: () {
        return const SizedBox();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    var signLayout = ref.watch(signLayoutConfigProvider);
    var userIntegral = ref.watch(userSignIntegralProvider);
    return Scaffold(
      body: AnnotatedRegion(
        value: const SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarContrastEnforced: false,
        ),
        child: Stack(
          children: [
            _buildBack(),
            RefreshIndicator(
              color: const Color(0xFFFF3F4E),
              onRefresh: () async {
                ref.read(signLayoutBallProvider.notifier).getSignLayoutBall();
                ref.read(signLayoutConfigProvider.notifier).getSignLayoutConfig();
                ref.read(fetchLuckGiftInfoProvider.notifier).getLuckGiftInfo();
              },
              child: CustomScrollView(
                physics: const BouncingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics(),
                ),
                controller: _scrollController,
                slivers: [
                  SliverToBoxAdapter(
                    child: SignTitle(userIntegral: userIntegral),
                  ),
                  SliverToBoxAdapter(
                    child: IntegratingSphere(signLayout: signLayout),
                  ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 14.h),
                      child: const EarnPoints(),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        Exchange(signLayout: signLayout),
                        // TaskReminder(),
                      ],
                    ),
                  ),
                  const SliverToBoxAdapter(
                    child: SignKoiRedPacket(),
                  ),
                  // SliverList(
                  //   delegate: SliverChildBuilderDelegate(
                  //     (context, index) => TaskContent(
                  //       title: "每日任务",
                  //       index: index,
                  //       length: 14,
                  //     ),
                  //     childCount: 14,
                  //   ),
                  // ),
                  // SliverList(
                  //   delegate: SliverChildBuilderDelegate(
                  //     (context, index) => TaskContent(
                  //       title: "简单任务",
                  //       index: index,
                  //       length: 4,
                  //     ),
                  //     childCount: 4,
                  //   ),
                  // ),
                  // SliverList(
                  //   delegate: SliverChildBuilderDelegate(
                  //     (context, index) => TaskContent(
                  //       title: "限时任务",
                  //       index: index,
                  //       length: 4,
                  //     ),
                  //     childCount: 4,
                  //   ),
                  // ),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 10.h),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
