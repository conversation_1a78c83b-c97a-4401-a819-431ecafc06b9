import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/config/splash_ad_provider.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

import '../../navigation/router.dart';

// Copyright (C), 2021-2025, <PERSON><PERSON>
// @ProjectName: msmds_platform
// @Package: app.view.splash
// @ClassName: splash_ad_page
// @Description: app 云配广告闪屏页
// @Author: frankylee
// @CreateDate: 2025/8/4 10:54
// @UpdateUser: frankylee
// @UpdateData: 2025/8/4 10:54
// @UpdateRemark: 更新说明
class SplashAdPage extends ConsumerStatefulWidget {
  const SplashAdPage({Key? key}) : super(key: key);

  @override
  SplashAdPageState createState() => SplashAdPageState();
}

class SplashAdPageState extends ConsumerState<SplashAdPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      var result =
          await ref.read(splashAdConfigProvider.notifier).fetchSplash(() {
        _pushReplacementHome();
      });
      if (!result && context.mounted) {
        _pushReplacementHome();
      }
    });
  }

  // 带参数或者不带参数跳转首页
  void _pushReplacementHome({String? adConfigData}) {
    if (adConfigData != null) {
      Navigator.pushReplacementNamed(
        context,
        CsRouter.home,
        arguments: {"adConfigData": adConfigData},
      );
    } else {
      Navigator.pushReplacementNamed(context, CsRouter.home);
    }
  }

  Widget _buildContent() {
    return Stack(
      alignment: Alignment.center,
      children: [
        _buildAdPic(),
        _buildSkipBtn(),
      ],
    );
  }

  Widget _buildAdPic() {
    var pic =
        ref.watch(splashAdConfigProvider.select((value) => value?.picUrl));
    var adData =
        ref.watch(splashAdConfigProvider.select((value) => value?.configData));
    if (pic == null) return const SizedBox();
    return InkWell(
      onTap: () {
        ref.read(splashAdCountdownProvider.notifier).adClick(() {
          _pushReplacementHome(adConfigData: adData);
        });
      },
      child: Image.network(
        pic,
        width: MediaQuery.of(context).size.width,
        fit: BoxFit.fitWidth,
      ),
    );
  }

  Widget _buildSkipBtn() {
    var buttonPosition = ref
        .watch(splashAdConfigProvider.select((value) => value?.buttonPosition));
    var countdown = ref.watch(splashAdCountdownProvider);
    if (countdown == null) return const SizedBox();
    var top = MediaQuery.of(context).padding.top + 16;
    Widget child = InkWell(
      onTap: () {
        ref.read(splashAdCountdownProvider.notifier).cleanCountdown(() {
          _pushReplacementHome();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0x60000000),
          borderRadius: BorderRadius.circular(12),
        ),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: "${countdown}s",
                style: const TextStyle(
                  fontSize: 11,
                  color: Color(0xFFF93324),
                ),
              ),
              const TextSpan(
                text: "｜ 跳过",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
        ),
      ),
    );

    if (buttonPosition == 1) {
      return Positioned(
        top: top,
        left: 20,
        child: child,
      );
    }

    if (buttonPosition == 2) {
      return Positioned(
        top: top,
        right: 20,
        child: child,
      );
    }

    if (buttonPosition == 3) {
      return Positioned(
        bottom: 26,
        left: 20,
        child: child,
      );
    }

    if (buttonPosition == 4) {
      return Positioned(
        bottom: 26,
        right: 20,
        child: child,
      );
    }

    return Positioned(
      top: top,
      right: 20,
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: _buildContent(),
          ),
          Container(
            padding: EdgeInsets.only(
              top: 10,
              bottom: MediaQuery.of(context).padding.bottom + 15,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(splashBottomLogo, height: 40),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
