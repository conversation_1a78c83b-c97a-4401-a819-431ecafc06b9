import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/sign/sign_provider.dart';
import 'package:msmds_platform/app/repository/modals/sign/exchange_item.dart';

import '../../../../common/img/icon_addres.dart';
import '../../../../config/constant.dart';
import '../../../../widgets/button/gradient_button.dart';
import '../../home/<USER>/widgets/sign_dialog_animation.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.integral.dialog
/// @ClassName: exchange_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 14:19
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 14:19
/// @UpdateRemark: 更新说明
class ExchangeDialog {
  static void show(ExchangeItem? exchangeItem) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "exchange_dialog",
      animationTime: const Duration(milliseconds: 300),
      animationBuilder: (
        AnimationController controller,
        Widget child,
        AnimationParam animationParam,
      ) {
        return CustomDialogAnimation(
            animationParam: animationParam, child: child);
      },
      builder: (context) {
        return ExchangeDialogWidget(
          exchangeItem: exchangeItem,
        );
      },
    );
  }
}

// 弹窗显示组件
class ExchangeDialogWidget extends ConsumerStatefulWidget {
  const ExchangeDialogWidget({
    super.key,
    required this.exchangeItem,
  });

  final ExchangeItem? exchangeItem;

  @override
  ExchangeDialogWidgetState createState() => ExchangeDialogWidgetState();
}

class ExchangeDialogWidgetState extends ConsumerState<ExchangeDialogWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        RotationTransition(
          turns: _controller,
          child: Image.network(
            "${Constant.msmdsAliCdn}/signOpti/opti_sign_success_gh.png",
            width: 139.w,
            height: 140.h,
          ),
        ),
        Container(
          width: 295.w,
          margin: EdgeInsets.only(top: 60.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.topCenter,
            children: [
              Positioned(
                top: -30.h,
                child: Image.network(
                  "${Constant.msmdsAliCdn}/signOpti/opti_sign_integarl_header.png",
                  width: 60.w,
                  height: 60.h,
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 70.h),
                  Text(
                    "确认兑换此奖品",
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 20.h),
                  GradientButton(
                    onPress: () async {
                      bool success = await ref
                          .read(integralExchangeProvider.notifier)
                          .exchangeGoods(widget.exchangeItem);
                      if (success) {
                        SmartDialog.dismiss(tag: "exchange_dialog");
                      }
                    },
                    radius: 19.r,
                    shadow: false,
                    margin: EdgeInsets.symmetric(horizontal: 45.w),
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFFFF8F20),
                        Color(0xFFFFC500),
                      ],
                    ),
                    child: Text(
                      "确认",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),
                ],
              ),
              Positioned(
                top: 10.h,
                right: 10.w,
                child: InkWell(
                  onTap: () {
                    SmartDialog.dismiss(tag: "exchange_dialog");
                  },
                  child: Image.asset(
                    closeBlack,
                    width: 14.w,
                    height: 14.h,
                    color: Colors.grey,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
