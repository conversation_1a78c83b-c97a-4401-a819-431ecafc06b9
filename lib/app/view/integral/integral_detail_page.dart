import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/sign/sign_provider.dart';
import 'package:msmds_platform/app/repository/modals/sign/integral_detail_item.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../../common/widgets/delegate/persistent_builder.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../../widgets/tabbar/rect_tab_indicator.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.integral
/// @ClassName: integral_detail_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 10:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 10:23
/// @UpdateRemark: 更新说明
class IntegralDetailPage extends ConsumerWidget {
  const IntegralDetailPage({super.key});

  // 积分显示
  Widget _buildIntegral(BuildContext context, WidgetRef ref) {
    var integral = ref.watch(userSignIntegralProvider);
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 10.w, right: 10.w),
      height: 102.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "${integral?.data ?? 0}",
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF202123),
            ),
          ),
          Text(
            "可用积分",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF7C7C7C),
            ),
          ),
        ],
      ),
    );
  }

  Widget _renderItem(IntegralDetailItem item) {
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
      padding: EdgeInsets.fromLTRB(20.w, 10.h, 20.w, 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${item.modifyReason}",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF191919),
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                "${item.updateTime ?? item.createTime}",
                style: TextStyle(
                  fontSize: 10.sp,
                  color: const Color(0xFF999999),
                ),
              ),
            ],
          ),
          Text(
            "${item.addOrSubtract == '增加' ? "+" : "-"}${item.modifyNo}",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFFFE6045),
            ),
          ),
        ],
      ),
    );
  }

  Widget _emptyWidget() {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        "暂无数据",
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 44.h,
        title: Text(
          '积分明细',
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        leading: const Leading(),
      ),
      body: CustomListView(
        onLoadMore: () async {
          ref.read(integralDetailListProvider.notifier).loadMore();
        },
        sliverHeader: [
          SliverToBoxAdapter(
            child: _buildIntegral(context, ref),
          ),
          SliverPersistentHeader(
            pinned: true,
            delegate: PersistentBuilder(
              max: 50.h,
              min: 50.h,
              builder: (_, offset) {
                return const ExchangeTab();
              },
            ),
          ),
          SliverToBoxAdapter(
            child: SizedBox(height: 10.h),
          ),
        ],
        data: ref.watch(
          integralDetailListProvider
              .select((value) => value.integralDetailList),
        ),
        footerState: ref.watch(
          integralDetailListProvider.select((value) => value.loadState),
        ),
        footerStateText: "只保留30天记录哦",
        renderItem: (context, index, o) {
          return _renderItem(o);
        },
        empty: _emptyWidget(),
      ),
    );
  }
}

class ExchangeType {
  String title;
  int exchangeType;

  ExchangeType(this.title, this.exchangeType);
}

class ExchangeTab extends ConsumerWidget {
  const ExchangeTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var data = [ExchangeType("领取记录", 1), ExchangeType("使用记录", 2)];
    return DefaultTabController(
      length: data.length,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        color: const Color(0xFFF9F9F9),
        height: 50.h,
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          // indicatorColor: const Color(0xFFF93324),
          indicator: RectTabIndicator(
            indicatorSize: 2.h,
            offset: 5,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF999999),
          unselectedLabelStyle: TextStyle(
            fontSize: 14.sp,
          ),
          labelStyle: TextStyle(
            fontSize: 14.sp,
          ),
          labelColor: const Color(0xFFF93324),
          tabs: data.map((e) => Tab(text: e.title)).toList(),
          onTap: (index) {
            ref
                .read(integralDetailListProvider.notifier)
                .changeTab(data[index].exchangeType);
          },
        ),
      ),
    );
  }
}
