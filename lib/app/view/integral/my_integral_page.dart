import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/sign/sign_provider.dart';
import 'package:msmds_platform/app/repository/modals/sign/exchange_item.dart';
import 'package:msmds_platform/app/view/integral/dialog/exchange_dialog.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

import '../../../common/widgets/appbar/leading.dart';
import '../../../common/widgets/delegate/persistent_builder.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../../widgets/tabbar/rect_tab_indicator.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.integral
/// @ClassName: my_integral_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 10:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 10:23
/// @UpdateRemark: 更新说明
class MyIntegralPage extends ConsumerWidget {
  const MyIntegralPage({super.key});

  // 积分显示
  Widget _buildIntegral(BuildContext context, WidgetRef ref) {
    var integral = ref.watch(userSignIntegralProvider);
    return Container(
      margin: EdgeInsets.only(top: 10.h, left: 10.w, right: 10.w, bottom: 10.h),
      height: 102.h,
      decoration: const BoxDecoration(
        image: DecorationImage(image: AssetImage(jfBc)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 10.h, left: 12.w),
            child: Text(
              "可用积分",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF494949),
              ),
            ),
          ),
          SizedBox(height: 10.h),
          Row(
            children: [
              SizedBox(width: 12.w),
              Text(
                "${integral?.data ?? 0}",
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF333333),
                ),
              ),
              SizedBox(width: 16.w),
              GradientButton(
                onPress: () {
                  Navigator.pop(context);
                },
                radius: 14.r,
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFFFEFA),
                    Color(0xFFFFE9CC),
                  ],
                ),
                child: Text(
                  "去赚积分>>",
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: const Color(0xFF604320),
                  ),
                ),
              ),
            ],
          ),
          const Expanded(child: SizedBox()),
          Container(
            height: 30.h,
            decoration: const BoxDecoration(
              color: Color(0xFFFFEACE),
            ),
            child: Row(
              children: [
                SizedBox(width: 5.w),
                InkWell(
                  onTap: () {
                    Navigator.pushNamed(context, CsRouter.integralDetailPage);
                  },
                  child: Row(
                    children: [
                      Image.asset(
                        jfBcLeft,
                        width: 13.w,
                        height: 13.w,
                      ),
                      Text(
                        "积分明细",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: const Color(0xFF744521),
                        ),
                      ),
                    ],
                  ),
                ),
                // SizedBox(width: 3.w),
                // Text(
                //   "|",
                //   style: TextStyle(
                //     fontSize: 10.sp,
                //     color: const Color(0xFF744521),
                //   ),
                // ),
                // SizedBox(width: 3.w),
                // InkWell(
                //   onTap: () {},
                //   child: Row(
                //     children: [
                //       Image.asset(
                //         jfBcRight,
                //         width: 13.w,
                //         height: 13.w,
                //       ),
                //       Text(
                //         "兑换记录",
                //         style: TextStyle(
                //           fontSize: 10.sp,
                //           color: const Color(0xFF744521),
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _renderItem(ExchangeItem item) {
    bool enable = false;
    var stock = item.stock ?? item.goodsStock;
    if (stock != null) {
      enable = stock >= 1;
    }
    return Container(
      margin: EdgeInsets.fromLTRB(10.w, 0, 10.w, 10.h),
      child: InkWell(
        borderRadius: BorderRadius.circular(10.r),
        onTap: () {
          if (enable) {
            ExchangeDialog.show(item);
          }
        },
        child: Ink(
          padding: EdgeInsets.fromLTRB(7.w, 10.h, 7.w, 10.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Row(
            children: [
              Image.network(
                "${item.pic ?? item.goodsImg}",
                width: 78.w,
                height: 78.w,
              ),
              SizedBox(width: 20.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "${item.name ?? item.goodsName}",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  RichText(
                    text: TextSpan(
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.red,
                      ),
                      children: [
                        TextSpan(
                          text: "${item.integral ?? item.needIntegral} ",
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const TextSpan(text: "积分"),
                      ],
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    "今日库存：$stock",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ],
              ),
              const Expanded(child: SizedBox()),
              GradientButton(
                onPress: () {
                  if (enable) {
                    ExchangeDialog.show(item);
                  }
                },
                radius: 16.r,
                enable: enable,
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
                gradient: LinearGradient(
                  colors: [
                    enable ? const Color(0xFFFB2D1A) : Colors.white,
                    enable ? const Color(0xFFFB2D1A) : Colors.white,
                  ],
                ),
                child: Text(
                  enable ? "立即兑换" : "已被抢完",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: enable ? Colors.white : const Color(0xFFFB2D1A),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _emptyWidget() {
    return Container(
      margin: EdgeInsets.only(top: 80.h),
      alignment: Alignment.center,
      child: Text(
        "暂无数据",
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 44.h,
        title: Text(
          '我的积分',
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.w700,
          ),
        ),
        centerTitle: true,
        leading: const Leading(),
      ),
      body: CustomListView(
        sliverHeader: [
          SliverToBoxAdapter(
            child: _buildIntegral(context, ref),
          ),
          SliverPersistentHeader(
            pinned: true,
            delegate: PersistentBuilder(
              max: 50.h,
              min: 50.h,
              builder: (_, offset) {
                return const ExchangeTab();
              },
            ),
          ),
          SliverToBoxAdapter(
            child: SizedBox(height: 10.h),
          ),
        ],
        data: ref.watch(integralExchangeProvider),
        footerState: LoadState.noMore,
        renderItem: (context, index, o) {
          return _renderItem(o);
        },
        empty: _emptyWidget(),
      ),
    );
  }
}

class ExchangeType {
  String title;
  int exchangeType;

  ExchangeType(this.title, this.exchangeType);
}

class ExchangeTab extends ConsumerWidget {
  const ExchangeTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var data = [ExchangeType("红包兑换", 2), ExchangeType("会员兑换", 1)];
    return DefaultTabController(
      length: data.length,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        color: const Color(0xFFF9F9F9),
        height: 50.h,
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          // indicatorColor: const Color(0xFFF93324),
          indicator: RectTabIndicator(
            indicatorSize: 2.h,
            offset: 5,
            gradient: const LinearGradient(
              colors: [
                Colors.white,
                Color(0xFFFF0E38),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          unselectedLabelColor: const Color(0xFF999999),
          unselectedLabelStyle: TextStyle(
            fontSize: 14.sp,
          ),
          labelStyle: TextStyle(
            fontSize: 14.sp,
          ),
          labelColor: const Color(0xFFF93324),
          tabs: data.map((e) => Tab(text: e.title)).toList(),
          onTap: (index) {
            ref
                .read(integralExchangeProvider.notifier)
                .changeExchangeTab(data[index].exchangeType);
          },
        ),
      ),
    );
  }
}
