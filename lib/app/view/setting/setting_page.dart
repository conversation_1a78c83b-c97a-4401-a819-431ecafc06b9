import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/setting/widgets/setting_access_widget.dart';
import 'package:msmds_platform/app/view/setting/widgets/setting_account_widget.dart';
import 'package:msmds_platform/app/view/setting/widgets/setting_other_widget.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: setting_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 12:05
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 12:05
/// @UpdateRemark: 更新说明
class SettingPage extends StatelessWidget {
  const SettingPage({super.key});

  /// 账户信息
  Widget _buildAccountSetting() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // const AvatarWidget(),
          Text(
            "账号设置",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
          // const NickNameWidget(),
          // const BindPhoneWidget(),
          const AlipayWidget(),
          const VersionWidget(),
        ],
      ),
    );
  }

  /// 账户权限
  Widget _buildAccessSetting() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(padding: EdgeInsets.only(top: 14.h)),
          Text(
            "账号权限",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
          // const PermissionWidget(),
          // const RecommendWidget(),
          // const NotifyWidget(),
          const CleanCacheWidget(),
        ],
      ),
    );
  }

  /// 其他设置
  Widget _buildOtherSetting(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h, bottom: 20.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(padding: EdgeInsets.only(top: 14.h)),
          Text(
            "其他",
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
          const ServiceWidget(),
          const AgreementWidget(),
          const SummaryWidget(),
          const ChecklistWidget(),
          const SharedListWidget(),
          const ContactWidget(),
          // const LogoffWidget(),
          const LogoutWidget(),
          const CsProxyWidget(),
          const CsServerWidget(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        backgroundColor: Colors.white,
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: Column(
          children: [
            _buildAccountSetting(),
            _buildAccessSetting(),
            _buildOtherSetting(context),
          ],
        ),
      ),
    );
  }
}
