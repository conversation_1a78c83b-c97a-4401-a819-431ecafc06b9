import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: logoff_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/8 14:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/8 14:46
/// @UpdateRemark: 更新说明

/// 条款
final List<String> terms = [
  "prefix请注意，注销后你将放弃以下权益",
  "1.账号信息、会员权益等将被清空且无法恢复；",
  "2.已获得的所有相关返现、积分和红包等将被清空且无法恢复；",
  "3.已绑定的下线将被清空且无法恢复；",
  "4.已完成的订单信息将被清空且无法恢复；",
  "5.您将无法使用已注销的手机号参与返利快车与外部合作的一切优惠活动；外部合作已获得奖品等将被清空且无法恢复；",
  "6.您将无法使用已注销的手机号重新注册返利快车；",
  "7.您将无法使用已注销的支付宝账号重新绑定返利快车账号；",
  "8.您将无法使用已注销的淘宝账号重新绑定返利快车账号；",
  "9.注销账号即表示您已充分理解并同意以上所有条款。",
];

class LogoffPage extends StatelessWidget {
  const LogoffPage({super.key});

  Widget _buildTermItem(String value) {
    bool first = value.contains("prefix");
    double fontSize = first ? 15.sp : 12.sp;
    var fontWeight = first ? FontWeight.w600 : FontWeight.normal;
    var color = first ? const Color(0xFFF93324) : const Color(0xFFA7A7A7);
    return Padding(
      padding: EdgeInsets.only(bottom: 13.h),
      child: Text(
        first ? value.replaceAll("prefix", "") : value,
        style: TextStyle(
          height: 1.5,
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white.withAlpha(0),
        elevation: 0,
        leading: const Leading(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(10.w, 10.h, 0, 10.h),
              child: Text(
                "账号注销",
                style: TextStyle(
                  fontSize: 20.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Container(
                padding: EdgeInsets.fromLTRB(12.w, 14.h, 0.w, 11.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: terms.map((e) => _buildTermItem(e)).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        height: 46.h,
        margin: EdgeInsets.fromLTRB(48.w, 20.h, 48.w, 27.h),
        child: Consumer(
          builder: (context, ref, child) {
            return GradientButton(
              onPress: () {
                ref.read(cancelAccountProvider.notifier).cancel();
              },
              radius: 23.h,
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFFE5640),
                  Color(0xFFFA2E1B),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              child: Text(
                "申请注销账户",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
