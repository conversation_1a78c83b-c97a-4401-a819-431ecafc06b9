import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/service_dialog.dart';
import 'package:msmds_platform/app/view/setting/dialog/confirm_logout_dialog.dart';
import 'package:msmds_platform/app/view/setting/widgets/setting_item_widget.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/utils/toast_util.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: access_info_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 17:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 17:45
/// @UpdateRemark: 更新说明

/// 用户协议
class ServiceWidget extends ConsumerWidget {
  const ServiceWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "用户协议",
      value: "",
      onPress: () {
        Navigator.pushNamed(
          context,
          CsRouter.webPage,
          arguments: ["", Constant.userServiceAgreement],
        );
      },
    );
  }
}

/// 用户隐私政策
class AgreementWidget extends ConsumerWidget {
  const AgreementWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "用户隐私政策",
      value: "",
      onPress: () {
        Navigator.pushNamed(
          context,
          CsRouter.webPage,
          arguments: ["", Constant.userAgreement],
        );
      },
    );
  }
}

/// 用户隐私政策摘要
class SummaryWidget extends ConsumerWidget {
  const SummaryWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "用户隐私政策摘要",
      value: "",
      onPress: () {
        Navigator.pushNamed(
          context,
          CsRouter.webPage,
          arguments: ["", Constant.userAgreementSummary],
        );
      },
    );
  }
}

/// 个人信息收集清单
class ChecklistWidget extends ConsumerWidget {
  const ChecklistWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "个人信息收集清单",
      value: "",
      onPress: () {
        Navigator.pushNamed(context, CsRouter.infoCollection);
      },
    );
  }
}

/// 第三方共享个人信息清单
class SharedListWidget extends ConsumerWidget {
  const SharedListWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "第三方共享个人信息清单",
      value: "",
      onPress: () {
        Navigator.pushNamed(
          context,
          CsRouter.webPage,
          arguments: ["", Constant.shareInfoList],
        );
      },
    );
  }
}

/// 联系我们
class ContactWidget extends ConsumerWidget {
  const ContactWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "联系我们",
      value: "",
      onPress: () {
        ServiceDialog.showServiceDialog();
      },
    );
  }
}

/// 注销账号
class LogoffWidget extends ConsumerWidget {
  const LogoffWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    if (userData == null) {
      return Container();
    }
    return ItemWidget(
      name: "注销账号",
      value: "",
      onPress: () {
        Navigator.pushNamed(context, CsRouter.logoff);
      },
    );
  }
}

/// 退出登录
class LogoutWidget extends ConsumerWidget {
  const LogoutWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    if (userData == null) {
      return Container();
    }
    return ItemWidget(
      name: "退出登录",
      value: "",
      onPress: () {
        ConfirmLogoutDialog.confirmLogoutDialog(() {
          ref.read(authProvider.notifier).logout();
          ToastUtil.showToast("退出登录成功");
        });
      },
    );
  }
}

/// 设置代理
class CsProxyWidget extends ConsumerWidget {
  const CsProxyWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var show = GlobalConfig.enableProxy;
    if (show) {
      return ItemWidget(
        name: "代理设置",
        value: "",
        onPress: () {
          Navigator.pushNamed(context, CsRouter.proxy);
        },
      );
    }
    return Container();
  }
}

/// 设置服务器
class CsServerWidget extends ConsumerWidget {
  const CsServerWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var show = GlobalConfig.enableProxy;
    if (show) {
      return ItemWidget(
        name: "服务器设置",
        value: "",
        onPress: () {
          Navigator.pushNamed(context, CsRouter.server);
        },
      );
    }
    return Container();
  }
}
