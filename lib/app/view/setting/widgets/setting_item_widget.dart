import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: item_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 17:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 17:46
/// @UpdateRemark: 更新说明
class ItemWidget extends StatelessWidget {
  const ItemWidget({
    super.key,
    required this.name,
    this.value,
    this.valueStyle,
    this.showArrow = true,
    this.onPress,
  });

  final String name;
  final String? value;
  final TextStyle? valueStyle;
  final bool? showArrow;
  final Function()? onPress;

  @override
  Widget build(BuildContext context) {
    InlineSpan valueWidget;
    if (value != null) {
      valueWidget = TextSpan(
        text: value!,
        style: valueStyle ??
            TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
      );
    } else {
      valueWidget = const TextSpan();
    }

    Widget arrow = Container();
    if (showArrow == true) {
      arrow = Icon(
        Icons.keyboard_arrow_right_rounded,
        size: 24.r,
        color: const Color(0xFF979797),
      );
    }

    return InkWell(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 14.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              name,
              style: TextStyle(fontSize: 14.sp, color: Colors.black),
            ),
            Text.rich(
              TextSpan(
                children: [
                  valueWidget,
                  WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: arrow,
                  ),
                ],
              ),
            ),
            // Row(
            //   children: [valueWidget, arrow],
            // ),
          ],
        ),
      ),
    );
  }
}
