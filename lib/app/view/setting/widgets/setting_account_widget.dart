import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/setting/setting_provider.dart';
import 'package:msmds_platform/app/provider/withdrawal/withdrawal_provider.dart';
import 'package:msmds_platform/app/view/setting/widgets/setting_item_widget.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: account_info_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 17:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 17:45
/// @UpdateRemark: 更新说明

/// 头像
class AvatarWidget extends ConsumerWidget {
  const AvatarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    if (userData == null) {
      return Container();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 16.h, bottom: 22.h),
          child: Container(
            width: 68.w,
            height: 68.w,
            color: Colors.grey.withAlpha(60),
          ),
        ),
      ],
    );
  }
}

/// 昵称
class NickNameWidget extends ConsumerWidget {
  const NickNameWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    if (userData == null) {
      return Container();
    }
    return ItemWidget(
      name: "昵称",
      value: "${userData.data?.nickName}",
      showArrow: false,
    );
  }
}

/// 手机号
// class BindPhoneWidget extends ConsumerWidget {
//   const BindPhoneWidget({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     var userData = ref.watch(fetchAccountDetailProvider).value;
//     if (userData == null) {
//       return Container();
//     }
//     if (userData.phone == null) {
//       return ItemWidget(
//         name: "绑定手机号",
//         value: "去绑定",
//         valueStyle: TextStyle(
//           fontSize: 14.sp,
//           color: const Color(0xFFFF0E38),
//         ),
//         showArrow: true,
//         onPress: () {
//           BindPhoneDialog.showBindPhoneDialog("绑定手机号");
//         },
//       );
//     }
//     return ItemWidget(
//       name: "绑定手机号",
//       value: "${userData.phone}",
//       showArrow: false,
//     );
//   }
// }

/// 支付宝
class AlipayWidget extends ConsumerWidget {
  const AlipayWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    if (userData == null) {
      return Container();
    }
    var alipayAccount = ref.watch(
      alipayAccountProvider.select((value) => value?.phone),
    );
    if (alipayAccount == null) {
      return Container();
    }
    return ItemWidget(
      name: "绑定支付宝",
      value: alipayAccount,
      showArrow: false,
    );
  }
}

/// 当前版本
class VersionWidget extends ConsumerWidget {
  const VersionWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(fetchVersionProvider).when(
      data: (version) {
        return ItemWidget(
          name: "当前版本",
          value: version,
          showArrow: false,
        );
      },
      error: (error, stackTrace) {
        return Container();
      },
      loading: () {
        return Container();
      },
    );
  }
}
