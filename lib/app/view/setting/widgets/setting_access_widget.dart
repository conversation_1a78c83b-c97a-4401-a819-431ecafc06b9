import 'package:app_settings/app_settings.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/setting/setting_provider.dart';
import 'package:msmds_platform/app/view/setting/widgets/setting_item_widget.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: access_info_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 17:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 17:45
/// @UpdateRemark: 更新说明

/// 权限设置
class PermissionWidget extends ConsumerWidget {
  const PermissionWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "权限设置",
      value: "",
      onPress: () {
        AppSettings.openAppSettings();
      },
    );
  }
}

/// 个性化推荐
class RecommendWidget extends ConsumerWidget {
  const RecommendWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "个性化消息推荐",
      value: ref.watch(settingRecommendProvider) ? "去开启" : "去关闭",
      onPress: () {
        ref.read(settingRecommendProvider.notifier).toggle();
      },
    );
  }
}

/// 消息推送
class NotifyWidget extends ConsumerWidget {
  const NotifyWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "消息推送",
      value: "未开启返现通知",
      showArrow: false,
      onPress: () {},
    );
  }
}

/// 清除缓存
class CleanCacheWidget extends ConsumerWidget {
  const CleanCacheWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ItemWidget(
      name: "清除缓存",
      value: ref.watch(settingCleanCacheProvider),
      onPress: () {
        ref.read(settingCleanCacheProvider.notifier).clear();
      },
    );
  }
}
