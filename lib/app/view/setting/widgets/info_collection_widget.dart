import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/setting/info_collection_provider.dart';
import 'package:msmds_platform/app/provider/setting/setting_provider.dart';
import 'package:msmds_platform/utils/router_util.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: info_collection_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/5 11:09
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/5 11:09
/// @UpdateRemark: 更新说明

/// 身份信息
class IdentityInfoWidget extends ConsumerWidget {
  const IdentityInfoWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "1、用户身份信息",
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          Text(
            "手机号",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用目的：",
            right: Text(
              "用户注册、用户认证",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用场景：",
            right: Text(
              "注册和认证、用户更换绑定手机号",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集情况：",
            right: Text(
              "收集1条",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集内容：",
            right: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Text(
                "点击查看",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFFFB2F25),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 用户使用记录
class UsingInfoWidget extends ConsumerWidget {
  const UsingInfoWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "2、用户使用记录",
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          Text(
            "订单记录",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用目的：",
            right: Text(
              "客服与售后服务争议处理",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用场景：",
            right: Text(
              "用户下单时",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集情况：",
            right: Text(
              "详情见订单页",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集内容：",
            right: InkWell(
              onTap: () {
                RouterUtil.checkLogin(context, call: () {
                  Navigator.pushNamed(context, CsRouter.myOrderPage);
                });
              },
              child: Text(
                "点击查看",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFFFB2F25),
                ),
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 20)),
          Text(
            "剪贴板记录",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用目的：",
            right: Text(
              "获取用户剪贴板内容，识别是否是商品标题或链接",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用场景：",
            right: Text(
              "自动识别商品标题或链接，触发商品查询任务",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集情况：",
            right: Text(
              "收集${ref.watch(
                collectionClipboardProvider.select((value) => value.num),
              )}条",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集内容：",
            right: Text(
              ref.watch(
                collectionClipboardProvider.select(
                  (value) => value.content ?? "-",
                ),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 设备信息
class AppInfoWidget extends ConsumerWidget {
  const AppInfoWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "3、设备信息",
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          Text(
            "APP版本号",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用目的：",
            right: Text(
              "用户兼容性判断和安全保障",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "使用场景：",
            right: Text(
              "使用APP过程中",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集情况：",
            right: Text(
              "收集${ref.watch(collectionVersionProvider)}条",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
          const Padding(padding: EdgeInsets.only(bottom: 10)),
          ItemWidget(
            name: "收集内容：",
            right: Text(
              ref.watch(fetchVersionProvider).value ?? "-",
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ItemWidget extends StatelessWidget {
  const ItemWidget({
    super.key,
    required this.right,
    required this.name,
  });

  final String name;
  final Widget right;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          name,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.black,
          ),
        ),
        Expanded(child: right),
      ],
    );
  }
}
