import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.setting
/// @ClassName: not_found_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/21 15:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/21 15:23
/// @UpdateRemark: 更新说明
class NotFountPage extends StatelessWidget {
  const NotFountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        backgroundColor: Colors.white,
        leading: const Leading(),
      ),
      body: Center(
        child: Text(
          "未找到该活动",
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ),
    );
  }
}
