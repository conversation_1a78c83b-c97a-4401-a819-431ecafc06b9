import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/view/setting/widgets/info_collection_widget.dart';

import '../../../common/widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: info_collection_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/5 10:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/5 10:49
/// @UpdateRemark: 更新说明
class InfoCollectionPage extends StatelessWidget {
  const InfoCollectionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 44.h,
        elevation: 0,
        backgroundColor: Colors.white,
        leading: const Leading(),
        centerTitle: true,
        title: Text(
          "个人信息收集清单",
          style: TextStyle(
            fontSize: 16.sp,
          ),
        ),
      ),
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Text(
                "为了向您提供相应的产品及服务，并保障系统安全稳定运行，我们可能会收集以下个人信息。当前页面只展示最新获取的信息，每次退出APP，当前页面记录的信息将被清除，下次进入时，则重新获取。",
                style: TextStyle(fontSize: 12.sp, color: Colors.black),
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: IdentityInfoWidget(),
          ),
          const SliverToBoxAdapter(
            child: UsingInfoWidget(),
          ),
          const SliverToBoxAdapter(
            child: AppInfoWidget(),
          )
        ],
      ),
    );
  }
}
