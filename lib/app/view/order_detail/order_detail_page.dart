import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/order/order_detail_provider.dart';
import 'package:msmds_platform/app/view/order_detail/widgets/order_detail_info.dart';
import 'package:msmds_platform/app/view/order_detail/widgets/order_header_info.dart';
import 'package:msmds_platform/app/view/order_detail/widgets/order_recommend_item.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';
import 'package:msmds_platform/common/widgets/appbar/leading.dart';
import 'package:msmds_platform/common/widgets/back/back_widget.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_detail_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/4 10:22
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/4 10:22
/// @UpdateRemark: 更新说明
class OrderDetailPage extends ConsumerStatefulWidget {
  const OrderDetailPage({super.key});

  @override
  OrderDetailPageState createState() => OrderDetailPageState();
}

class OrderDetailPageState extends ConsumerState<OrderDetailPage> {
  late ScrollController _scrollController;

  List<int> data = [];

  @override
  void initState() {
    _scrollController = ScrollController();
    super.initState();
  }

  /// app bar
  SliverAppBar _buildAppBar(String? title) {
    return SliverAppBar(
      backgroundColor: Colors.white.withAlpha(0),
      pinned: false,
      floating: true,
      toolbarHeight: 44.h,
      centerTitle: true,
      elevation: 0,
      title: Text(
        title ?? "",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.black,
          fontWeight: FontWeight.w600,
        ),
      ),
      leading: const Leading(),
    );
  }

  /// 推荐列表头部提示
  // Widget _buildRecommendTitle() {
  //   return Container(
  //     padding: EdgeInsets.symmetric(vertical: 14.h),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       children: [
  //         Container(
  //           width: 22.w,
  //           height: 2.h,
  //           margin: EdgeInsets.only(right: 10.w),
  //           decoration: const BoxDecoration(
  //             gradient: LinearGradient(
  //               colors: [
  //                 Color(0x00FB133C),
  //                 Color(0xFFEA3931),
  //               ],
  //             ),
  //           ),
  //         ),
  //         Text(
  //           "相关推荐",
  //           style: TextStyle(
  //             fontSize: 16.sp,
  //             fontWeight: FontWeight.w600,
  //             color: const Color(0xFF333333),
  //           ),
  //         ),
  //         Container(
  //           width: 22.w,
  //           height: 2.h,
  //           margin: EdgeInsets.only(left: 10.w),
  //           decoration: const BoxDecoration(
  //             gradient: LinearGradient(
  //               colors: [
  //                 Color(0xFFEA3931),
  //                 Color(0x00FB133C),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return ref.watch(fetchOrderDetailProvider).when(
      data: (orderDetail) {
        var firstDetail = orderDetail?.first;
        var title = firstDetail != null ? firstDetail.statusDesc : "订单详情";
        return Scaffold(
          body: Stack(
            children: [
              BackWidget(
                scrollController: _scrollController,
                assets: orderDetailBg,
              ),
              CustomListView(
                controller: _scrollController,
                onRefresh: () async {
                  ref.refresh(fetchOrderDetailProvider).unwrapPrevious();
                },
                data: data,
                sliverGridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: MediaQuery.of(context).size.width / 2,
                  mainAxisExtent: 298.h,
                  mainAxisSpacing: 10,
                  crossAxisSpacing: 7,
                ),
                sliverHeader: [
                  _buildAppBar(title),
                  SliverToBoxAdapter(
                    child: OrderHeaderInfo(
                      orderDetailList: orderDetail,
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: OrderDetailInfo(
                      orderDetail: firstDetail,
                    ),
                  ),
                  // SliverToBoxAdapter(
                  //   child: _buildRecommendTitle(),
                  // ),
                ],
                renderItem: (context, index, item) {
                  return const OrderRecommendItem();
                },
              ),
            ],
          ),
          // bottomNavigationBar: const OrderDetailBottom(),
        );
      },
      error: (o, s) {
        return Scaffold(
          body: Stack(
            children: [
              BackWidget(
                scrollController: _scrollController,
                assets: orderDetailBg,
              ),
              CustomListView(
                controller: _scrollController,
                onRefresh: () async {
                  ref.refresh(fetchOrderDetailProvider).unwrapPrevious();
                },
                data: data,
                sliverGridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: MediaQuery.of(context).size.width / 2,
                  mainAxisExtent: 298.h,
                  mainAxisSpacing: 10,
                  crossAxisSpacing: 7,
                ),
                sliverHeader: [
                  _buildAppBar(""),
                ],
                renderItem: (context, index, item) {
                  return const OrderRecommendItem();
                },
              ),
            ],
          ),
        );
      },
      loading: () {
        return Scaffold(
          body: Stack(
            children: [
              BackWidget(
                scrollController: _scrollController,
                assets: orderDetailBg,
              ),
              CustomListView(
                data: data,
                sliverGridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: MediaQuery.of(context).size.width / 2,
                  mainAxisExtent: 298.h,
                  mainAxisSpacing: 10,
                  crossAxisSpacing: 7,
                ),
                sliverHeader: [
                  _buildAppBar(""),
                ],
                renderItem: (context, index, item) {
                  return const OrderRecommendItem();
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
