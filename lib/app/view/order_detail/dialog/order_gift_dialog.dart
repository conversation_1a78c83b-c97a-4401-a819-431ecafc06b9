import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/order/order_detail_provider.dart';
import 'package:msmds_platform/config/constant.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';

import '../../../../common/img/icon_addres.dart';
import '../../../navigation/coosea.dart';
import '../../../repository/modals/order/order_gift_item.dart';

/// Copyright (C), 2021-2025, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.view.order_detail.dialog
/// @ClassName: order_gift_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2025/4/10 16:06
/// @UpdateUser: frankylee
/// @UpdateData: 2025/4/10 16:06
/// @UpdateRemark: 更新说明
class OrderGiftDialog {
  static void showGiftDialog(
    List<OrderGiftItem>? orderGiftList,
    int orderId,
  ) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "order_gift_dialog",
      builder: (context) {
        return SizedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.network(
                "${Constant.msmdsAliCdn}/APPSHOW/gift_order_title.png",
                width: 248.w,
                height: 25.h,
              ),
              SizedBox(
                height: 20.h,
              ),
              GiftWidget(
                orderGiftList: orderGiftList,
                orderId: orderId,
              ),
              Padding(padding: EdgeInsets.only(bottom: 20.h)),
              InkWell(
                onTap: () {
                  SmartDialog.dismiss(tag: "order_gift_dialog");
                },
                child: Image.network(
                  close,
                  width: 30.w,
                  height: 30.w,
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

class GiftWidget extends ConsumerWidget {
  const GiftWidget({
    super.key,
    required this.orderGiftList,
    required this.orderId,
  });

  final List<OrderGiftItem>? orderGiftList;
  final int orderId;

  // 红包使用提醒
  Widget _giftTip() {
    bool isNotEmpty = orderGiftList != null && orderGiftList!.isNotEmpty;
    if (isNotEmpty) {
      return Container(
        margin: EdgeInsets.only(top: 21.h, bottom: 13.h),
        child: Column(
          children: [
            Text(
              "红包激活后可以和返现",
              style: TextStyle(
                color: const Color(0xFFFFF6BD),
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              "一起提现到支付宝",
              style: TextStyle(
                color: const Color(0xFFFFF6BD),
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      );
    }
    return Container();
  }

  // 红包为空
  Widget _emptyWidget() {
    return Column(
      children: [
        SizedBox(
          height: 70.h,
        ),
        Image.network(
          "${Constant.msmdsAliCdn}/APPSHOW/gift_order_empty.png",
          width: 115.w,
          height: 120.h,
        ),
        Text(
          "暂无满足激活条件的红包",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  // 底部按钮
  Widget _bottomWidget(BuildContext context, WidgetRef ref) {
    bool isNotEmpty = orderGiftList != null && orderGiftList!.isNotEmpty;
    return InkWell(
      onTap: () async {
        if (isNotEmpty) {
          // 激活红包
          var result = await ref
              .read(orderGiftListProvider.notifier)
              .activeGift(orderId);
          if (result) {
            SmartDialog.dismiss(tag: "order_gift_dialog");
          }
        } else {
          SmartDialog.dismiss(tag: "order_gift_dialog");
          navigatorKey.currentState?.pushNamed(CsRouter.myIntegralPage);
        }
      },
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Image.network(
            "${Constant.msmdsAliCdn}/APPSHOW/gift_order_btn.png",
            width: 220.w,
            height: 79.h,
          ),
          Padding(
            padding: EdgeInsets.only(top: 17.h),
            child: Text(
              isNotEmpty ? "立即激活" : "去兑换红包",
              style: TextStyle(
                fontSize: 22.sp,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 红包item
  Widget _giftItem(OrderGiftItem item, WidgetRef ref) {
    var currentGift = ref.watch(orderGiftListProvider);
    String bg = "${Constant.msmdsAliCdn}/appNormal/giftcheck.png";
    if (item.giftId == currentGift?.giftId) {
      bg = "${Constant.msmdsAliCdn}/appNormal/giftChecked.png";
    }

    // 红包激活条件
    Widget giftLimit = Container();
    if (item.moneyLimit == 0) {
      giftLimit = Text(
        "下单即激活返利红包",
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF9C9C9C),
        ),
      );
    }
    if (item.moneyLimit != 0 && item.useType == 2) {
      giftLimit = Text(
        "每单返利满${(item.moneyLimit ?? 0) / 100}元可用",
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF9C9C9C),
        ),
      );
    }
    if (item.moneyLimit != 0 && item.useType == 1) {
      giftLimit = Text(
        "单次提现满${(item.moneyLimit ?? 0) / 100}元可用",
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF9C9C9C),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      child: InkWell(
        onTap: () {
          ref.read(orderGiftListProvider.notifier).selectGift(item);
        },
        child: Stack(
          children: [
            Image.network(
              bg,
              width: 257.w,
              height: 70.h,
            ),
            Container(
              height: 70.h,
              padding: EdgeInsets.only(left: 7.w, top: 10.h, bottom: 10.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${item.useType == 1 ? "提现红包" : item.typeDesc}",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF3A3A3A),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          giftLimit,
                          SizedBox(
                            height: 3.h,
                          ),
                          Text(
                            "失效时间：${item.endTime}",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: const Color(0xFF9C9C9C),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 7.w),
                    child: Column(
                      children: [
                        Text(
                          "¥${(item.money ?? 0) / 100}",
                          style: TextStyle(
                            fontSize: 20.sp,
                            color: const Color(0xFFF84634),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(
                          height: 4.h,
                        ),
                        GradientButton(
                          onPress: () {
                            ref
                                .read(orderGiftListProvider.notifier)
                                .selectGift(item);
                          },
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFE9702F),
                              Color(0xFFE5545D),
                            ],
                          ),
                          shadow: false,
                          radius: 20.r,
                          padding: EdgeInsets.symmetric(
                            vertical: 4.h,
                            horizontal: 6.w,
                          ),
                          child: Text(
                            "激活该红包",
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Image.network(
          "${Constant.msmdsAliCdn}/APPSHOW/gift_order_list_bg.png",
          width: 330.w,
          height: 390.h,
        ),
        SizedBox(
          width: 257.w,
          height: 390.h,
          child: Column(
            children: [
              _giftTip(),
              Expanded(
                child: CustomListView(
                  data: orderGiftList,
                  renderItem: (context, index, item) {
                    return _giftItem(item, ref);
                  },
                  empty: _emptyWidget(),
                ),
              ),
              SizedBox(
                height: 40.h,
              ),
              _bottomWidget(context, ref),
            ],
          ),
        ),
      ],
    );
  }
}
