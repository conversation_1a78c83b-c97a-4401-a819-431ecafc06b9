import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/provider/order/order_detail_provider.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';
import 'package:msmds_platform/widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_header_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/4 10:25
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/4 10:25
/// @UpdateRemark: 更新说明
class OrderHeaderInfo extends ConsumerWidget {
  const OrderHeaderInfo({
    super.key,
    required this.orderDetailList,
  });

  final List<OrderDetail>? orderDetailList;

  Widget _buildGoods(WidgetRef ref) {
    if (orderDetailList != null && orderDetailList!.isNotEmpty) {
      var orderFirst = orderDetailList!.first;
      return Column(
        children: orderDetailList!
            .map(
              (e) => Container(
                margin: const EdgeInsets.only(bottom: 10),
                child: Column(
                  children: [
                    Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(6),
                          child: Image.network(
                            "${e.shopImg}",
                            width: 80.w,
                            height: 80.w,
                            fit: BoxFit.cover,
                            errorBuilder: (_, o, s) {
                              return Container(
                                width: 80.w,
                                height: 80.w,
                                color: Colors.grey.withAlpha(60),
                              );
                            },
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(right: 8.w)),
                        Expanded(
                          child: Text(
                            "${e.shopName}",
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10.h,
                    ),
                    Divider(
                      color: Colors.grey.withAlpha(50),
                      height: 1,
                    ),
                    Padding(padding: EdgeInsets.only(top: 16.h)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "实付金额",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF333333),
                          ),
                        ),
                        Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.black,
                            ),
                            children: [
                              const TextSpan(text: "¥"),
                              TextSpan(
                                text: "${e.amount ?? "-"}",
                                style: TextStyle(
                                  fontSize: 16.sp,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    Padding(padding: EdgeInsets.only(top: 17.h)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "返现金额",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF333333),
                          ),
                        ),
                        Text.rich(
                          TextSpan(
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: const Color(0xFFFF0E38),
                            ),
                            children: [
                              const TextSpan(text: "¥"),
                              TextSpan(
                                text: "${e.realCommission ?? "-"}",
                                style: TextStyle(
                                  fontSize: 16.sp,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                    _giftInfo(orderFirst, ref),
                  ],
                ),
              ),
            )
            .toList(),
      );
    }
    return Container();
  }

  // 红包信息
  Widget _giftInfo(OrderDetail orderFirst, WidgetRef ref) {
    if (orderFirst.redPacketAmount != null && orderFirst.redPacketAmount! > 0) {
      return Column(
        children: [
          Padding(padding: EdgeInsets.only(top: 17.h)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "激活红包",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                ),
              ),
              Text.rich(
                TextSpan(
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF3A3A3A),
                  ),
                  children: [
                    const TextSpan(text: "¥"),
                    TextSpan(
                      text: "${orderFirst.redPacketAmount ?? "无"}",
                      style: TextStyle(
                        fontSize: 16.sp,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      );
    }

    if (orderFirst.canUseRedPacket == true) {
      return Column(
        children: [
          Padding(padding: EdgeInsets.only(top: 17.h)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "激活红包",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                ),
              ),
              GradientButton(
                onPress: () {
                  if (orderFirst.orderId != null) {
                    ref
                        .read(orderGiftListProvider.notifier)
                        .getOrderGiftList(orderFirst.orderId!);
                  }
                },
                gradient: const LinearGradient(
                  colors: [Color(0xFFEA3931), Color(0xFFEA3931)],
                ),
                radius: 12.r,
                shadow: false,
                padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 10.w),
                child: Text(
                  "点我激活红包",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFFFFFFFF),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    }
    return Container();
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.fromLTRB(10.w, 14.h, 10.w, 18.h),
      margin: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          _buildGoods(ref),
          // Padding(padding: EdgeInsets.only(top: 17.h)),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //   children: [
          //     Text(
          //       "激活红包",
          //       style: TextStyle(
          //         fontSize: 14.sp,
          //         color: const Color(0xFF333333),
          //       ),
          //     ),
          //     GradientButton(
          //       gradient: const LinearGradient(
          //         colors: [Color(0xFFFB2F25), Color(0xFFFB2F25)],
          //       ),
          //       shadow: false,
          //       radius: 12,
          //       padding: EdgeInsets.symmetric(horizontal: 11.w, vertical: 4.h),
          //       child: Text(
          //         "点我激活红包",
          //         style: TextStyle(
          //           fontSize: 12.sp,
          //           color: Colors.white,
          //         ),
          //       ),
          //     ),
          //   ],
          // ),
        ],
      ),
    );
  }
}
