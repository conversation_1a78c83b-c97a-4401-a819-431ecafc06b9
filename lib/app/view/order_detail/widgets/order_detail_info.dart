import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';
import 'package:msmds_platform/utils/toast_util.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_detail_info
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/4 10:25
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/4 10:25
/// @UpdateRemark: 更新说明
class OrderDetailInfo extends StatelessWidget {
  const OrderDetailInfo({
    super.key,
    required this.orderDetail,
  });

  final OrderDetail? orderDetail;

  Widget _buildItem(String title, Widget right) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 16.w)),
        right,
      ],
    );
  }

  // 积分
  Widget _buildPoint() {
    if (orderDetail?.point == null) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.only(top: 12.h),
      child: _buildItem(
        "返现积分：",
        Row(
          children: [
            Text(
              "${orderDetail?.point}积分",
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 订单编号
  Widget _buildOrderNo() {
    if (orderDetail?.orderNo == null) {
      return const SizedBox();
    }
    List<Widget> child = [];
    child.addAll(
      [
        Text(
          orderDetail?.orderNo ?? "-",
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 6.w)),
      ],
    );
    if (orderDetail != null) {
      child.add(
        Ink(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: () async {
              await Clipboard.setData(
                ClipboardData(text: "${orderDetail?.orderNo}"),
              );
              ToastUtil.showToast("复制成功");
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 1.h,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFEFEFEF),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                "复制",
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF666666),
                ),
              ),
            ),
          ),
        ),
      );
    }
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: _buildItem(
        "订单编号：",
        Row(
          children: child,
        ),
      ),
    );
  }

  // 下单时间
  Widget _buildCreateTime() {
    if (orderDetail?.createTime == null) {
      return const SizedBox();
    }
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: _buildItem(
        "下单时间：",
        Text(
          orderDetail!.createTime!,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  // 签收时间
  Widget _buildSignTime() {
    if (orderDetail?.finishDate != null ||
        (orderDetail?.orderType == 6 && orderDetail?.status == 4)) {
      String value = "-";
      if (orderDetail?.finishDate != null) {
        value = orderDetail!.finishDate!;
      } else {
        value = "半小时内可同步签收状态";
      }

      List<Widget> child = [];

      child.addAll(
        [
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          Padding(padding: EdgeInsets.only(right: 6.w)),
        ],
      );
      return Padding(
        padding: EdgeInsets.only(top: 16.h),
        child: _buildItem(
          "签收时间：",
          Row(
            children: child,
          ),
        ),
      );
    }
    return const SizedBox();
  }

  // 结算时间
  Widget _buildSettlementTime() {
    if (orderDetail != null &&
        orderDetail!.settlementTime != null &&
        orderDetail!.settlementTime!.isNotEmpty) {
      List<Widget> child = [];

      child.addAll(
        [
          Text(
            "${orderDetail?.settlementTime}",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          Padding(padding: EdgeInsets.only(right: 6.w)),
        ],
      );
      return Padding(
        padding: EdgeInsets.only(top: 16.h),
        child: _buildItem(
          "结算时间：",
          Row(
            children: child,
          ),
        ),
      );
    }
    return const SizedBox();
  }

  // 退款时间
  Widget _buildRefundTime() {
    if (orderDetail != null &&
        orderDetail!.refundTime != null &&
        orderDetail!.refundTime!.isNotEmpty) {
      List<Widget> child = [];

      child.addAll(
        [
          Text(
            "${orderDetail?.refundTime}",
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          Padding(padding: EdgeInsets.only(right: 6.w)),
        ],
      );
      return Padding(
        padding: EdgeInsets.only(top: 16.h),
        child: _buildItem(
          "退款时间：",
          Row(
            children: child,
          ),
        ),
      );
    }
    return const SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10.w, 14.h, 10.w, 18.h),
      margin: const EdgeInsets.fromLTRB(10, 0, 10, 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "订单详情",
            style: TextStyle(
              fontSize: 15.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
          _buildPoint(),
          _buildOrderNo(),
          _buildCreateTime(),
          _buildSignTime(),
          _buildSettlementTime(),
          _buildRefundTime(),
        ],
      ),
    );
  }
}
