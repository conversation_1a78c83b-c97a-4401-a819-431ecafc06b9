import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_detail_bottom
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/4 15:18
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/4 15:18
/// @UpdateRemark: 更新说明
class OrderDetailBottom extends ConsumerWidget {
  const OrderDetailBottom({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // var orderDetail = ref.watch(fetchOrderDetailProvider).value;
    List<Widget> child = [];
    // if (orderDetail?.orderType == OrderType.tb.type) {
    //   child.add(
    //     InkWell(
    //       child: Container(
    //         padding: EdgeInsets.symmetric(
    //           horizontal: 10.w,
    //           vertical: 8.h,
    //         ),
    //         decoration: BoxDecoration(
    //           borderRadius: BorderRadius.circular(20),
    //           border: Border.all(
    //             color: const Color(0xFFBEBEBE),
    //             width: 1,
    //           ),
    //         ),
    //         child: Text(
    //           "去淘宝签收",
    //           style: TextStyle(
    //             fontSize: 14.sp,
    //             color: Colors.black,
    //           ),
    //         ),
    //       ),
    //     ),
    //   );
    //   child.add(
    //     Padding(padding: EdgeInsets.only(right: 10.w)),
    //   );
    // }

    // if (orderDetail != null &&
    //     orderDetail.orderType != OrderType.mt.type &&
    //     orderDetail.orderType != OrderType.ele.type) {
    //   child.add(
    //     InkWell(
    //       onTap: () {
    //         // var firstGoods = orderDetail.goodsList?.first;
    //         // if (firstGoods != null) {
    //         //   ref.read(onGoodsItemTapProvider.notifier).onGoodsItemTap(
    //         //         firstGoods.goodsType,
    //         //         materialId: firstGoods.completeGoodsId,
    //         //         goodsSign: firstGoods.completeGoodsId,
    //         //         goodsId: firstGoods.completeGoodsId,
    //         //       );
    //         // }
    //       },
    //       child: Container(
    //         padding: EdgeInsets.symmetric(
    //           horizontal: 17.w,
    //           vertical: 8.h,
    //         ),
    //         decoration: BoxDecoration(
    //           borderRadius: BorderRadius.circular(20),
    //           border: Border.all(
    //             color: const Color(0xFFBEBEBE),
    //             width: 1,
    //           ),
    //         ),
    //         child: Text(
    //           "再次购买",
    //           style: TextStyle(
    //             fontSize: 14.sp,
    //             color: const Color(0xFFFB2F25),
    //           ),
    //         ),
    //       ),
    //     ),
    //   );
    //   child.add(
    //     Padding(padding: EdgeInsets.only(right: 10.w)),
    //   );
    // }
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).padding.bottom + 10,
        top: 10,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: child,
      ),
    );
  }
}
