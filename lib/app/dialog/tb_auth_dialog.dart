import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: tb_auth_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 17:37
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 17:37
/// @UpdateRemark: 更新说明
class TbAuthDialog {
  /// 淘宝授权弹窗
  static void authDialog(Function onPress) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "tb_auth_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 296.w,
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 23.h, bottom: 19.h),
                    child: Text(
                      "请完成淘宝授权",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Text(
                    "应淘宝官方要求，需先授权才有返现，不涉及资金安全或泄露个人信息，请放心",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 18.h)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(tag: "tb_auth_dialog");
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              // horizontal: 44.w,
                              vertical: 9.h,
                            ),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: const Color(0xFFCCCCCC),
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              "取消",
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: const Color(0xFF999999),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Padding(padding: EdgeInsets.only(right: 14.w)),
                      Expanded(
                        child: GradientButton(
                          padding: EdgeInsets.symmetric(
                            // horizontal: 44.w,
                            vertical: 9.h,
                          ),
                          onPress: () {
                            SmartDialog.dismiss(tag: "tb_auth_dialog");
                            onPress();
                          },
                          shadow: false,
                          radius: 20,
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFE5640),
                              Color(0xFFFA2E1B),
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          child: Text(
                            "去授权",
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 30.h)),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
