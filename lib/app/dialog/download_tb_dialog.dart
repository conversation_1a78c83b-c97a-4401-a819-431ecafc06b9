import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../../../../widgets/button/gradient_button.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: download_tb_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 16:01
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 16:01
/// @UpdateRemark: 更新说明
class DownloadTbDialog {
  /// 提示下载淘宝
  static void downloadTbDialog(Function onPress) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: true,
      tag: "download_tb_dialog",
      builder: (context) {
        return Container(
          width: 296.w,
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          margin: EdgeInsets.only(bottom: 36.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 28.h, bottom: 30.h),
                child: Text(
                  "如需购买，请先下载淘宝APP",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: GradientButton(
                      border: Border.all(color: const Color(0xFFCCCCCC)),
                      padding: EdgeInsets.symmetric(vertical: 9.h),
                      onPress: () {
                        SmartDialog.dismiss(tag: "download_tb_dialog");
                      },
                      shadow: false,
                      radius: 20,
                      gradient: const LinearGradient(
                        colors: [
                          Colors.white,
                          Colors.white,
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      child: Text(
                        "取消",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 15.w,
                  ),
                  Expanded(
                    child: GradientButton(
                      padding: EdgeInsets.symmetric(vertical: 9.h),
                      onPress: () {
                        SmartDialog.dismiss(tag: "download_tb_dialog");
                        onPress();
                      },
                      shadow: false,
                      radius: 20,
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFFE5640),
                          Color(0xFFFA2E1B),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      child: Text(
                        "去下载",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(padding: EdgeInsets.only(bottom: 20.h)),
            ],
          ),
        );
      },
    );
  }
}
