import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/config/constant.dart';

import '../../common/img/icon_addres.dart';
import '../../widgets/button/gradient_button.dart';
import '../navigation/coosea.dart';
import '../navigation/router.dart';
import '../repository/modals/convert/convert_goods.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: convert_dialog
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 11:28
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 11:28
/// @UpdateRemark: 更新说明
/// 识别弹窗
class ConvertDialog {
  /// 唤起识别弹窗
  static void showConvertDialog(ConvertGoods? goods) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "convert_dialog",
      builder: (context) {
        return SizedBox(
          width: 291.w,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Column(
                  children: [
                    Cover(goods: goods),
                    Padding(padding: EdgeInsets.only(bottom: 10.h)),
                    Title(goods: goods),
                    Padding(padding: EdgeInsets.only(bottom: 12.h)),
                    Commission(goods: goods),
                    Bottom(goods: goods),
                    Padding(padding: EdgeInsets.only(bottom: 14.h)),
                  ],
                ),
              ),
              Padding(padding: EdgeInsets.only(bottom: 20.h)),
              InkWell(
                onTap: () {
                  SmartDialog.dismiss(tag: "convert_dialog");
                },
                child: Image.network(
                  close,
                  width: 30.w,
                  height: 30.w,
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

/// 大图
class Cover extends StatelessWidget {
  const Cover({
    super.key,
    required this.goods,
  });

  final ConvertGoods? goods;

  @override
  Widget build(BuildContext context) {
    var widget = Container(
      width: 291.w,
      height: 291.h,
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(60),
      ),
    );

    if (goods?.goodsImg == null) {
      return widget;
    }

    return ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: Image.network(
        goods!.goodsImg!,
        width: 291.w,
        fit: BoxFit.contain,
        loadingBuilder: (_, child, e) {
          if (e == null) {
            return child;
          }
          return widget;
        },
        errorBuilder: (_, o, s) {
          return widget;
        },
      ),
    );
  }
}

/// 标题
class Title extends StatelessWidget {
  const Title({
    super.key,
    required this.goods,
  });

  final ConvertGoods? goods;

  String _getTitleIcon() {
    String typeImg = "${Constant.msmdsAliCdn}/detail/icon.png";
    if (goods?.platformType == 2) {
      typeImg = "${Constant.msmdsAliCdn}/detail/jd.png";
    } else if (goods?.platformType == 6) {
      typeImg = "${Constant.msmdsAliCdn}/detail/taobao.png";
    } else if (goods?.platformType == 3) {
      typeImg = "${Constant.msmdsAliCdn}/detail/tianmao.png";
    } else if (goods?.platformType == 10) {
      typeImg = "${Constant.msmdsAliCdn}/detail/pdd.png";
    } else if (goods?.platformType == 21) {
      typeImg = "${Constant.msmdsAliCdn}/APPSHOW/search_dy.png";
    } else if (goods?.platformType == 19) {
      typeImg = "${Constant.msmdsAliCdn}/peiZhiBanner/homeIcon_weipinhui.png";
    }
    return typeImg;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Padding(padding: EdgeInsets.only(left: 20.w)),
        Image.network(
          _getTitleIcon(),
          width: 16.w,
          height: 16.w,
        ),
        Padding(padding: EdgeInsets.only(right: 4.w)),
        Expanded(
          child: Text(
            "${goods?.goodsName}",
            style: TextStyle(
              fontSize: 15.sp,
              color: const Color(0xFF333333),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Padding(padding: EdgeInsets.only(right: 20.w)),
      ],
    );
  }
}

/// 返利信息
class Commission extends ConsumerWidget {
  const Commission({
    super.key,
    required this.goods,
  });

  final ConvertGoods? goods;

  String _getPricePrefix() {
    String lineThroughPrice = "原价";
    if (goods?.platformType == 2) {
      lineThroughPrice = "京东价";
    } else if (goods?.platformType == 6) {
      lineThroughPrice = "淘宝价";
    } else if (goods?.platformType == 3) {
      lineThroughPrice = "天猫价";
    } else if (goods?.platformType == 10) {
      lineThroughPrice = "拼多多价";
    } else if (goods?.platformType == 21) {
      lineThroughPrice = "抖音价";
    } else if (goods?.platformType == 19) {
      lineThroughPrice = "唯品会价";
    }
    return lineThroughPrice;
  }

  /// 返现
  Widget _buildCommission(WidgetRef ref) {
    var userData = ref.read(authProvider);
    var vipLevel = userData?.data?.vipLevel ?? -1;
    var isVip = vipLevel >= 0;
    return Row(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          height: 17.h,
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFFFF6B2B),
                Color(0xFFFF1D1D),
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(4),
              bottomLeft: Radius.circular(4),
            ),
          ),
          child: Text(
            "预估返",
            style: TextStyle(
              fontSize: 11.sp,
              color: Colors.white,
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          height: 17.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border.all(
              color: const Color(0xFFFB2F25),
              width: 1,
            ),
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(4),
              bottomRight: Radius.circular(4),
            ),
          ),
          child: Text(
            "¥${isVip ? goods?.vipCommission : goods?.noVipCommission}",
            style: TextStyle(
              fontSize: 11.sp,
              color: const Color(0xFFF93324),
            ),
          ),
        ),
      ],
    );
  }

  /// 券
  Widget _buildCoupon() {
    if (goods?.couponAmount != null) {
      return Container(
        margin: EdgeInsets.only(left: 6.w),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFF93324)),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              decoration: const BoxDecoration(
                color: Color(0xFFF93324),
                borderRadius: BorderRadius.only(
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Text(
                "券",
                style: TextStyle(
                  fontSize: 11.sp,
                  color: Colors.white,
                ),
              ),
            ),
            Padding(padding: EdgeInsets.only(right: 3.w)),
            Text(
              "${goods?.couponAmountInfo}",
              style: TextStyle(
                fontSize: 11.sp,
                color: const Color(0xFFF93324),
              ),
            ),
            Padding(padding: EdgeInsets.only(right: 5.w)),
          ],
        ),
      );
    }

    return Container();
  }

  /// 价格
  Widget _buildPrice() {
    return Text.rich(
      TextSpan(
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color(0xFFFF0E38),
        ),
        children: [
          const TextSpan(
            text: "¥",
            style: TextStyle(
              fontWeight: FontWeight.w600,
            ),
          ),
          WidgetSpan(
            child: Padding(
              padding: EdgeInsets.only(right: 2.w),
            ),
          ),
          TextSpan(
            text: "${goods?.priceAfterReceive}",
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          WidgetSpan(
            child: Padding(
              padding: EdgeInsets.only(right: 7.w),
            ),
          ),
          const TextSpan(
            text: "到手价",
          ),
          WidgetSpan(
            child: Padding(
              padding: EdgeInsets.only(right: 6.w),
            ),
          ),
          TextSpan(
            text: "${_getPricePrefix()}¥${goods?.price}",
            style: const TextStyle(
              decoration: TextDecoration.lineThrough,
              decorationThickness: 2,
              color: Color(0xFF999999),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Padding(padding: EdgeInsets.only(left: 20.w)),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildCommission(ref),
                _buildCoupon(),
              ],
            ),
            Padding(padding: EdgeInsets.only(bottom: 6.h)),
            _buildPrice(),
          ],
        ),
        Padding(padding: EdgeInsets.only(right: 20.w)),
      ],
    );
  }
}

/// 底部按钮
class Bottom extends StatelessWidget {
  const Bottom({
    super.key,
    required this.goods,
  });

  final ConvertGoods? goods;

  void _onTap(BuildContext context, WidgetRef ref) {
    SmartDialog.dismiss(tag: "convert_dialog");
    if (navigatorKey.currentContext != null) {
      Navigator.pushNamed(
        navigatorKey.currentContext!,
        CsRouter.goodsDetailPage,
        arguments: {
          "skuId": goods?.goodsId,
          "platformType": goods?.platformType,
          "bizSceneId": goods?.bizSceneId,
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 14.h),
      child: Row(
        children: [
          Padding(padding: EdgeInsets.only(left: 18.w)),
          Expanded(
            child: Consumer(
              builder: (_, ref, child) {
                return GradientButton(
                  radius: 20,
                  shadow: false,
                  onPress: () => _onTap(context, ref),
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFFF6B2B),
                      Color(0xFFFF1D1D),
                      Color(0xFFFF0F77),
                    ],
                  ),
                  child: Text(
                    "领取优惠",
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFFF7F7F7),
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(padding: EdgeInsets.only(right: 18.w)),
        ],
      ),
    );
  }
}

class TrianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2.0
      ..style = PaintingStyle.fill;

    Path path = Path();
    path.moveTo(0, 0); // Top point
    path.lineTo(size.width / 2, size.height); // Bottom right point
    path.lineTo(size.width, 0); // Bottom left point
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
