// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'withdrawal_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchWithdrawAmountListHash() =>
    r'65ec851621c182d1a1265e61af4b7f65af4edf87';

/// 获取系统预设提现金额列表
///
/// Copied from [fetchWithdrawAmountList].
@ProviderFor(fetchWithdrawAmountList)
final fetchWithdrawAmountListProvider =
    AutoDisposeFutureProvider<WithdrawalAmount?>.internal(
  fetchWithdrawAmountList,
  name: r'fetchWithdrawAmountListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchWithdrawAmountListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchWithdrawAmountListRef
    = AutoDisposeFutureProviderRef<WithdrawalAmount?>;
String _$alipayAccountHash() => r'b53441612649a44b304b6d11b6a3bb103cd9bd75';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 15:00
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 15:00
/// @UpdateRemark: 更新说明
///
/// Copied from [AlipayAccount].
@ProviderFor(AlipayAccount)
final alipayAccountProvider =
    AutoDisposeNotifierProvider<AlipayAccount, WithdrawalAccount?>.internal(
  AlipayAccount.new,
  name: r'alipayAccountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alipayAccountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlipayAccount = AutoDisposeNotifier<WithdrawalAccount?>;
String _$currentSelectWithdrawAmountHash() =>
    r'9b2014c2547801df91d91c27dadb9ff67dc357db';

/// 当前选中的提现金额
///
/// Copied from [CurrentSelectWithdrawAmount].
@ProviderFor(CurrentSelectWithdrawAmount)
final currentSelectWithdrawAmountProvider = AutoDisposeNotifierProvider<
    CurrentSelectWithdrawAmount, WithdrawalAmountItem?>.internal(
  CurrentSelectWithdrawAmount.new,
  name: r'currentSelectWithdrawAmountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentSelectWithdrawAmountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentSelectWithdrawAmount
    = AutoDisposeNotifier<WithdrawalAmountItem?>;
String _$alipayAccountEditingHash() =>
    r'b64dbbf7ec704952f34717da23ca91cf0447e835';

/// ====================绑定支付宝账户==============
/// 账号输入
///
/// Copied from [AlipayAccountEditing].
@ProviderFor(AlipayAccountEditing)
final alipayAccountEditingProvider =
    AutoDisposeNotifierProvider<AlipayAccountEditing, String?>.internal(
  AlipayAccountEditing.new,
  name: r'alipayAccountEditingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alipayAccountEditingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlipayAccountEditing = AutoDisposeNotifier<String?>;
String _$alipayAccountNameEditingHash() =>
    r'4eb49206d1c1f190d3236a10c0209a4eb7bb1d6b';

/// 账户真实名输入
///
/// Copied from [AlipayAccountNameEditing].
@ProviderFor(AlipayAccountNameEditing)
final alipayAccountNameEditingProvider =
    AutoDisposeNotifierProvider<AlipayAccountNameEditing, String?>.internal(
  AlipayAccountNameEditing.new,
  name: r'alipayAccountNameEditingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alipayAccountNameEditingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlipayAccountNameEditing = AutoDisposeNotifier<String?>;
String _$bindAlipayAccountHash() => r'15d52a8a87033a562a890b0dc40882abc9079df1';

/// See also [BindAlipayAccount].
@ProviderFor(BindAlipayAccount)
final bindAlipayAccountProvider =
    AutoDisposeNotifierProvider<BindAlipayAccount, bool?>.internal(
  BindAlipayAccount.new,
  name: r'bindAlipayAccountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bindAlipayAccountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BindAlipayAccount = AutoDisposeNotifier<bool?>;
String _$initiateWithdrawalHash() =>
    r'7b789a08456fc61a8d007d073a809792de7b288b';

/// ====================绑定支付宝账户==============
/// ====================发起提现==============
///
/// Copied from [InitiateWithdrawal].
@ProviderFor(InitiateWithdrawal)
final initiateWithdrawalProvider =
    AutoDisposeNotifierProvider<InitiateWithdrawal, void>.internal(
  InitiateWithdrawal.new,
  name: r'initiateWithdrawalProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$initiateWithdrawalHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InitiateWithdrawal = AutoDisposeNotifier<void>;
String _$withdrawalStatusHash() => r'51df048a6bd346dc24ab30c3e409b480929d1af1';

/// See also [WithdrawalStatus].
@ProviderFor(WithdrawalStatus)
final withdrawalStatusProvider =
    AutoDisposeNotifierProvider<WithdrawalStatus, WithdrawalMessage?>.internal(
  WithdrawalStatus.new,
  name: r'withdrawalStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$withdrawalStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WithdrawalStatus = AutoDisposeNotifier<WithdrawalMessage?>;
String _$withdrawalRecordListHash() =>
    r'8a11f5a929ab47fa57ac797d89fb69e56f5c48fd';

/// See also [WithdrawalRecordList].
@ProviderFor(WithdrawalRecordList)
final withdrawalRecordListProvider = AutoDisposeNotifierProvider<
    WithdrawalRecordList, WithdrawRecordResult>.internal(
  WithdrawalRecordList.new,
  name: r'withdrawalRecordListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$withdrawalRecordListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WithdrawalRecordList = AutoDisposeNotifier<WithdrawRecordResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
