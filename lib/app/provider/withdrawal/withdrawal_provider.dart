import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/me/me_provider.dart';
import 'package:msmds_platform/app/repository/modals/account/withdrawal_account.dart';
import 'package:msmds_platform/app/repository/modals/account/withdrawal_message.dart';
import 'package:msmds_platform/app/repository/service/account_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../navigation/coosea.dart';
import '../../navigation/router.dart';
import '../../repository/modals/bill/withdrawal_amount.dart';
import '../../repository/modals/bill/withdrawal_amount_item.dart';
import '../../repository/modals/bill/withdrawn_bill.dart';
import '../../repository/service/bill_service.dart';
import '../bill/bill_provider.dart';

part 'withdrawal_provider.g.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: withdrawal_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/13 15:00
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/13 15:00
/// @UpdateRemark: 更新说明

@riverpod
class AlipayAccount extends _$AlipayAccount {
  @override
  WithdrawalAccount? build() {
    getWithdrawal();
    return null;
  }

  /// 获取绑定提现账户
  void getWithdrawal() async {
    var result = await AccountService.getWithdrawalAccount();
    if (result.status == Status.completed) {
      state = result.data;
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

/// 获取系统预设提现金额列表
@riverpod
Future<WithdrawalAmount?> fetchWithdrawAmountList(
    FetchWithdrawAmountListRef ref) async {
  var userData = ref.watch(authProvider);
  if (userData != null) {
    var result = await AccountService.getWithdrawAmountList();
    if (result.data?.data != null && result.data!.data!.isNotEmpty) {
      var first = result.data!.data!.first;
      ref
          .watch(currentSelectWithdrawAmountProvider.notifier)
          .setWithdrawalAmount(first);
    }
    return result.data;
  }
  return null;
}

/// 当前选中的提现金额
@riverpod
class CurrentSelectWithdrawAmount extends _$CurrentSelectWithdrawAmount {
  @override
  WithdrawalAmountItem? build() {
    return null;
  }

  void setWithdrawalAmount(WithdrawalAmountItem? item) {
    state = item;
  }
}

/// ====================绑定支付宝账户==============

/// 账号输入
@riverpod
class AlipayAccountEditing extends _$AlipayAccountEditing {
  @override
  String? build() {
    return null;
  }

  void setAlipayNo(String? name) {
    state = name;
  }
}

/// 账户真实名输入
@riverpod
class AlipayAccountNameEditing extends _$AlipayAccountNameEditing {
  @override
  String? build() {
    return null;
  }

  void setAlipayName(String? name) {
    state = name;
  }
}

@riverpod
class BindAlipayAccount extends _$BindAlipayAccount {
  @override
  bool? build() {
    return null;
  }

  /// 绑定提现账户
  void bindWithdrawal() async {
    var accountNo = ref.read(alipayAccountEditingProvider);
    var accountName = ref.read(alipayAccountNameEditingProvider);
    debugPrint("bindWithdrawal-----: $accountNo, $accountName");
    if (accountNo != null &&
        accountNo.isNotEmpty &&
        accountName != null &&
        accountName.isNotEmpty) {
      SmartDialog.showLoading(msg: "绑定中...");
      var result = await AccountService.bindWithdrawalAccount(
        accountNo,
        accountName,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        ToastUtil.showToast("绑定成功");
        SmartDialog.dismiss(tag: "bind_alipay_dialog");
        ref.read(alipayAccountProvider.notifier).getWithdrawal();
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }
}

/// ====================绑定支付宝账户==============

/// ====================发起提现==============
@riverpod
class InitiateWithdrawal extends _$InitiateWithdrawal {
  @override
  void build() {
    return;
  }

  // 固定提现金额
  void initiate() async {
    // 当前选中的金额
    var currentAmount = ref.read(currentSelectWithdrawAmountProvider);
    if (currentAmount == null || currentAmount.money == null) {
      ToastUtil.showToast("数据异常");
      return;
    }

    // 非自定义金额，获取系统预设金额
    var sysAmount = int.tryParse(currentAmount.money!);
    debugPrint("InitiateWithdrawal-sysAmount: $sysAmount");
    if (sysAmount == null) {
      ToastUtil.showToast("数据异常");
      return;
    }
    // 可提现余额小于预设金额
    // 系统预设金额单位是分，可用余额需要*100
    var canCash = ref.read(
      walletInfoProvider.select((value) => value?.canCash ?? 0),
    );
    if (canCash * 100 < sysAmount) {
      ToastUtil.showToast("余额不足");
      return;
    }

    // 提现
    _withdrawal(currentAmount.withdrawType!, currentAmount.money!);
  }

  // 自定义金额提现
  void customInitiate(String amount) async {
    // 输入的金额
    var inputAmount = num.tryParse(amount);
    if (inputAmount == null) {
      ToastUtil.showToast("数据异常");
      return;
    }
    if (inputAmount < 1) {
      ToastUtil.showToast("提现金额不能小于1元");
      return;
    }

    // 当前余额
    var canCash = ref.read(
      walletInfoProvider.select((value) => value?.canCash ?? 0),
    );
    if (canCash < inputAmount) {
      ToastUtil.showToast("余额不足");
      return;
    }

    // 当前选中的金额
    var currentAmount = ref.read(currentSelectWithdrawAmountProvider);
    if (currentAmount == null || currentAmount.money == null) {
      ToastUtil.showToast("数据异常");
      return;
    }

    // 提现
    _withdrawal(
      currentAmount.withdrawType!,
      (inputAmount * 100).toInt().toString(),
    );
  }

  // 开始提现
  void _withdrawal(String withdrawType, String canCash) async {
    SmartDialog.showLoading(msg: "提现中...");
    var result = await AccountService.initiateWithdrawal(withdrawType, canCash);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("_withdrawal: ${result.data?.status}");
      debugPrint("_withdrawal: ${result.data?.userWithdraw?.money}");
      if (result.data?.status == true) {
        /// 提现成功，刷新钱包信息和提现账单明细
        ref.read(walletInfoProvider.notifier).loadWalletInfo();
        ref.read(withdrawnBillListProvider.notifier).loadData();
      }
      ref.watch(withdrawalStatusProvider.notifier).setMessage(result.data);
      navigatorKey.currentState?.pushNamed(CsRouter.withdrawalResult);
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

@riverpod
class WithdrawalStatus extends _$WithdrawalStatus {
  @override
  WithdrawalMessage? build() {
    return null;
  }

  void setMessage(WithdrawalMessage? message) async {
    state = message;
  }
}

/// ====================发起提现==============

/// ====================提现记录==============
/// 页长
const int pageSize = 20;

/// 提现记录结果
class WithdrawRecordResult {
  final int pageNo;
  final List<WithdrawnBill?>? recordList;
  final LoadState? loadState;

  const WithdrawRecordResult({
    this.pageNo = 1,
    this.recordList,
    this.loadState,
  });

  WithdrawRecordResult copyWith({
    int? page,
    List<WithdrawnBill?>? recordList,
    LoadState? loadState,
  }) {
    return WithdrawRecordResult(
      pageNo: page ?? pageNo,
      recordList: recordList ?? this.recordList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class WithdrawalRecordList extends _$WithdrawalRecordList {
  @override
  WithdrawRecordResult build() {
    state = const WithdrawRecordResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      state = state.copyWith(
        page: 1,
        loadState: null,
      );
      var result = await BillService.listUserWithdrawnBill(
        state.pageNo,
        pageSize,
      );
      if (result.status == Status.completed) {
        state = state.copyWith(
          recordList: result.data?.list ?? [],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await BillService.listUserWithdrawnBill(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          recordList: [...?state.recordList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
