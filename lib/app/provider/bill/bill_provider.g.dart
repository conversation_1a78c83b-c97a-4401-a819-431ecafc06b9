// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bill_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$withdrawnBillListHash() => r'7930f357be0702356e0cc6f7d1945d43b6397d55';

/// See also [WithdrawnBillList].
@ProviderFor(WithdrawnBillList)
final withdrawnBillListProvider = AutoDisposeNotifierProvider<WithdrawnBillList,
    WithdrawnBillResult>.internal(
  WithdrawnBillList.new,
  name: r'withdrawnBillListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$withdrawnBillListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WithdrawnBillList = AutoDisposeNotifier<WithdrawnBillResult>;
String _$comingSoonBillListHash() =>
    r'0b87238e59ca7759012f7d3c456c0cf9798be967';

/// See also [ComingSoonBillList].
@ProviderFor(ComingSoonBillList)
final comingSoonBillListProvider = AutoDisposeNotifierProvider<
    ComingSoonBillList, ComingSoonBillResult>.internal(
  ComingSoonBillList.new,
  name: r'comingSoonBillListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$comingSoonBillListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ComingSoonBillList = AutoDisposeNotifier<ComingSoonBillResult>;
String _$creditedBillListHash() => r'118edccc561945e49c36915ffb19e243e70fe465';

/// See also [CreditedBillList].
@ProviderFor(CreditedBillList)
final creditedBillListProvider =
    AutoDisposeNotifierProvider<CreditedBillList, CreditedBillResult>.internal(
  CreditedBillList.new,
  name: r'creditedBillListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creditedBillListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreditedBillList = AutoDisposeNotifier<CreditedBillResult>;
String _$allBillListHash() => r'cbc2f93ed65a06cd6988436269f66f1aa97d2aa4';

/// See also [AllBillList].
@ProviderFor(AllBillList)
final allBillListProvider =
    AutoDisposeNotifierProvider<AllBillList, AllBillResult>.internal(
  AllBillList.new,
  name: r'allBillListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$allBillListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AllBillList = AutoDisposeNotifier<AllBillResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
