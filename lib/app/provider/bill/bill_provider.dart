import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/repository/modals/bill/bill.dart';
import 'package:msmds_platform/app/repository/service/bill_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../repository/modals/bill/withdrawn_bill.dart';

part 'bill_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON> <PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: bill_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/12 15:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/12 15:06
/// @UpdateRemark: 更新说明

/// 账单类型
enum BillType {
  withdrawn(-1, "已提现"),
  waller(1, "即将入账"),
  settled(2, "已入账"),
  all(0, "全部");

  final int type;
  final String name;

  const BillType(this.type, this.name);
}

/// 页长
const int pageSize = 20;

/// ================已提现=====================
class WithdrawnBillResult {
  final int pageNo;
  final List<WithdrawnBill?>? withdrawnBillList;
  final LoadState? loadState;

  const WithdrawnBillResult({
    this.pageNo = 1,
    this.withdrawnBillList,
    this.loadState,
  });

  WithdrawnBillResult copyWith({
    int? page,
    List<WithdrawnBill?>? withdrawnBillList,
    LoadState? loadState,
  }) {
    return WithdrawnBillResult(
      pageNo: page ?? pageNo,
      withdrawnBillList: withdrawnBillList ?? this.withdrawnBillList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class WithdrawnBillList extends _$WithdrawnBillList {
  @override
  WithdrawnBillResult build() {
    state = const WithdrawnBillResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await BillService.listUserWithdrawnBill(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
      state = state.copyWith(
        withdrawnBillList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await BillService.listUserWithdrawnBill(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
        state = state.copyWith(
          withdrawnBillList: [...?state.withdrawnBillList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
/// ================已提现=====================

/// ===================即将入账==================
class ComingSoonBillResult {
  final int pageNo;
  final List<Bill?>? billList;
  final LoadState? loadState;

  const ComingSoonBillResult({
    this.pageNo = 1,
    this.billList,
    this.loadState,
  });

  ComingSoonBillResult copyWith({
    int? page,
    List<Bill?>? billList,
    LoadState? loadState,
  }) {
    return ComingSoonBillResult(
      pageNo: page ?? pageNo,
      billList: billList ?? this.billList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class ComingSoonBillList extends _$ComingSoonBillList {
  @override
  ComingSoonBillResult build() {
    state = const ComingSoonBillResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await BillService.listUserWallerBill(
      BillType.waller.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
      state = state.copyWith(
        billList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await BillService.listUserWallerBill(
      BillType.waller.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
        state = state.copyWith(
          billList: [...?state.billList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

/// ===================即将入账==================

/// ===================已入账==================
class CreditedBillResult {
  final int pageNo;
  final List<Bill?>? billList;
  final LoadState? loadState;

  const CreditedBillResult({
    this.pageNo = 1,
    this.billList,
    this.loadState,
  });

  CreditedBillResult copyWith({
    int? page,
    List<Bill?>? billList,
    LoadState? loadState,
  }) {
    return CreditedBillResult(
      pageNo: page ?? pageNo,
      billList: billList ?? this.billList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class CreditedBillList extends _$CreditedBillList {
  @override
  CreditedBillResult build() {
    state = const CreditedBillResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await BillService.listUserWallerBill(
      BillType.settled.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
      state = state.copyWith(
        billList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await BillService.listUserWallerBill(
      BillType.settled.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
        state = state.copyWith(
          billList: [...?state.billList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

/// ===================已入账==================

/// ===================全部账单==================
class AllBillResult {
  final int pageNo;
  final List<Bill?>? billList;
  final LoadState? loadState;

  const AllBillResult({
    this.pageNo = 1,
    this.billList,
    this.loadState,
  });

  AllBillResult copyWith({
    int? page,
    List<Bill?>? billList,
    LoadState? loadState,
  }) {
    return AllBillResult(
      pageNo: page ?? pageNo,
      billList: billList ?? this.billList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class AllBillList extends _$AllBillList {
  @override
  AllBillResult build() {
    state = const AllBillResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await BillService.listUserWallerBill(
      BillType.all.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
      state = state.copyWith(
        billList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await BillService.listUserWallerBill(
      BillType.all.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        debugPrint("result.data?.hasNextPage: ${result.data?.hasNextPage}");
        state = state.copyWith(
          billList: [...?state.billList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

/// ===================全部账单==================
