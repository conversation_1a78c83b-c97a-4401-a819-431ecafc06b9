import 'package:msmds_platform/app/repository/modals/goods/goods.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_pkg.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'goods_provider.g.dart';
/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: goods_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/28 11:36
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/28 11:36
/// @UpdateRemark: 更新说明

/// 页长
const int pageSize = 20;

/// 商品包获取商品列表结果
class GoodsResult {
  final int pageNo;
  final GoodsPkg? pkgId;
  final List<Goods?>? goods;
  final LoadState? loadState;

  const GoodsResult({
    this.pageNo = 1,
    this.pkgId,
    this.goods,
    this.loadState,
  });

  GoodsResult copyWith({
    int? page,
    GoodsPkg? pkgId,
    List<Goods?>? goods,
    LoadState? loadState,
  }) {
    return GoodsResult(
      pageNo: page ?? pageNo,
      pkgId: pkgId ?? this.pkgId,
      goods: goods ?? this.goods,
      loadState: loadState ?? this.loadState,
    );
  }
}

/// ================首页京东商品包======================
@Riverpod(keepAlive: true)
class JdGoodsByPkg extends _$JdGoodsByPkg {
  @override
  GoodsResult build() {
    state = const GoodsResult();
    loadGoods();
    return state;
  }

  /// 获取商品包配置并获取第一个
  Future<GoodsPkg?> fetchPkgInfo() async {
    var result = await GoodsService.getListGoodsPkg("home_page", 2);
    return result.data?.first;
  }

  /// 加载数据
  void loadGoods() async {
    var pkgInfo = await fetchPkgInfo();
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        pkgId: pkgInfo,
        page: 1,
        loadState: null,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        state = state.copyWith(
          goods: result.data?.list,
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    var pkgInfo = state.pkgId;
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        loadState: LoadState.loading,
        page: state.pageNo + 1,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        if (result.data != null) {
          state = state.copyWith(
            goods: [...?state.goods, ...?result.data?.list],
            loadState: result.data?.hasNextPage == true
                ? LoadState.idle
                : LoadState.noMore,
          );
        } else {
          state = state.copyWith(
            loadState: LoadState.noMore,
          );
        }
      }
    }
  }
}

/// ================首页京东商品包======================

/// ================首页淘宝商品包======================
@Riverpod(keepAlive: true)
class TbGoodsByPkg extends _$TbGoodsByPkg {
  @override
  GoodsResult build() {
    state = const GoodsResult();
    loadGoods();
    return state;
  }

  /// 获取商品包配置并获取第一个
  Future<GoodsPkg?> fetchPkgInfo() async {
    var result = await GoodsService.getListGoodsPkg("home_page", 1);
    return result.data?.first;
  }

  /// 加载数据
  void loadGoods() async {
    var pkgInfo = await fetchPkgInfo();
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        pkgId: pkgInfo,
        page: 1,
        loadState: null,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        state = state.copyWith(
          goods: result.data?.list,
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    var pkgInfo = state.pkgId;
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        loadState: LoadState.loading,
        page: state.pageNo + 1,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        if (result.data != null) {
          state = state.copyWith(
            goods: [...?state.goods, ...?result.data?.list],
            loadState: result.data?.hasNextPage == true
                ? LoadState.idle
                : LoadState.noMore,
          );
        } else {
          state = state.copyWith(
            loadState: LoadState.noMore,
          );
        }
      }
    }
  }
}

/// ================首页淘宝商品包======================

/// ================首页拼多多商品包======================
@Riverpod(keepAlive: true)
class PddGoodsByPkg extends _$PddGoodsByPkg {
  @override
  GoodsResult build() {
    state = const GoodsResult();
    loadGoods();
    return state;
  }

  /// 获取商品包配置并获取第一个
  Future<GoodsPkg?> fetchPkgInfo() async {
    var result = await GoodsService.getListGoodsPkg("home_page", 3);
    return result.data?.first;
  }

  /// 加载数据
  void loadGoods() async {
    var pkgInfo = await fetchPkgInfo();
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        pkgId: pkgInfo,
        page: 1,
        loadState: null,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        state = state.copyWith(
          goods: result.data?.list,
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    var pkgInfo = state.pkgId;
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        loadState: LoadState.loading,
        page: state.pageNo + 1,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        if (result.data != null) {
          state = state.copyWith(
            goods: [...?state.goods, ...?result.data?.list],
            loadState: result.data?.hasNextPage == true
                ? LoadState.idle
                : LoadState.noMore,
          );
        } else {
          state = state.copyWith(
            loadState: LoadState.noMore,
          );
        }
      }
    }
  }
}

/// ================首页拼多多商品包======================
