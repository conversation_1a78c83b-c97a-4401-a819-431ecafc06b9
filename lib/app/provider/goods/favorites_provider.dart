import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/repository/service/archive_service.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/modals/archive/favorite_goods.dart';
import '../../repository/modals/archive/favorite_goods_list.dart';

part 'favorites_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.provider.goods
/// @ClassName: favorites_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/26 11:10
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/26 11:10
/// @UpdateRemark: 更新说明
class FavoriteTab {
  String name;
  int type;

  FavoriteTab(this.name, this.type);
}

final List<FavoriteTab> favoriteTab = [
  FavoriteTab("淘宝", 6),
  FavoriteTab("拼多多", 10),
  FavoriteTab("京东", 2),
];

/// 页长
const int pageSize = 20;

/// 收藏商品列表结果
class FavoriteGoodsResult {
  final int pageNo;
  final FavoriteTab? favoriteTab;
  final FavoriteGoodsList? favGoodsData;
  final List<FavoriteGoods?>? favGoodsList;
  final LoadState? loadState;

  const FavoriteGoodsResult({
    this.pageNo = 1,
    this.favoriteTab,
    this.favGoodsData,
    this.favGoodsList,
    this.loadState,
  });

  FavoriteGoodsResult copyWith({
    int? pageNo,
    FavoriteTab? favoriteTab,
    FavoriteGoodsList? favGoodsData,
    List<FavoriteGoods?>? favGoodsList,
    LoadState? loadState,
  }) {
    return FavoriteGoodsResult(
      pageNo: pageNo ?? this.pageNo,
      favoriteTab: favoriteTab ?? this.favoriteTab,
      favGoodsData: favGoodsData ?? this.favGoodsData,
      favGoodsList: favGoodsList ?? this.favGoodsList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class FavoriteInfo extends _$FavoriteInfo {
  @override
  FavoriteGoodsResult build() {
    state = FavoriteGoodsResult(favoriteTab: favoriteTab.first);
    loadGoods();
    return state;
  }

  /// 切换tab
  void changeTab(FavoriteTab item) {
    state = state.copyWith(favoriteTab: item);
    loadGoods();
  }

  /// 加载数据
  void loadGoods() async {
    state = state.copyWith(
      pageNo: 1,
      loadState: null,
    );
    var result = await ArchiveService.findUserGoodsCollect(
      state.favoriteTab!.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      bool? hasNextPage = result.data?.hasNextPage;
      debugPrint("findUserGoodsCollect: $hasNextPage");
      debugPrint("findUserGoodsCollect: ${result.data?.total}");
      state = state.copyWith(
        favGoodsData: result.data,
        favGoodsList: result.data?.list ?? [],
        loadState: hasNextPage == true ? LoadState.idle : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      pageNo: state.pageNo + 1,
    );
    var result = await ArchiveService.findUserGoodsCollect(
      state.favoriteTab!.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        bool? hasNextPage = result.data?.hasNextPage;
        state = state.copyWith(
          favGoodsData: result.data,
          favGoodsList: [...?state.favGoodsList, ...?result.data?.list],
          loadState: hasNextPage == true ? LoadState.idle : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

// 管理状态
@riverpod
class FavoriteManageState extends _$FavoriteManageState {
  @override
  bool build() {
    return false;
  }

  /// 切换状态
  void changeState() {
    state = !state;
  }
}

// 管理状态
@riverpod
class FavoriteManageAllSelect extends _$FavoriteManageAllSelect {
  @override
  bool build() {
    checkAllSelect();
    return false;
  }

  // 检查是否全选
  void checkAllSelect() {
    // 删除列表
    var deleteList = ref.watch(favoriteManageDeleteProvider);
    // 所有收藏列表
    var favList = ref.watch(
      favoriteInfoProvider.select((value) => value.favGoodsList),
    );
    if (favList != null) {
      state = deleteList.length == favList.length;
    }
  }
}

// 删除收藏
@riverpod
class FavoriteManageDelete extends _$FavoriteManageDelete {
  @override
  List<FavoriteGoods?> build() {
    return [];
  }

  // 增加或者从列表中取消选中的收藏商品
  void addOrCancelGoods(FavoriteGoods favoriteGoods) {
    // 判断是否在列表中
    int index = state.indexWhere((element) => element?.id == favoriteGoods.id);
    if (index != -1) {
      // 从删除列表中移除
      state.removeAt(index);
      state = [...state];
    } else {
      // 添加到列表中
      state = [...state, favoriteGoods];
    }
    ref.read(favoriteManageAllSelectProvider.notifier).checkAllSelect();
  }

  // 全选或者取消全选
  void selectAll(bool isAllSelect) {
    debugPrint("selectAll: $isAllSelect");
    var favList = ref.read(
      favoriteInfoProvider.select((value) => value.favGoodsList),
    );
    if (favList != null && favList.isNotEmpty) {
      state = isAllSelect ? [] : [...favList];
      ref.read(favoriteManageAllSelectProvider.notifier).checkAllSelect();
    }
  }

  // 删除收藏
  void deleteFavorite() async {
    List<int> deleteList = state
        .where((element) => element != null && element.id != null)
        .map((e) => e!.id!)
        .toList();
    if (deleteList.isEmpty) {
      ToastUtil.showToast("请选择要删除商品");
      return;
    }
    SmartDialog.showLoading(msg: "删除中...");
    var result = await ArchiveService.removeGoodsCollect(deleteList);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ref.read(favoriteInfoProvider.notifier).loadGoods();
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

// 检查商品是否收藏
@riverpod
class CheckGoodsFavorite extends _$CheckGoodsFavorite {
  @override
  bool build() {
    return false;
  }

  // 检查
  void check(String? goodsId, int? goodsType) async {
    if (goodsId == null || goodsType == null) return;
    var result = await ArchiveService.checkGoodsCollect(goodsId, goodsType);
    debugPrint("CheckGoodsFavorite: ${result.data}");
    if (result.status == Status.completed) {
      state = result.data ?? false;
    }
  }

  // 取消或者添加收藏
  void addFavOrCancel(bool fav, String? goodsId, int? goodsType) async {
    if (goodsId == null || goodsType == null) return;
    if (fav) {
      // 取消收藏
      var deleteResult =
          await ArchiveService.removeGoodsCollectGoods([goodsId], goodsType);
      if (deleteResult.status == Status.completed) {
        state = false;
      } else {
        ToastUtil.showToast(deleteResult.exception!.getMessage());
      }
    } else {
      // 添加收藏
      var addResult = await ArchiveService.saveGoodsCollect(goodsId, goodsType);
      if (addResult.status == Status.completed) {
        state = true;
      } else {
        ToastUtil.showToast(addResult.exception!.getMessage());
      }
    }
  }
}
