import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../../utils/toast_util.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/modals/archive/favorite_goods.dart';
import '../../repository/modals/archive/favorite_goods_list.dart';
import '../../repository/service/archive_service.dart';

part 'history_provider.g.dart';
/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.provider.goods
/// @ClassName: history_provider
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/27 10:37
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/27 10:37
/// @UpdateRemark: 更新说明
class HistoryTab {
  String name;
  int type;

  HistoryTab(this.name, this.type);
}

final List<HistoryTab> historyTab = [
  HistoryTab("全部", 0),
  HistoryTab("淘宝", 6),
  HistoryTab("拼多多", 10),
  HistoryTab("京东", 2),
  HistoryTab("唯品会", 19),
];

/// 页长
const int pageSize = 20;

/// 收藏商品列表结果
class HistoryGoodsResult {
  final int pageNo;
  final HistoryTab? historyTab;
  final FavoriteGoodsList? hisGoodsData;
  final List<FavoriteGoods?>? hisGoodsList;
  final LoadState? loadState;

  const HistoryGoodsResult({
    this.pageNo = 1,
    this.historyTab,
    this.hisGoodsData,
    this.hisGoodsList,
    this.loadState,
  });

  HistoryGoodsResult copyWith({
    int? pageNo,
    HistoryTab? historyTab,
    FavoriteGoodsList? hisGoodsData,
    List<FavoriteGoods?>? hisGoodsList,
    LoadState? loadState,
  }) {
    return HistoryGoodsResult(
      pageNo: pageNo ?? this.pageNo,
      historyTab: historyTab ?? this.historyTab,
      hisGoodsData: hisGoodsData ?? this.hisGoodsData,
      hisGoodsList: hisGoodsList ?? this.hisGoodsList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class HistoryInfo extends _$HistoryInfo {
  @override
  HistoryGoodsResult build() {
    state = HistoryGoodsResult(historyTab: historyTab.first);
    loadGoods();
    return state;
  }

  /// 切换tab
  void changeTab(HistoryTab item) {
    state = state.copyWith(historyTab: item);
    loadGoods();
  }

  /// 加载数据
  void loadGoods() async {
    state = state.copyWith(
      pageNo: 1,
      loadState: null,
    );
    var result = await ArchiveService.findGoodsBrowsingRecord(
      state.historyTab!.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      bool? hasNextPage = result.data?.hasNextPage;
      debugPrint("findGoodsBrowsingRecord: $hasNextPage");
      debugPrint("findGoodsBrowsingRecord: ${result.data?.total}");
      state = state.copyWith(
        hisGoodsData: result.data,
        hisGoodsList: result.data?.list ?? [],
        loadState: hasNextPage == true ? LoadState.idle : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      pageNo: state.pageNo + 1,
    );
    var result = await ArchiveService.findGoodsBrowsingRecord(
      state.historyTab!.type,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        bool? hasNextPage = result.data?.hasNextPage;
        state = state.copyWith(
          hisGoodsData: result.data,
          hisGoodsList: [...?state.hisGoodsList, ...?result.data?.list],
          loadState: hasNextPage == true ? LoadState.idle : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

// 管理状态
@riverpod
class HistoryManageState extends _$HistoryManageState {
  @override
  bool build() {
    return false;
  }

  /// 切换状态
  void changeState() {
    state = !state;
  }
}

// 管理状态
@riverpod
class HistoryManageAllSelect extends _$HistoryManageAllSelect {
  @override
  bool build() {
    checkAllSelect();
    return false;
  }

  // 检查是否全选
  void checkAllSelect() {
    // 删除列表
    var deleteList = ref.watch(historyManageDeleteProvider);
    // 所有浏览列表
    var hisList = ref.watch(
      historyInfoProvider.select((value) => value.hisGoodsList),
    );
    if (hisList != null) {
      state = deleteList.length == hisList.length;
    }
  }
}

// 删除浏览记录
@riverpod
class HistoryManageDelete extends _$HistoryManageDelete {
  @override
  List<FavoriteGoods?> build() {
    return [];
  }

  // 增加或者从列表中取消选中的浏览商品
  void addOrCancelGoods(FavoriteGoods favoriteGoods) {
    // 判断是否在列表中
    int index = state.indexWhere((element) => element?.id == favoriteGoods.id);
    if (index != -1) {
      // 从删除列表中移除
      state.removeAt(index);
      state = [...state];
    } else {
      // 添加到列表中
      state = [...state, favoriteGoods];
    }
    ref.read(historyManageAllSelectProvider.notifier).checkAllSelect();
  }

  // 全选或者取消全选
  void selectAll(bool isAllSelect) {
    debugPrint("selectAll: $isAllSelect");
    var hisList = ref.read(
      historyInfoProvider.select((value) => value.hisGoodsList),
    );
    if (hisList != null && hisList.isNotEmpty) {
      state = isAllSelect ? [] : [...hisList];
      ref.read(historyManageAllSelectProvider.notifier).checkAllSelect();
    }
  }

  // 删除浏览记录
  void deleteHistory() async {
    List<int> deleteList = state
        .where((element) => element != null && element.id != null)
        .map((e) => e!.id!)
        .toList();
    if (deleteList.isEmpty) {
      ToastUtil.showToast("请选择要删除商品");
      return;
    }
    SmartDialog.showLoading(msg: "删除中...");
    var result = await ArchiveService.deleteGoodsBrowsingRecord(deleteList);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ref.read(historyInfoProvider.notifier).loadGoods();
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

// 添加浏览记录
@riverpod
class SaveGoodsBrowsingHistory extends _$SaveGoodsBrowsingHistory {
  @override
  void build() {
    return;
  }

  // 添加记录
  void addBrowsingHistory(String? goodsId, int? goodsType) {
    if (goodsId == null || goodsType == null) return;
    // 添加收藏
    ArchiveService.saveGoodsBrowsingRecord(goodsId, goodsType);
  }
}