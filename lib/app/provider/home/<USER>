import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:msmds_platform/app/dialog/convert_dialog.dart';
import 'package:msmds_platform/app/dialog/convert_fail_dialog.dart';
import 'package:msmds_platform/app/navigation/coosea.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/order/order_detail_provider.dart';
import 'package:msmds_platform/app/provider/search/platform_provider.dart';
import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:msmds_platform/app/provider/setting/info_collection_provider.dart';
import 'package:msmds_platform/app/repository/modals/convert/convert_goods.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/utils/router_util.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../repository/service/account_service.dart';

part 'home_provider.g.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: home_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/29 11:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/29 11:12
/// @UpdateRemark: 更新说明
class HomeController {
  int index;
  PageController pageController;

  HomeController(this.index, this.pageController);
}

@riverpod
class Home extends _$Home {
  PageController pageController = PageController();

  @override
  HomeController build() {
    ref.onDispose(() {
      pageController.dispose();
    });
    return HomeController(0, pageController);
  }

  void jumpToPage(int index, {bool init = false}) {
    if (init) {
      pageController = PageController(initialPage: index);
      state = HomeController(index, pageController);
    } else {
      if (index == 1) {
        // 切换到签到需要先登录
        if (navigatorKey.currentContext != null) {
          RouterUtil.checkLogin(
            navigatorKey.currentContext!,
            call: () {
              state = HomeController(index, pageController);
              pageController.jumpToPage(index);
            },
            execute: true,
          );
        }
      } else {
        state = HomeController(index, pageController);
        pageController.jumpToPage(index);
      }
    }
  }

  /// deep link 跳转
  void openAppLink(BuildContext context, Uri uri) {
    var params = uri.queryParameters["params"];
    if (params != null && params.isNotEmpty) {
      var map = jsonDecode(params);
      var category = map["category"];
      if (category == "jump") {
        /// 获取跳转页面名称
        var routerName = map["url"];
        switch (routerName) {
          case CsRouter.searchPre:

            /// 搜索
            searchPage(map);
            break;
          case CsRouter.withdrawalRecord:

            /// 提现记录
            RouterUtil.checkLogin(
              context,
              call: () {
                navigatorKey.currentState?.pushNamed(CsRouter.withdrawalRecord);
              },
              execute: true,
            );
            break;
          case CsRouter.orderDetail:

            /// 订单详情
            RouterUtil.checkLogin(
              context,
              call: () {
                int orderId = map["orderId"];
                ref
                    .watch(currentOrderProvider.notifier)
                    .setCurrentOrder(orderId.toString());
                navigatorKey.currentState?.pushNamed(CsRouter.orderDetail);
              },
              execute: true,
            );
            break;
          default:
            if (routerName != null &&
                routerName != CsRouter.home &&
                routerName != '/') {
              bool? needLogin = map["needLogin"];
              if (needLogin == true && GlobalConfig.account == null) {
                RouterUtil.checkLogin(
                  context,
                  call: () {
                    if (GlobalConfig.currentRouter == routerName) {
                      navigatorKey.currentState
                          ?.pushReplacementNamed(routerName);
                    } else {
                      navigatorKey.currentState?.pushNamed(routerName);
                    }
                  },
                  execute: true,
                );
              } else {
                if (GlobalConfig.currentRouter == routerName) {
                  navigatorKey.currentState?.pushReplacementNamed(routerName);
                } else {
                  navigatorKey.currentState?.pushNamed(routerName);
                }
              }
            }
            break;
        }
      } else if (category == "switch") {
        /// 首页切换tab
        var index = map["index"];
        ref.watch(homeProvider.notifier).jumpToPage(index);
      }
    }
  }

  /// 搜索页处理
  void searchPage(Map map) {
    /// 搜索平台，默认淘宝
    int platformType = map["platformType"] ?? 1;
    String keyword = map["keyword"];

    /// 设置搜索平台
    ref.watch(platformProvider.notifier).setCurrentPlatform(
          platformType,
          isSearch: false,
        );

    /// 设置搜索词
    ref.watch(searchKeywordProvider.notifier).setKeyword(keyword);

    /// 搜索跳转
    if (GlobalConfig.currentRouter == CsRouter.search) {
      navigatorKey.currentState?.pushReplacementNamed(CsRouter.search);
    } else if (GlobalConfig.currentRouter == CsRouter.searchPre) {
      navigatorKey.currentState?.pushNamed(CsRouter.search);
    } else {
      ref.watch(searchAutoFocusProvider.notifier).setAutoFocus(false);
      navigatorKey.currentState?.pushNamed(CsRouter.searchPre);
      navigatorKey.currentState?.pushNamed(CsRouter.search);
    }
  }
}

/// 识别弹窗
@riverpod
class ConvertContent extends _$ConvertContent {
  @override
  ConvertGoods? build() {
    return null;
  }

  /// 识别接口
  void convert() async {
    try {
      var content = await Clipboard.getData(Clipboard.kTextPlain);
      debugPrint("convert: $content");
      ref.watch(collectionClipboardProvider.notifier).collection(content?.text);
      if (content != null && content.text != null && content.text!.isNotEmpty) {
        PackageInfo packageInfo = await PackageInfo.fromPlatform();
        var result = await GoodsService.clipboardConvert(
          packageInfo.version,
          content.text!,
        );
        debugPrint("convert-eeee: $result");
        if (result.status == Status.completed) {
          Clipboard.setData(const ClipboardData(text: ""));
          // 先下掉拼多多
          if (result.data?.goods != null &&
              result.data?.goods?.platformType != 10) {
            /// 识别成功
            state = result.data?.goods;
            ConvertDialog.showConvertDialog(result.data?.goods);
          } else {
            /// 识别失败
            ConvertFailDialog.showConvertFailDialog(
              result.data?.displayStandardSearchWord,
            );
          }
        }
      }
    } catch (e) {
      debugPrint("convert-eeee: $e");
    }
  }
}

/// 获取最新用户信息
@riverpod
class NewestUserInfo extends _$NewestUserInfo {
  @override
  void build() {
    return;
  }

  /// 获取最新数据并更新到本地
  void getNewestUserInfo() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var userInfoById = await AccountService.getAccountDetail();
      if (userInfoById.status == Status.completed) {
        ref.read(authProvider.notifier).refreshUserInfo(userInfoById.data);
      }
    }
  }
}
