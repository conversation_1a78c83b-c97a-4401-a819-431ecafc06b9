import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/reminder/reminder.dart';
import 'package:msmds_platform/app/repository/service/reminder_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../../utils/toast_util.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/modals/convert/convert_goods.dart';

part 'subscribe_reminder_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: subscribe_reminder_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/24 14:10
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/24 14:10
/// @UpdateRemark: 更新说明

/// 页长
const int pageSize = 2;

class ReminderResult {
  final int pageNo;
  final bool notice;
  final int? goodsNum;
  final int? goodsOrderRemind;
  final List<Reminder?>? reminders;
  final LoadState? loadState;

  const ReminderResult({
    this.pageNo = 1,
    this.notice = true,
    this.goodsNum,
    this.goodsOrderRemind,
    this.reminders,
    this.loadState,
  });

  ReminderResult copyWith({
    int? page,
    int? goodsNum,
    bool? notice,
    int? goodsOrderRemind,
    List<Reminder?>? reminders,
    LoadState? loadState,
  }) {
    return ReminderResult(
      pageNo: page ?? pageNo,
      notice: notice ?? this.notice,
      goodsNum: goodsNum ?? this.goodsNum,
      goodsOrderRemind: goodsOrderRemind ?? this.goodsOrderRemind,
      reminders: reminders ?? this.reminders,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class ReminderGoodsList extends _$ReminderGoodsList {
  @override
  ReminderResult build() {
    state = const ReminderResult();
    loadData();
    return state;
  }

  /// 设置是否开启通知
  void setNotice(bool notice) async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ReminderService.toggleReminderNotice(notice ? 1 : 0);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      state = state.copyWith(notice: notice);
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 加载数据
  void loadData() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      state = state.copyWith(
        page: 1,
        loadState: null,
      );
      var result = await ReminderService.listReminder(
        state.pageNo,
        pageSize,
      );

      if (result.status == Status.completed) {
        state = state.copyWith(
          notice: result.data?.goodsOrderRemind == 1,
          goodsNum: result.data?.goodsNum,
          goodsOrderRemind: result.data?.goodsOrderRemind,
          reminders: result.data?.pageInfo?.list ?? [],
          loadState: result.data?.pageInfo?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await ReminderService.listReminder(
      state.pageNo,
      pageSize,
    );

    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          reminders: [...?state.reminders, ...?result.data?.pageInfo?.list],
          loadState: result.data?.pageInfo?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

///==================保存提醒============
@riverpod
class SaveReminder extends _$SaveReminder {
  @override
  void build() {
    return;
  }

  void saveReminder(ConvertGoods? goods) async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ReminderService.saveReminderGoods(goods);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ToastUtil.showToast("已订阅，请留意系统通知");
      ref.read(reminderGoodsListProvider.notifier).loadData();
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 去除所有提醒
  void cleanAllReminder() async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ReminderService.deleteReminderAll();
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ref.read(reminderGoodsListProvider.notifier).loadData();
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 去除单个提醒
  void cleanReminder(int id) async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ReminderService.deleteReminder(id);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ref.read(reminderGoodsListProvider.notifier).loadData();
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}
