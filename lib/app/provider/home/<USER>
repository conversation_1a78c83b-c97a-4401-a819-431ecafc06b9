import 'dart:async';

import 'package:flutter/material.dart';
import 'package:msmds_platform/app/repository/modals/activity/free_buy_good_item.dart';
import 'package:msmds_platform/app/repository/modals/activity/valid_free_buy.dart';
import 'package:msmds_platform/utils/prefs_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/service/account_service.dart';
import '../account/auth_provider.dart';

part 'free_buy_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: free_buy_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/3/28 14:30
/// @UpdateUser: frankylee
/// @UpdateData: 2024/3/28 14:30
/// @UpdateRemark: 更新说明

/// ==================新人引导显示===============
@riverpod
class FreeBuyTutorial extends _$FreeBuyTutorial {
  @override
  bool build() {
    return false;
  }

  void showTitleTutorial(bool value) {
    debugPrint("showTitleTutorial: $value");
    var isShowed = PrefsUtil().getBool(PrefsKeys.freeBuyKey);
    if (isShowed != true) {
      state = value;
    }
  }
}

@riverpod
class FreeBuyEntranceTutorial extends _$FreeBuyEntranceTutorial {
  @override
  bool build() {
    return false;
  }

  void showTitleTutorial(bool value) {
    debugPrint("showEntranceTutorial: $value");
    state = value;
  }
}

/// ==================新人引导显示===============

/// 获取用户0元购资格
@riverpod
Future<ValidFreeBuy?> fetchValidFreeBuy(FetchValidFreeBuyRef ref) async {
  var userData = ref.watch(authProvider);
  if (userData != null) {
    var result = await AccountService.getUserValidFreeBuy();
    if (result.data != null && result.data?.state == 1) {
      /// 启动倒计时
      if (result.data?.expiryDate != null) {
        ref
            .watch(validFreeBuyTimerProvider.notifier)
            .start(result.data!.expiryDate!);

        /// 请求0元购商品列表
        ref.watch(freeBuyEntranceProvider.notifier).fetchViewGoods();
      }
    }
    return result.data;
  }
  return null;
}

/// 资格倒计时
@riverpod
class ValidFreeBuyTimer extends _$ValidFreeBuyTimer {
  Timer? _timer;

  @override
  Duration? build() {
    return null;
  }

  void start(String expireDate) {
    var expire = DateTime.parse(expireDate);
    stop();
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        var difference = expire.difference(DateTime.now());
        if (difference.inSeconds == 0) {
          timer.cancel();
          state = null;
        } else {
          state = difference;
          // String days = "${difference.inDays}天";
          // int hours = difference.inHours % 24;
          // String hourStr = "${hours < 10 ? 0 : ''}$hours时";
          // int minute = difference.inMinutes % 60;
          // String minuteStr = "${minute < 10 ? 0 : ''}$minute分";
          // int seconds = difference.inSeconds % 60;
          // String secondsStr = "${seconds < 10 ? 0 : ''}$seconds秒";
          // state = "$days$hourStr$minuteStr$secondsStr";
        }
      },
    );
  }

  void stop() {
    _timer?.cancel();
  }
}

/// 0元购入口商品显示
@riverpod
class FreeBuyEntrance extends _$FreeBuyEntrance {
  @override
  List<FreeBuyGoodItem?>? build() {
    return null;
  }

  void fetchViewGoods() async {
    var result = await AccountService.getUserFreeBuyGoods(1, 3);
    var data = result.data?.data;
    if (data != null && data.length > 3) {
      data = data.take(3).toList();
    }
    state = data;
    if (data != null && data.isNotEmpty) {
      ref.read(freeBuyTutorialProvider.notifier).showTitleTutorial(true);
    }
  }
}

/// 0元购商品列表
/// 页长
const int pageSize = 10;

class FreeBuyGoodsResult {
  final int pageNo;
  final List<FreeBuyGoodItem?>? goodsList;
  final LoadState? loadState;

  const FreeBuyGoodsResult({
    this.pageNo = 1,
    this.goodsList,
    this.loadState,
  });

  FreeBuyGoodsResult copyWith({
    int? page,
    List<FreeBuyGoodItem?>? goodsList,
    LoadState? loadState,
  }) {
    return FreeBuyGoodsResult(
      pageNo: page ?? pageNo,
      goodsList: goodsList ?? this.goodsList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class FreeBuyGoodsList extends _$FreeBuyGoodsList {
  @override
  FreeBuyGoodsResult build() {
    state = const FreeBuyGoodsResult();
    loadData();
    return state;
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await AccountService.getUserFreeBuyGoods(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        goodsList: result.data?.data ?? [],
        loadState: result.data?.data?.isNotEmpty == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await AccountService.getUserFreeBuyGoods(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          goodsList: [...?state.goodsList, ...?result.data?.data],
          loadState: result.data?.data?.isNotEmpty == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
