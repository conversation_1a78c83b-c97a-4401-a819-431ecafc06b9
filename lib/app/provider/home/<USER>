// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$homeHash() => r'eba7e5223ccf78329bea7c2eb7cc7ea681af80f7';

/// See also [Home].
@ProviderFor(Home)
final homeProvider = AutoDisposeNotifierProvider<Home, HomeController>.internal(
  Home.new,
  name: r'homeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$homeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Home = AutoDisposeNotifier<HomeController>;
String _$convertContentHash() => r'37a4d43d7555dd0fa3df34ab753f328ceb03035e';

/// 识别弹窗
///
/// Copied from [ConvertContent].
@ProviderFor(ConvertContent)
final convertContentProvider =
    AutoDisposeNotifierProvider<ConvertContent, ConvertGoods?>.internal(
  ConvertContent.new,
  name: r'convertContentProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$convertContentHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConvertContent = AutoDisposeNotifier<ConvertGoods?>;
String _$newestUserInfoHash() => r'bf0e01241cf82dc84dc2bbdbec3da15a84e4451a';

/// 获取最新用户信息
///
/// Copied from [NewestUserInfo].
@ProviderFor(NewestUserInfo)
final newestUserInfoProvider =
    AutoDisposeNotifierProvider<NewestUserInfo, void>.internal(
  NewestUserInfo.new,
  name: r'newestUserInfoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$newestUserInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$NewestUserInfo = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
