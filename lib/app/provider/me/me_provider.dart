import 'dart:async';

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/navigation/coosea.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/account/my_number.dart';
import 'package:msmds_platform/app/repository/modals/account/wallet.dart';
import 'package:msmds_platform/app/repository/modals/order/order.dart';
import 'package:msmds_platform/app/repository/service/account_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../utils/toast_util.dart';
import '../../repository/modals/config/icon_config.dart';
import '../../repository/service/config_service.dart';

part 'me_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: me_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/6 11:11
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/6 11:11
/// @UpdateRemark: 更新说明

// 积分，红包、收藏、足迹数量显示
@riverpod
class MyNumberShowing extends _$MyNumberShowing {
  @override
  MyNumber? build() {
    getNumber();
    return null;
  }

  void getNumber() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await AccountService.myNumber();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

// 收藏数量显示
@riverpod
class CollectCountShowing extends _$CollectCountShowing {
  @override
  num? build() {
    getCollectCount();
    return null;
  }

  void getCollectCount() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await AccountService.collectCount();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

// 足迹数量显示
@riverpod
class BrowsingCountShowing extends _$BrowsingCountShowing {
  @override
  num? build() {
    getBrowsingCount();
    return null;
  }

  void getBrowsingCount() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await AccountService.browsingCount();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

/// 获取钱包信息
@riverpod
class WalletInfo extends _$WalletInfo {
  @override
  Wallet? build() {
    loadWalletInfo();
    return null;
  }

  /// 加载钱包信息
  void loadWalletInfo() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await AccountService.getUserWallet();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

/// 获取我的页面最新订单
@riverpod
Future<Order?> fetchNewestOrder(FetchNewestOrderRef ref) async {
  var userData = ref.watch(authProvider);
  if (userData != null) {
    var result = await AccountService.getNewestOrder();
    return result.data;
  }
  return null;
}

/// 获取轮播配置
@riverpod
Future<List<IconConfig>?> fetchMeSwiperConfig(
  FetchMeSwiperConfigRef ref,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(20, packageInfo.version);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

/// 获取必备工具配置
@riverpod
Future<List<IconConfig>?> fetchMeToolConfig(
  FetchMeToolConfigRef ref,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(11, packageInfo.version);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

/// 注销账户
@riverpod
class CancelAccount extends _$CancelAccount {
  @override
  bool? build() {
    return null;
  }

  /// 注销
  void cancel() async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await AccountService.cancelAccount();
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ref.read(authProvider.notifier).logout();
      navigatorKey.currentState?.popUntil(
        (route) =>
            route.settings.name == CsRouter.home || route.settings.name == "/",
      );
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

/// ====================绑定手机号=================///
/// 手机号输入
@riverpod
class BindPhoneEditing extends _$BindPhoneEditing {
  @override
  String? build() {
    return null;
  }

  void setPhone(String? phone) {
    state = phone;
  }
}

/// 验证码输入
@riverpod
class BindPhoneVerificationEditing extends _$BindPhoneVerificationEditing {
  @override
  String? build() {
    return null;
  }

  void setVerification(String? code) {
    state = code;
  }
}

/// 验证码倒计时
@riverpod
class VerCodeTimer extends _$VerCodeTimer {
  Timer? _timer;

  @override
  int build() {
    return 0;
  }

  void start() {
    int ms = 60;
    stop();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (ms == 0) {
        timer.cancel();
      } else {
        ms--;
      }
      state = ms;
    });
  }

  void stop() {
    _timer?.cancel();
  }
}

/// 绑定手机
@riverpod
class BindPhone extends _$BindPhone {
  @override
  void build() {
    return;
  }

  /// 获取验证码
  void getVerification() async {
    var phone = ref.read(bindPhoneEditingProvider);
    if (phone != null && phone.isNotEmpty) {
      SmartDialog.showLoading(msg: "加载中...");
      var result = await AccountService.getBindPhoneVerCode(phone);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        /// 开启倒计时
        ref.read(verCodeTimerProvider.notifier).start();
        ToastUtil.showToast("发送成功, 请注意查收");
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }

  /// 绑定手机
  void bindPhone() async {
    var phone = ref.read(bindPhoneEditingProvider);
    var code = ref.read(bindPhoneVerificationEditingProvider);
    if (phone != null && phone.isNotEmpty && code != null && code.isNotEmpty) {
      SmartDialog.showLoading(msg: "加载中...");
      var result = await AccountService.bindPhone(phone, code);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        ToastUtil.showToast("绑定成功");
        SmartDialog.dismiss(tag: "bind_phone_dialog");

        /// 刷新用户信息
        // ref.refresh(fetchAccountDetailProvider).unwrapPrevious();
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }
}

/// ====================绑定手机号=================///
