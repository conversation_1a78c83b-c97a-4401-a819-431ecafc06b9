// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'me_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchNewestOrderHash() => r'47bd7462593459115ce8e1dd7aa91df7ece029f8';

/// 获取我的页面最新订单
///
/// Copied from [fetchNewestOrder].
@ProviderFor(fetchNewestOrder)
final fetchNewestOrderProvider = AutoDisposeFutureProvider<Order?>.internal(
  fetchNewestOrder,
  name: r'fetchNewestOrderProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchNewestOrderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchNewestOrderRef = AutoDisposeFutureProviderRef<Order?>;
String _$fetchMeSwiperConfigHash() =>
    r'846d775ae4772264734b6bbf1f6702d5542c40e6';

/// 获取轮播配置
///
/// Copied from [fetchMeSwiperConfig].
@ProviderFor(fetchMeSwiperConfig)
final fetchMeSwiperConfigProvider =
    AutoDisposeFutureProvider<List<IconConfig>?>.internal(
  fetchMeSwiperConfig,
  name: r'fetchMeSwiperConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMeSwiperConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMeSwiperConfigRef
    = AutoDisposeFutureProviderRef<List<IconConfig>?>;
String _$fetchMeToolConfigHash() => r'b797ad88b744af2c0f06c7a543a2ea5f10334a2e';

/// 获取必备工具配置
///
/// Copied from [fetchMeToolConfig].
@ProviderFor(fetchMeToolConfig)
final fetchMeToolConfigProvider =
    AutoDisposeFutureProvider<List<IconConfig>?>.internal(
  fetchMeToolConfig,
  name: r'fetchMeToolConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMeToolConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMeToolConfigRef = AutoDisposeFutureProviderRef<List<IconConfig>?>;
String _$myNumberShowingHash() => r'90d47f7e44a8ad9a87b3eb6661a45fab50239c9a';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: me_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/6 11:11
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/6 11:11
/// @UpdateRemark: 更新说明
///
/// Copied from [MyNumberShowing].
@ProviderFor(MyNumberShowing)
final myNumberShowingProvider =
    AutoDisposeNotifierProvider<MyNumberShowing, MyNumber?>.internal(
  MyNumberShowing.new,
  name: r'myNumberShowingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$myNumberShowingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MyNumberShowing = AutoDisposeNotifier<MyNumber?>;
String _$collectCountShowingHash() =>
    r'2c47d12302b334e2eb65ec21b29a1c9e71fe99e6';

/// See also [CollectCountShowing].
@ProviderFor(CollectCountShowing)
final collectCountShowingProvider =
    AutoDisposeNotifierProvider<CollectCountShowing, num?>.internal(
  CollectCountShowing.new,
  name: r'collectCountShowingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$collectCountShowingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CollectCountShowing = AutoDisposeNotifier<num?>;
String _$browsingCountShowingHash() =>
    r'6e0715b5039af8a0954fab537577e8782d08711b';

/// See also [BrowsingCountShowing].
@ProviderFor(BrowsingCountShowing)
final browsingCountShowingProvider =
    AutoDisposeNotifierProvider<BrowsingCountShowing, num?>.internal(
  BrowsingCountShowing.new,
  name: r'browsingCountShowingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browsingCountShowingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowsingCountShowing = AutoDisposeNotifier<num?>;
String _$walletInfoHash() => r'9cacb37e3d0b7e38773a55055a791c1bc6e7f150';

/// 获取钱包信息
///
/// Copied from [WalletInfo].
@ProviderFor(WalletInfo)
final walletInfoProvider =
    AutoDisposeNotifierProvider<WalletInfo, Wallet?>.internal(
  WalletInfo.new,
  name: r'walletInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$walletInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$WalletInfo = AutoDisposeNotifier<Wallet?>;
String _$cancelAccountHash() => r'ab9a57f5292ab120acedee9d25b4657bd6103172';

/// 注销账户
///
/// Copied from [CancelAccount].
@ProviderFor(CancelAccount)
final cancelAccountProvider =
    AutoDisposeNotifierProvider<CancelAccount, bool?>.internal(
  CancelAccount.new,
  name: r'cancelAccountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cancelAccountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CancelAccount = AutoDisposeNotifier<bool?>;
String _$bindPhoneEditingHash() => r'549bda0e7dfaf02760fd0cca98dda7a6fa275070';

/// ====================绑定手机号=================///
/// 手机号输入
///
/// Copied from [BindPhoneEditing].
@ProviderFor(BindPhoneEditing)
final bindPhoneEditingProvider =
    AutoDisposeNotifierProvider<BindPhoneEditing, String?>.internal(
  BindPhoneEditing.new,
  name: r'bindPhoneEditingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bindPhoneEditingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BindPhoneEditing = AutoDisposeNotifier<String?>;
String _$bindPhoneVerificationEditingHash() =>
    r'058f2fac3ee4830872f2987034f5d05a103b3aaf';

/// 验证码输入
///
/// Copied from [BindPhoneVerificationEditing].
@ProviderFor(BindPhoneVerificationEditing)
final bindPhoneVerificationEditingProvider =
    AutoDisposeNotifierProvider<BindPhoneVerificationEditing, String?>.internal(
  BindPhoneVerificationEditing.new,
  name: r'bindPhoneVerificationEditingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$bindPhoneVerificationEditingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BindPhoneVerificationEditing = AutoDisposeNotifier<String?>;
String _$verCodeTimerHash() => r'45bd9867ecc58882348614c3192e1d1e0af76966';

/// 验证码倒计时
///
/// Copied from [VerCodeTimer].
@ProviderFor(VerCodeTimer)
final verCodeTimerProvider =
    AutoDisposeNotifierProvider<VerCodeTimer, int>.internal(
  VerCodeTimer.new,
  name: r'verCodeTimerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$verCodeTimerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VerCodeTimer = AutoDisposeNotifier<int>;
String _$bindPhoneHash() => r'9fd1fbb032c105377aa90c1e171f36161a3b5f09';

/// 绑定手机
///
/// Copied from [BindPhone].
@ProviderFor(BindPhone)
final bindPhoneProvider = AutoDisposeNotifierProvider<BindPhone, void>.internal(
  BindPhone.new,
  name: r'bindPhoneProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$bindPhoneHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BindPhone = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
