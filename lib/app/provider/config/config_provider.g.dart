// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMainSwiperConfigHash() =>
    r'26eecd6d773a4f7d2b0d7b08dd8b2c5c00683f57';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: config_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/8 11:30
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/8 11:30
/// @UpdateRemark: 更新说明
/// 获取首页轮播配置
///
/// Copied from [fetchMainSwiperConfig].
@ProviderFor(fetchMainSwiperConfig)
final fetchMainSwiperConfigProvider =
    AutoDisposeFutureProvider<List<IconConfig>?>.internal(
  fetchMainSwiperConfig,
  name: r'fetchMainSwiperConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMainSwiperConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMainSwiperConfigRef
    = AutoDisposeFutureProviderRef<List<IconConfig>?>;
String _$fetchMainIconConfigHash() =>
    r'3d8bf5e0e422aeaf2a176761c49d9ba06033bf88';

/// 获取首页icon配置
///
/// Copied from [fetchMainIconConfig].
@ProviderFor(fetchMainIconConfig)
final fetchMainIconConfigProvider =
    AutoDisposeFutureProvider<List<List<IconConfig>>?>.internal(
  fetchMainIconConfig,
  name: r'fetchMainIconConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMainIconConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMainIconConfigRef
    = AutoDisposeFutureProviderRef<List<List<IconConfig>>?>;
String _$fetchMainPorcelainConfigHash() =>
    r'c732db6687c9bbb89252b0819ff446aa3953cee4';

/// 获取首页瓷片区配置
///
/// Copied from [fetchMainPorcelainConfig].
@ProviderFor(fetchMainPorcelainConfig)
final fetchMainPorcelainConfigProvider =
    AutoDisposeFutureProvider<List<IconConfig>?>.internal(
  fetchMainPorcelainConfig,
  name: r'fetchMainPorcelainConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMainPorcelainConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMainPorcelainConfigRef
    = AutoDisposeFutureProviderRef<List<IconConfig>?>;
String _$fetchMainPkgConfigHash() =>
    r'052c896607aafbd01bac2b1f63e6eccd0a9f1a98';

/// 获取首页Tab商品包配置
///
/// Copied from [fetchMainPkgConfig].
@ProviderFor(fetchMainPkgConfig)
final fetchMainPkgConfigProvider =
    AutoDisposeFutureProvider<List<HomeTabPkg>?>.internal(
  fetchMainPkgConfig,
  name: r'fetchMainPkgConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMainPkgConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchMainPkgConfigRef = AutoDisposeFutureProviderRef<List<HomeTabPkg>?>;
String _$mainSidebarConfigHash() => r'4cf735fa9177bf22053f2ef6fa8a8658ed20e44f';

/// 获取首页侧边拦区配置
///
/// Copied from [MainSidebarConfig].
@ProviderFor(MainSidebarConfig)
final mainSidebarConfigProvider =
    AutoDisposeNotifierProvider<MainSidebarConfig, List<IconConfig>?>.internal(
  MainSidebarConfig.new,
  name: r'mainSidebarConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mainSidebarConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MainSidebarConfig = AutoDisposeNotifier<List<IconConfig>?>;
String _$homeTabPkgGoodsHash() => r'795a3b42fd81e0e6756c0bf57b47494555f4957c';

/// See also [HomeTabPkgGoods].
@ProviderFor(HomeTabPkgGoods)
final homeTabPkgGoodsProvider =
    AutoDisposeNotifierProvider<HomeTabPkgGoods, HomeTabPkgData>.internal(
  HomeTabPkgGoods.new,
  name: r'homeTabPkgGoodsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$homeTabPkgGoodsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HomeTabPkgGoods = AutoDisposeNotifier<HomeTabPkgData>;
String _$configItemClickHash() => r'17099a76a423c99c77693930bfa5179cb82c5241';

/// 首页tab商品包加载
/// 自定义跳转
///
/// type 1 内部跳转                  配置app页面地址
/// type 2 跳H5页面                  配置h5 url
/// type 3 SDK跳转淘宝               配置淘宝url
/// type 4 SDK跳转京东               配置京东url
/// type 5 微信分享
/// type 6 跳小程序                  配置小程序 miniId 和 path
/// type 7 内部跳转-动态配置参数       配置app页面地址+参数
/// type 8 内部广播                  配置广播名+参数
/// type 10 淘宝跳转链接添加转链       配置活动id
/// type 11 京东跳转链接添加转链       配置活动id
/// type 12 饿了么买菜专用
/// type 13 用户反馈
/// type 14 必应鸟
/// type 15 优惠雷达
/// type 16 Linking跳转
/// type 17 空
/// type 18 唯品会跳转
/// type 19 兔小巢
/// type 20 美团联盟跳转 配置活动id
/// type 21 饿了么外卖
/// typp 22 苏宁
/// type 23 多多金宝渠道推广
/// type 24 SDK跳转京喜               配置京喜url
/// type 25 先京东转链再SDK跳转京喜
/// type 26 美团联盟跳转 配置活动id
/// type 27 跳转权限设置
/// type 28 跳拼多多商城
/// type 32 跳转应用市场
/// type 33 滴滴转链，跳转小程序
/// type 34 美团活动转链，跳转美团
/// type 35 跳转活动转链(多麦，聚推客)
/// type 36 拼多多活动链接转链接         配置活动url
/// type 37 抖音活动链接转链接         配置活动activityId， mixActivityId：可推广的运营自建活动页活动id；activityId为3时，该项必填
/// type 38 活动转链跳转小程序(多麦，聚推客)
///
/// Copied from [ConfigItemClick].
@ProviderFor(ConfigItemClick)
final configItemClickProvider =
    AutoDisposeNotifierProvider<ConfigItemClick, void>.internal(
  ConfigItemClick.new,
  name: r'configItemClickProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$configItemClickHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConfigItemClick = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
