// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'splash_ad_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$splashAdConfigHash() => r'c2c763d14728667188a15a0ae7cfde8bcc8f29c8';

/// See also [SplashAdConfig].
@ProviderFor(SplashAdConfig)
final splashAdConfigProvider =
    AutoDisposeNotifierProvider<SplashAdConfig, SplashAdData?>.internal(
  SplashAdConfig.new,
  name: r'splashAdConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$splashAdConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SplashAdConfig = AutoDisposeNotifier<SplashAdData?>;
String _$splashAdCountdownHash() => r'4d6205ef62c02541666ee978b8aafff30a4856d1';

/// See also [SplashAdCountdown].
@ProviderFor(SplashAdCountdown)
final splashAdCountdownProvider =
    AutoDisposeNotifierProvider<SplashAdCountdown, int?>.internal(
  SplashAdCountdown.new,
  name: r'splashAdCountdownProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$splashAdCountdownHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SplashAdCountdown = AutoDisposeNotifier<int?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
