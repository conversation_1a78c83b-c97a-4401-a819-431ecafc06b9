import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../repository/service/config_service.dart';

part 'splash_ad_provider.g.dart';

// Copyright (C), 2021-2025, <PERSON><PERSON>
// @ProjectName: msmds_platform
// @Package: app.provider.config
// @ClassName: splash_ad_provider
// @Description:
// @Author: frankylee
// @CreateDate: 2025/8/4 11:43
// @UpdateUser: frankylee
// @UpdateData: 2025/8/4 11:43
// @UpdateRemark: 更新说明

/// 获取开屏广告配置信息
class SplashAdData {
  int? state;
  int? buttonPosition;
  int? templateType;
  String? styleData;
  int? expireTime;
  String? configData;
  String? picUrl;

  SplashAdData({
    this.state,
    this.buttonPosition,
    this.templateType,
    this.styleData,
    this.expireTime,
    this.configData,
    this.picUrl,
  });
}

@riverpod
class SplashAdConfig extends _$SplashAdConfig {
  @override
  SplashAdData? build() {
    return null;
  }

  Future<bool> fetchSplash(Function() onDown) async {
    try {
      return await Future.any([
        fetchAdData(onDown), // 加载广告数据的Future
        Future.delayed(const Duration(seconds: 3), () {
          return false;
        }), // 超时保护
      ]);
    } catch (e) {
      debugPrint("广告加载异常：$e");
      return false;
    }
  }

  Future<bool> fetchAdData(Function() onDown) async {
    try {
      var userData = ref.read(authProvider);
      debugPrint("userData: $userData");
      if (userData == null) return false;
      var result = await ConfigService.getSplashAd();
      if (result.status == Status.completed) {
        var adState = result.data["state"];
        var buttonPosition = result.data["buttonPosition"];
        var templateType = result.data["templateType"];
        var styleData = result.data["styleData"];
        var expireTime = result.data["expireTime"];
        var configData = result.data["configData"];
        var picUrl = result.data["picUrl"];
        debugPrint("state: $adState, "
            "buttonPosition: $buttonPosition, "
            "templateType: $templateType, "
            "styleData: $styleData, "
            "expireTime: $expireTime, "
            "configData: $configData, picUrl: $picUrl");
        if (adState == 1 && templateType == 2) {
          state = SplashAdData(
            state: adState,
            buttonPosition: buttonPosition,
            templateType: templateType,
            styleData: styleData,
            expireTime: expireTime,
            configData: configData,
            picUrl: picUrl,
          );
          ref
              .read(splashAdCountdownProvider.notifier)
              .startCountdown(expireTime, onDown);
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}

@riverpod
class SplashAdCountdown extends _$SplashAdCountdown {
  Timer? _timer;

  @override
  int? build() {
    return null;
  }

  void startCountdown(int value, Function() onDown) {
    int seconds = value;
    state = value;
    if (_timer != null) {
      _timer!.cancel();
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (seconds == 0) {
        timer.cancel();
        onDown.call();
      } else {
        state = seconds--;
      }
    });
  }

  void cleanCountdown(Function() onClean) {
    if (_timer != null) {
      _timer!.cancel();
    }
    onClean.call();
  }

  void adClick(Function() onAdClick) {
    if (_timer != null) {
      _timer!.cancel();
    }
    onAdClick.call();
  }
}
