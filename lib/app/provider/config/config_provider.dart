import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:msmds_platform/plugin/alibc/alibc_ohos_plugin.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/provider/home/<USER>';
import 'package:msmds_platform/app/repository/modals/config/icon_config.dart';
import 'package:msmds_platform/app/repository/service/chain_transfer_service.dart';
import 'package:msmds_platform/app/repository/service/config_service.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../plugin/wechat/src/fluwx.dart';
import '../../../plugin/wechat/src/foundation/arguments.dart';
import '../../../utils/toast_util.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../dialog/tb_auth_dialog.dart';
import '../../repository/modals/config/home_tab_pkg.dart';
import '../../repository/modals/goods/goods_tb_pkg.dart';
import '../conversion/link_conversion_provider.dart';

part 'config_provider.g.dart';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: config_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/8 11:30
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/8 11:30
/// @UpdateRemark: 更新说明

/// 获取首页轮播配置
@riverpod
Future<List<IconConfig>?> fetchMainSwiperConfig(
  FetchMainSwiperConfigRef ref,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(15, packageInfo.version);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

/// 获取首页icon配置
@riverpod
Future<List<List<IconConfig>>?> fetchMainIconConfig(
  FetchMainIconConfigRef ref,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(4, packageInfo.version);
  if (result.status == Status.completed) {
    /// 10个icon为一组
    var configs = result.data ?? [];
    if (configs.isNotEmpty) {
      List<List<IconConfig>> data = [];
      for (int i = 0; i < configs.length; i += 10) {
        int start = i;
        int end = i + 10;
        if (end > configs.length) {
          end = configs.length;
        }
        var item = configs.getRange(start, end).toList();
        data.add(item);
      }
      return data;
    }
  }
  return null;
}

/// 获取首页瓷片区配置
@riverpod
Future<List<IconConfig>?> fetchMainPorcelainConfig(
  FetchMainPorcelainConfigRef ref,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(63, packageInfo.version);
  return result.data;
}

/// 获取首页侧边拦区配置
@riverpod
class MainSidebarConfig extends _$MainSidebarConfig {
  @override
  List<IconConfig>? build() {
    fetchMainSidebar();
    return null;
  }

  void fetchMainSidebar() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var result = await ConfigService.getIconConfig(3, packageInfo.version);
    if (result.status == Status.completed) {
      state = result.data;
    }
  }

  // 关闭
  void closeSidebar(IconConfig config) {
    var data = state;
    if (data != null && data.isNotEmpty) {
      data.removeWhere(
          (element) => element.advertisingId == config.advertisingId);
      state = [...data];
    }
  }
}

/// 获取首页Tab商品包配置
@riverpod
Future<List<HomeTabPkg>?> fetchMainPkgConfig(
  FetchMainPkgConfigRef ref,
) async {
  var result = await ConfigService.getHomeTabPkg();
  if (result.status == Status.completed) {
    var data = result.data;
    if (data != null && data.isNotEmpty) {
      ref.watch(homeTabPkgGoodsProvider.notifier).setCurrentHomeTab(data.first);
    }
  }
  return result.data;
}

/// 首页tab商品包加载
const int pageSize = 10;

class HomeTabPkgData {
  final HomeTabPkg? homeTabPkg;
  final List<GoodsTbPkg?>? goods;
  final int pageNo;
  final LoadState? loadState;

  const HomeTabPkgData({
    this.pageNo = 1,
    this.goods,
    this.homeTabPkg,
    this.loadState,
  });

  HomeTabPkgData copyWith({
    int? pageNo,
    HomeTabPkg? homeTabPkg,
    List<GoodsTbPkg?>? goods,
    LoadState? loadState,
  }) {
    return HomeTabPkgData(
      pageNo: pageNo ?? this.pageNo,
      homeTabPkg: homeTabPkg ?? this.homeTabPkg,
      goods: goods ?? this.goods,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class HomeTabPkgGoods extends _$HomeTabPkgGoods {
  @override
  HomeTabPkgData build() {
    state = const HomeTabPkgData();
    return state;
  }

  // 保存商品图片
  void saveGoodsImageToGallery(String? imageUrl) async {
    if (imageUrl != null) {
      var response = await Dio().get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      final result = await ImageGallerySaver.saveImage(
        Uint8List.fromList(response.data),
        quality: 60,
      );
      ToastUtil.showToast(result["isSuccess"] == true ? "保存成功" : "保存失败");
    }
  }

  // 设置当前商品包
  void setCurrentHomeTab(HomeTabPkg pkg) {
    state = state.copyWith(homeTabPkg: pkg);
    loadData();
  }

  /// 加载数据
  void loadData() async {
    state = state.copyWith(
      pageNo: 1,
      loadState: null,
    );
    var result = await GoodsService.getGoodsByPkg(
      state.pageNo,
      pageSize,
      state.homeTabPkg?.pkgId,
    );
    if (result.status == Status.completed) {
      debugPrint("result.data?.hasNextPage: ${result.data?.length}");
      state = state.copyWith(
        goods: result.data ?? [],
        loadState: result.data != null && result.data!.isNotEmpty
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      pageNo: state.pageNo + 1,
    );
    var result = await GoodsService.getGoodsByPkg(
      state.pageNo,
      pageSize,
      state.homeTabPkg?.pkgId,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        debugPrint("result.data?.hasNextPage: ${result.data?.length}");
        state = state.copyWith(
          goods: [...?state.goods, ...?result.data],
          loadState: result.data != null && result.data!.isNotEmpty
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

/// 首页tab商品包加载

/// 自定义跳转
///
/// type 1 内部跳转                  配置app页面地址
/// type 2 跳H5页面                  配置h5 url
/// type 3 SDK跳转淘宝               配置淘宝url
/// type 4 SDK跳转京东               配置京东url
/// type 5 微信分享
/// type 6 跳小程序                  配置小程序 miniId 和 path
/// type 7 内部跳转-动态配置参数       配置app页面地址+参数
/// type 8 内部广播                  配置广播名+参数
/// type 10 淘宝跳转链接添加转链       配置活动id
/// type 11 京东跳转链接添加转链       配置活动id
/// type 12 饿了么买菜专用
/// type 13 用户反馈
/// type 14 必应鸟
/// type 15 优惠雷达
/// type 16 Linking跳转
/// type 17 空
/// type 18 唯品会跳转
/// type 19 兔小巢
/// type 20 美团联盟跳转 配置活动id
/// type 21 饿了么外卖
/// typp 22 苏宁
/// type 23 多多金宝渠道推广
/// type 24 SDK跳转京喜               配置京喜url
/// type 25 先京东转链再SDK跳转京喜
/// type 26 美团联盟跳转 配置活动id
/// type 27 跳转权限设置
/// type 28 跳拼多多商城
/// type 32 跳转应用市场
/// type 33 滴滴转链，跳转小程序
/// type 34 美团活动转链，跳转美团
/// type 35 跳转活动转链(多麦，聚推客)
/// type 36 拼多多活动链接转链接         配置活动url
/// type 37 抖音活动链接转链接         配置活动activityId， mixActivityId：可推广的运营自建活动页活动id；activityId为3时，该项必填
/// type 38 活动转链跳转小程序(多麦，聚推客)
@riverpod
class ConfigItemClick extends _$ConfigItemClick {
  @override
  void build() {
    return;
  }

  // url Params
  String _addParamsToUrl(String url, Map<String, String?> params) {
    final uri = Uri.parse(url);
    final updatedUri = uri.replace(
      queryParameters: {
        ...uri.queryParameters, // 保留原有的参数
        ...params, // 添加新的参数
      },
    );
    return updatedUri.toString();
  }

  /// 云配icon等的点击
  void configItemClick(BuildContext context, IconConfig config) {
    debugPrint("configItemClick-config: $config");
    try {
      var typeData = jsonDecode(config.typeData ?? "");
      var type = typeData["jump"]["type"];
      debugPrint("configItemClick: $type");
      action(type, context, config)?.call();
    } catch (e) {
      debugPrint("configItemClick-e: $e");
    }
  }

  Function()? action(dynamic type, BuildContext context, IconConfig config) {
    final actionMap = {
      // 跳h5
      2: () => h5Jump(context, config),
      // SDK跳转淘宝，配置淘宝链接
      3: () => sdkJumpTaoBao(context, config),
      // SDK跳转京东，配置淘宝链接
      4: () => sdkJumpJinDong(context, config),
      // 内部页面，内部的页面目前开发较少，暂时不配置
      1: () => innerJump(context, config),
      7: () => innerJump(context, config),
      // 淘宝活动
      10: () => tbActivity(context, config),
      // 京东活动
      11: () => jdActivity(context, config),
      // 饿了么买菜专用
      12: () => eleRetail(context, config),
      // 生活券，必应鸟
      14: () => lifeCoupon(context, config),
      // 优惠雷达
      15: () => discountRadar(context, config),
      // 唯品会app跳转 (type == 1 通过url获取url type==2 通过商品ID获取url)
      18: () => vipShopActivity(context, config),
      // 饿了么转链，跳饿了么小程序
      21: () => elePromotion(context, config),
      // 美团活动，跳美团小程序
      26: () => mtActivity(context, config),
      // 滴滴转链，跳转滴滴小程序
      33: () => didiGenerateLink(context, config),
      // H5活动,多麦、聚推客等
      35: () => h5Activity(context, config),
    };

    return actionMap[type];
  }

  // 跳转h5
  void h5Jump(BuildContext context, IconConfig config) {
    var userData = ref.read(authProvider);
    var typeData = jsonDecode(config.typeData ?? "");
    var carrayUserInfo = typeData["jump"]["carrayUserInfo"];
    var url = config.jumpUrl ?? "";
    if (carrayUserInfo == true) {
      if (userData == null) {
        Navigator.pushNamed(context, CsRouter.login);
        return;
      }
      url = _addParamsToUrl(url, {"phone": userData.data?.encryptionPhone});
    }
    Navigator.pushNamed(
      context,
      CsRouter.webPage,
      arguments: ["", url],
    );
  }

  // SDK跳转淘宝
  void sdkJumpTaoBao(BuildContext context, IconConfig config) {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;

    AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
    aliPlugin.launcherTbByUrl(config.jumpUrl!);
  }

  // SDK跳转京东
  void sdkJumpJinDong(BuildContext context, IconConfig config) {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;

    var url = config.jumpUrl!;
    var data = jsonEncode({"category": "jump", "des": "m", "url": url});
    var schemeUrl =
        "openapp.jdmobile://virtual?params=${Uri.encodeComponent(data)}";
    ref.read(launchAppProvider.notifier).launchApp(schemeUrl, url);
  }

  // 跳转内部页面
  void innerJump(BuildContext context, IconConfig config) {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == "SignInPage") {
      // 跳转签到
      ref.read(homeProvider.notifier).jumpToPage(1);
    } else {
      if (config.jumpUrl != null) {
        Navigator.pushNamed(context, config.jumpUrl!);
      }
    }
  }

  // 淘宝活动跳转
  void tbActivity(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;
    SmartDialog.showLoading(msg: "跳转中...");
    var result = await ChainTransferService.getTbActivityInfo(config.jumpUrl!);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var jsonData = result.data;
        debugPrint("tbActivity-jsonData: $jsonData");
        if (jsonData != null) {
          AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
          aliPlugin.launcherTbByUrl(jsonData);
        }
      } catch (e) {
        debugPrint("tbActivity-e: $e");
      }
    } else {
      if (result.exception?.getCode() == 5006) {
        // 唤起授权弹窗
        TbAuthDialog.authDialog(() {
          ref.read(tbConversionProvider.notifier).launchTbAuth();
        });
      }
    }
  }

  // 京东活动跳转
  void jdActivity(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;
    SmartDialog.showLoading(msg: "跳转中...");
    var result =
        await ChainTransferService.getJinDonActivityInfo(config.jumpUrl!);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var jsonData = result.data;
        debugPrint("jdActivity-jsonData: $jsonData");
        if (jsonData != null) {
          var url = jsonData;
          var data = jsonEncode({"category": "jump", "des": "m", "url": url});
          var schemeUrl =
              "openapp.jdmobile://virtual?params=${Uri.encodeComponent(data)}";
          ref.read(launchAppProvider.notifier).launchApp(schemeUrl, url);
        }
      } catch (e) {
        debugPrint("tbActivity-e: $e");
      }
    }
  }

  // 饿了么买菜专用
  void eleRetail(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    SmartDialog.showLoading(msg: "跳转中...");
    var result = await ChainTransferService.getNewRetailUrl(1);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var jsonData = result.data;
        debugPrint("eleRetail-jsonData: $jsonData");
        if (jsonData != null) {
          AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
          aliPlugin.launcherTbByUrl(jsonData);
        }
      } catch (e) {
        debugPrint("eleRetail-e: $e");
      }
    } else {
      if (result.exception?.getCode() == 5006) {
        // 唤起授权弹窗
        TbAuthDialog.authDialog(() {
          ref.read(tbConversionProvider.notifier).launchTbAuth();
        });
      }
    }
  }

  // 必应鸟生活券
  void lifeCoupon(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;
    SmartDialog.showLoading(msg: "跳转中...");
    var result = await ChainTransferService.getLifeCouponUrl(config.jumpUrl);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var jsonData = result.data;
        debugPrint("eleRetail-jsonData: $jsonData");
        if (jsonData != null && context.mounted) {
          Navigator.pushNamed(
            context,
            CsRouter.webPage,
            arguments: ["", jsonData],
          );
        }
      } catch (e) {
        debugPrint("eleRetail-e: $e");
      }
    }
  }

  // 优惠雷达
  void discountRadar(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    SmartDialog.showLoading(msg: "跳转中...");
    var result = await ChainTransferService.getDiscountRadarUrl();
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var jsonData = result.data;
        debugPrint("discountRadar-jsonData: $jsonData");
        if (jsonData != null) {
          AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
          aliPlugin.launcherTbByUrl(jsonData);
        }
      } catch (e) {
        debugPrint("discountRadar-e: $e");
      }
    } else {
      if (result.exception?.getCode() == 5006) {
        // 唤起授权弹窗
        TbAuthDialog.authDialog(() {
          ref.read(tbConversionProvider.notifier).launchTbAuth();
        });
      }
    }
  }

  // 唯品会app跳转 (type == 1 通过url获取url type==2 通过商品ID获取url)
  void vipShopActivity(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;

    SmartDialog.showLoading(msg: "跳转中...");

    // 活动数据
    var typeData = jsonDecode(config.typeData ?? "");
    var urlType = typeData["urlType"];
    debugPrint("vipShopActivity-urlType: $urlType");

    ApiResponse? result;
    if (urlType == 1) {
      // 通过配置url获取活动url
      result = await ChainTransferService.wphGetUrlByUrl(config.jumpUrl);
    } else if (urlType == 2) {
      result = await ChainTransferService.wphGetUrlById(
        config.jumpUrl,
        config.jumpUrl,
      );
    }
    SmartDialog.dismiss();
    if (result?.status == Status.completed) {
      try {
        var jsonData = result?.data;
        debugPrint("vipShopActivity-jsonData: $jsonData");
        var deeplinkUrl = jsonData["deeplinkUrl"];
        var longUrl = jsonData["longUrl"];
        ref.read(launchAppProvider.notifier).launchApp(deeplinkUrl, longUrl);
      } catch (e) {
        debugPrint("vipShopActivity-e: $e");
      }
    }
  }

  // 饿了么小程序
  void elePromotion(BuildContext context, IconConfig config) async {
    if (config.jumpUrl == null) return;
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    SmartDialog.showLoading(msg: "跳转中...");
    var result = await ChainTransferService.elePromotionLink(config.jumpUrl!);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var typeData = jsonDecode(config.typeData ?? "");
        var gh_id = typeData["jump"]["gh_id"];
        var jsonData = result.data;
        debugPrint("elePromotion-jsonData: $jsonData");
        debugPrint("elePromotion-gh_id: $gh_id");
        var wxPath = jsonData["wxPath"];
        debugPrint("elePromotion-wxPath: $wxPath");
        if (gh_id != null && wxPath != null) {
          Fluwx fluwx = Fluwx();
          var isWeChatInstalled = await fluwx.isWeChatInstalled;
          if (isWeChatInstalled) {
            fluwx.open(target: MiniProgram(username: gh_id, path: wxPath));
          } else {
            ToastUtil.showToast("微信未安装或版本过低");
          }
        }
      } catch (e) {
        debugPrint("mtActivity-e: $e");
      }
    }
  }

  // 美团活动跳转
  void mtActivity(BuildContext context, IconConfig config) async {
    if (config.jumpUrl == null) return;
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    SmartDialog.showLoading(msg: "跳转中...");
    var result =
        await ChainTransferService.getMtReferralLink(config.jumpUrl!, 4);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      try {
        var typeData = jsonDecode(config.typeData ?? "");
        var gh_id = typeData["jump"]["gh_id"];
        debugPrint("mtActivity-gh_id: $gh_id");
        var jsonData = result.data;
        debugPrint("mtActivity-jsonData: $jsonData");
        if (gh_id != null && jsonData != null && jsonData != "") {
          Fluwx fluwx = Fluwx();
          var isWeChatInstalled = await fluwx.isWeChatInstalled;
          if (isWeChatInstalled) {
            fluwx.open(target: MiniProgram(username: gh_id, path: jsonData));
          } else {
            ToastUtil.showToast("微信未安装或版本过低");
          }
        }
      } catch (e) {
        debugPrint("mtActivity-e: $e");
      }
    }
  }

  // 滴滴转链
  void didiGenerateLink(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;
    // 活动数据
    var typeData = jsonDecode(config.typeData ?? "");
    var promotionId = typeData["jump"]["promotionId"];
    var isMiniApp = typeData["jump"]["isMiniApp"];
    if (promotionId != null) {
      SmartDialog.showLoading(msg: "跳转中...");
      var result = await ChainTransferService.getDiDiActivityInfo(
        config.jumpUrl!,
        isMiniApp,
        promotionId,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        var jsonData = result.data;
        var appData = jsonData["data"];
        var appSource = appData["appSource"];
        var link = appData["link"];
        debugPrint("didiGenerateLink-appSource: $appSource");
        debugPrint("didiGenerateLink-link: $link");
        if (link == null) return;
        if (isMiniApp == true) {
          if (appSource != null) {
            Fluwx fluwx = Fluwx();
            var isWeChatInstalled = await fluwx.isWeChatInstalled;
            debugPrint("didiGenerateLink: $isWeChatInstalled");
            if (isWeChatInstalled) {
              fluwx.open(target: MiniProgram(username: appSource, path: link));
            } else {
              ToastUtil.showToast("微信未安装或版本过低");
            }
          }
        } else {
          // 跳h5
          if (context.mounted) {
            Navigator.pushNamed(
              context,
              CsRouter.webPage,
              arguments: ["", link],
            );
          }
        }
      }
    }
  }

  // h5活动页，多麦、聚推客等
  void h5Activity(BuildContext context, IconConfig config) async {
    var userData = ref.read(authProvider);
    if (userData == null) {
      Navigator.pushNamed(context, CsRouter.login);
      return;
    }
    if (config.jumpUrl == null) return;

    // 活动数据
    var typeData = jsonDecode(config.typeData ?? "");
    var activityType = typeData["jump"]["activityType"];
    var siteId = typeData["jump"]["siteId"];
    debugPrint("h5Activity-activityType: $activityType");
    debugPrint("h5Activity-siteId: $siteId");
    if (activityType == 1) {
      // 多麦
      if (siteId != null) {
        SmartDialog.showLoading(msg: "跳转中...");
        var result = await ChainTransferService.getDuoMaiActivityInfo(
          config.jumpUrl!,
          siteId,
        );
        SmartDialog.dismiss();
        if (result.status == Status.completed) {
          var jsonData = result.data;
          var cpsShort = jsonData["cpsShort"];
          if (context.mounted && cpsShort != null) {
            Navigator.pushNamed(
              context,
              CsRouter.webPage,
              arguments: ["", cpsShort],
            );
          }
        }
      }
    } else if (activityType == 2) {
      // 聚推客
      SmartDialog.showLoading(msg: "跳转中...");
      var result =
          await ChainTransferService.getJuTuiKeActivityInfo(config.jumpUrl!);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        var jsonData = result.data;
        var longH5 = jsonData["longH5"];
        if (context.mounted && longH5 != null) {
          Navigator.pushNamed(
            context,
            CsRouter.webPage,
            arguments: ["", longH5],
          );
        }
      }
    }
  }
}
