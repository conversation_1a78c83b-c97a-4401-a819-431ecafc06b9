import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/repository/modals/account/red_packet.dart';
import 'package:msmds_platform/app/repository/service/account_service.dart';
import 'package:msmds_platform/app/repository/service/chain_transfer_service.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/modals/config/icon_config.dart';
import '../../repository/service/config_service.dart';
import '../conversion/link_conversion_provider.dart';

part 'inner_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.provider.config
/// @ClassName: inner_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/21 15:41
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/21 15:41
/// @UpdateRemark: 更新说明
/// 获取内部唯品会页面的Banner
@riverpod
Future<List<IconConfig>?> fetchVipShopSwiperConfig(
  FetchVipShopSwiperConfigRef ref,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(39, packageInfo.version);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

@riverpod
class SuNingSaving extends _$SuNingSaving {
  @override
  void build() {
    return;
  }

  void goToSuNing() async {
    SmartDialog.showLoading(msg: "跳转中...");
    var result = await ChainTransferService.getSuningShop("channel0");
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      var jsonData = result.data;
      debugPrint("goToSuNing-jsonData: $jsonData");
      var deeplinkUrl = jsonData["deeplinkUrl"];
      var wapExtendUrl = jsonData["wapExtendUrl"];
      ref.read(launchAppProvider.notifier).launchApp(deeplinkUrl, wapExtendUrl);
    }
  }
}

/// ================用户红包相关================
class GiftItem {
  int index;
  int status;
  String name;

  GiftItem(this.index, this.status, this.name);
}

final List<GiftItem> giftTabs = [
  GiftItem(0, 0, "未使用"),
  GiftItem(1, 1, "使用中"),
  GiftItem(2, 3, "已使用"),
  GiftItem(3, 4, "已失效"),
];

/// 页长
const int pageSize = 10;

/// 红包列表结果
class RedPacketResult {
  final int pageNo;
  final GiftItem? giftItem;
  final List<RedPacket?>? redPacketList;
  final LoadState? loadState;

  const RedPacketResult({
    this.pageNo = 1,
    this.giftItem,
    this.redPacketList,
    this.loadState,
  });

  RedPacketResult copyWith({
    int? pageNo,
    GiftItem? giftItem,
    List<RedPacket?>? redPacketList,
    LoadState? loadState,
  }) {
    return RedPacketResult(
      pageNo: pageNo ?? this.pageNo,
      giftItem: giftItem ?? this.giftItem,
      redPacketList: redPacketList ?? this.redPacketList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class RedPacketsList extends _$RedPacketsList {
  @override
  RedPacketResult build() {
    state = RedPacketResult(giftItem: giftTabs.first);
    loadGoods();
    return state;
  }

  /// 切换红包状态
  void changeGiftStatus(GiftItem item) {
    state = state.copyWith(giftItem: item);
    loadGoods();
  }

  /// 加载数据
  void loadGoods() async {
    state = state.copyWith(
      pageNo: 1,
      loadState: null,
    );
    var result = await AccountService.getMyGiftList(
      state.giftItem!.status,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      bool hasNextPage = false;
      var data = result.data?.data;
      var total = result.data?.total;
      if (data != null && data.isNotEmpty && total != null && total != 0) {
        int totalPages = (total / pageSize).ceil(); // 总页数
        hasNextPage = state.pageNo < totalPages;
      }
      state = state.copyWith(
        redPacketList: result.data?.data ?? [],
        loadState: hasNextPage ? LoadState.idle : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      pageNo: state.pageNo + 1,
    );
    var result = await AccountService.getMyGiftList(
      state.giftItem!.status,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        bool hasNextPage = false;
        var data = result.data?.data;
        var total = result.data?.total;
        if (data != null && data.isNotEmpty && total != null && total != 0) {
          int totalPages = (total / pageSize).ceil(); // 总页数
          hasNextPage = state.pageNo < totalPages;
        }
        state = state.copyWith(
          redPacketList: [...?state.redPacketList, ...?result.data?.data],
          loadState: hasNextPage ? LoadState.idle : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
