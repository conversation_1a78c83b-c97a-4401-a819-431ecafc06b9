import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';
import 'package:msmds_platform/app/repository/modals/order/order_tab.dart';
import 'package:msmds_platform/app/repository/service/order_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'order_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 11:43
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 11:43
/// @UpdateRemark: 更新说明

/// filter
enum OrderFilter {
  all(null, "全部"),
  coming(1, "即将到账"),
  received(2, "已到账"),
  expired(3, "已失效");

  final int? status;
  final String name;

  const OrderFilter(this.status, this.name);
}

// /// 订单类型
// enum OrderType {
//   tb(2, "淘宝"),
//   jd(3, "京东"),
//   pdd(4, "拼多多"),
//   ele(7, "美团"),
//   mt(6, "饿了么");
//
//   final int type;
//   final String name;
//
//   const OrderType(this.type, this.name);
// }

/// 订单状态(状态（0：初始，1：待结算，2：已结算，3：退款，4：无效）)
enum OrderState {
  init(0, "初始"),
  paid(1, "待结算"),
  settled(2, "已结算"),
  refund(3, "退款"),
  invalid(4, "无效");

  final int type;
  final String state;

  const OrderState(this.type, this.state);
}

// 获取订单tab列表
@riverpod
class OrderTabList extends _$OrderTabList {
  @override
  List<OrderTab>? build() {
    getOrderTabList();
    return null;
  }

  void getOrderTabList() async {
    var result = await OrderService.getOrderTabList();
    if (result.status == Status.completed) {
      state = result.data;
      if (result.data != null && result.data!.isNotEmpty) {
        ref
            .read(orderListProvider.notifier)
            .selectOrderType(result.data!.first);
      }
    }
  }
}

/// 页长
const int pageSize = 10;

/// 订单列表结果
class OrderResult {
  final int pageNo;
  final OrderTab? orderTab;
  final OrderFilter orderFilter;
  final List<OrderDetail?>? orderList;
  final LoadState? loadState;

  const OrderResult({
    this.pageNo = 1,
    this.orderTab,
    this.orderFilter = OrderFilter.all,
    this.orderList,
    this.loadState,
  });

  OrderResult copyWith({
    int? page,
    OrderTab? orderTab,
    OrderFilter? orderFilter,
    List<OrderDetail?>? orderList,
    LoadState? loadState,
  }) {
    return OrderResult(
      pageNo: page ?? pageNo,
      orderTab: orderTab ?? this.orderTab,
      orderFilter: orderFilter ?? this.orderFilter,
      orderList: orderList ?? this.orderList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class OrderList extends _$OrderList {
  @override
  OrderResult build() {
    state = const OrderResult();
    return state;
  }

  /// 切换订单类型
  void selectOrderType(OrderTab orderTab) {
    state = state.copyWith(orderTab: orderTab);
    loadGoods();
  }

  /// 订单状态筛选
  void filterOrderState(int index) {
    OrderFilter of = OrderFilter.values[index];
    state = state.copyWith(orderFilter: of);
    loadGoods();
  }

  /// 加载数据
  void loadGoods() async {
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await OrderService.listUserOrderTabDetail(
      state.orderTab!.id!,
      state.pageNo,
      pageSize,
      state.orderFilter.status,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        orderList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await OrderService.listUserOrderTabDetail(
      state.orderTab!.id!,
      state.pageNo,
      pageSize,
      state.orderFilter.status,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          orderList: [...?state.orderList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
