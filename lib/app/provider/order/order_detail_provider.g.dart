// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchOrderDetailHash() => r'5a25f3e6f1ae5f0c3f9beff975f0234f65ef2cd2';

/// 查询订单详情
///
/// Copied from [fetchOrderDetail].
@ProviderFor(fetchOrderDetail)
final fetchOrderDetailProvider =
    AutoDisposeFutureProvider<List<OrderDetail>?>.internal(
  fetchOrderDetail,
  name: r'fetchOrderDetailProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchOrderDetailHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchOrderDetailRef = AutoDisposeFutureProviderRef<List<OrderDetail>?>;
String _$currentOrderHash() => r'67c88e54ad6f979dcdfaccab64f86787b0c9b0d3';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_detail_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 14:52
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 14:52
/// @UpdateRemark: 更新说明
/// 当前选中的订单
///
/// Copied from [CurrentOrder].
@ProviderFor(CurrentOrder)
final currentOrderProvider =
    AutoDisposeNotifierProvider<CurrentOrder, String?>.internal(
  CurrentOrder.new,
  name: r'currentOrderProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentOrderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentOrder = AutoDisposeNotifier<String?>;
String _$orderGiftListHash() => r'69dd60b5f6c6bc6d30b8f92c2e1ccb80aa09e7aa';

/// See also [OrderGiftList].
@ProviderFor(OrderGiftList)
final orderGiftListProvider =
    AutoDisposeNotifierProvider<OrderGiftList, OrderGiftItem?>.internal(
  OrderGiftList.new,
  name: r'orderGiftListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$orderGiftListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OrderGiftList = AutoDisposeNotifier<OrderGiftItem?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
