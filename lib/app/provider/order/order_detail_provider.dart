import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/order/order_detail.dart';
import 'package:msmds_platform/app/repository/modals/order/order_gift_item.dart';
import 'package:msmds_platform/app/repository/service/order_service.dart';
import 'package:msmds_platform/app/view/order_detail/dialog/order_gift_dialog.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../navigation/coosea.dart';
import '../../navigation/router.dart';

part 'order_detail_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON>y Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: order_detail_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/7 14:52
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/7 14:52
/// @UpdateRemark: 更新说明

/// 当前选中的订单
@riverpod
class CurrentOrder extends _$CurrentOrder {
  @override
  String? build() {
    return null;
  }

  void setCurrentOrder(String orderNoOrId) {
    state = orderNoOrId;
  }
}

/// 查询订单详情
@riverpod
Future<List<OrderDetail>?> fetchOrderDetail(FetchOrderDetailRef ref) async {
  var id = ref.watch(currentOrderProvider);
  var userData = ref.watch(authProvider);
  if (id != null && userData != null) {
    var result = await OrderService.getUserOrderDetail(orderNo: id);
    return result.data;
  }
  return null;
}

// 获取订单可用的红包列表
@riverpod
class OrderGiftList extends _$OrderGiftList {
  @override
  OrderGiftItem? build() {
    return null;
  }

  // 获取可用红包列表
  void getOrderGiftList(int orderId) async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await OrderService.getOrderCanUseRedPacket(orderId);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      if (result.data != null &&
          result.data!.giftList != null &&
          result.data!.giftList!.isNotEmpty) {
        // 有可用红包, 弹起选择红包弹窗
        OrderGiftDialog.showGiftDialog(result.data?.giftList, orderId);
      } else {
        if (result.data?.type == 1) {
          // 不可积分兑换红包
          OrderGiftDialog.showGiftDialog(result.data?.giftList, orderId);
        } else if (result.data?.type == 2) {
          // 可积分兑换红包, 跳转兑换红包页面
          SmartDialog.dismiss(tag: "order_gift_dialog");
          navigatorKey.currentState?.pushNamed(CsRouter.myIntegralPage);
        }
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  // 选择当前使用的红包
  void selectGift(OrderGiftItem item) {
    state = item;
  }

  // 激活红包
  Future<bool> activeGift(int orderId) async {
    if (state != null && state!.giftId != null) {
      SmartDialog.showLoading(msg: "加载中...");
      var result = await OrderService.activationGift(state!.giftId!, orderId);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        if (result.data == true) {
          ToastUtil.showToast("激活红包成功");
          // 刷新订单信息
          ref.refresh(fetchOrderDetailProvider).unwrapPrevious();
          return true;
        }
        ToastUtil.showToast("红包激活失败");
        return false;
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
        return false;
      }
    } else {
      ToastUtil.showToast("请先选择一个红包");
      return false;
    }
  }
}
