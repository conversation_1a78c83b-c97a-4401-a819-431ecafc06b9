import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/sign/exchange_item.dart';
import 'package:msmds_platform/app/repository/modals/sign/integral_detail_item.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout.dart';
import 'package:msmds_platform/app/repository/modals/sign/sign_layout_ball_item.dart';
import 'package:msmds_platform/app/repository/service/config_service.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/sign_dialog.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/modals/config/back_config.dart';
import '../../repository/modals/sign/sign_record.dart';
import '../../repository/modals/sign/user_integral.dart';

part 'sign_provider.g.dart';

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.sign
/// @ClassName: sign_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/11 10:51
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/11 10:51
/// @UpdateRemark: 更新说明

// 获取签到背景图
@riverpod
Future<BackConfig?> fetchSignBackImg(FetchSignBackImgRef ref) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getSignBackImg(packageInfo.version);
  return result.data;
}

// 用户当前积分
@riverpod
class UserSignIntegral extends _$UserSignIntegral {
  @override
  UserIntegral? build() {
    getUserIntegral();
    return null;
  }

  // 获取用户积分
  void getUserIntegral() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await ConfigService.getUserIntegral();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

// 签到记录
@riverpod
class SignPageRecord extends _$SignPageRecord {
  @override
  SignRecord? build() {
    getSignRecord();
    return null;
  }

  // 获取签到记录
  // 未签到则需要进行签到
  void getSignRecord() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await ConfigService.getSignRecord();
      if (result.status == Status.completed) {
        state = result.data;
        if (result.data?.signModel == null ||
            result.data?.signModel?.haveSign == "false") {
          // 自动签到并唤起签到弹窗
          userSign();
        }
      }
    }
  }

  void userSign() async {
    var result = await ConfigService.userSign();
    if (result.status == Status.completed) {
      // 刷新签到记录
      var recordResult = await ConfigService.getSignRecord();
      if (recordResult.status == Status.completed) {
        state = recordResult.data;
        SignDialog.show(recordResult.data);
      }
    }
  }
}

// 签到配置数据（兑换红包等数据）
@riverpod
class SignLayoutConfig extends _$SignLayoutConfig {
  @override
  SignLayout? build() {
    getSignLayoutConfig();
    return null;
  }

  // 获取签到配置
  void getSignLayoutConfig() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      var result = await ConfigService.getSignLayout(packageInfo.version, 1);
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }
}

// 签到积分球显示
@riverpod
class SignLayoutBall extends _$SignLayoutBall {
  @override
  List<SignLayoutBallItem>? build() {
    getSignLayoutBall();
    return null;
  }

  // 获取签到积分球
  void getSignLayoutBall() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await ConfigService.getSignBalls();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }

  // 点击气泡球领取积分
  void getBallIntegral() async {
    var result = await ConfigService.getIntegralById();
    if (result.status == Status.completed) {
      getSignLayoutBall();
      // 刷新用户积分
      ref.read(userSignIntegralProvider.notifier).getUserIntegral();
    }
  }
}

// 用户兑换列表
@riverpod
class IntegralExchange extends _$IntegralExchange {
  @override
  List<ExchangeItem>? build() {
    getIntegralExchangeList();
    return null;
  }

  // 切换列表
  void changeExchangeTab(int exchangeTab) {
    if (exchangeTab == 1) {
      getVipGoodsList();
    } else {
      getIntegralExchangeList();
    }
  }

  // 获取会员兑换商品列表
  void getVipGoodsList() async {
    var result = await ConfigService.findVipGoodsList();
    if (result.status == Status.completed) {
      state = result.data;
    }
  }

  // 获取积分兑换列表
  void getIntegralExchangeList() async {
    var result = await ConfigService.getIntegralExchangeList(2);
    if (result.status == Status.completed) {
      state = result.data;
    }
  }

  // 兑换奖品
  Future<bool> exchangeGoods(ExchangeItem? item) async {
    SmartDialog.showLoading(msg: "兑换中...");
    var result = await ConfigService.getGoodsV4(item?.id ?? item?.goodsId, 2);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      ToastUtil.showToast("兑换成功");
      // 刷新用户积分
      ref.read(userSignIntegralProvider.notifier).getUserIntegral();
      // 刷新库存
      if (item?.goodsId != null) {
        getVipGoodsList();
      } else {
        getIntegralExchangeList();
      }
      return true;
    }
    ToastUtil.showToast(result.exception!.getMessage());
    return false;
  }
}

// 积分明细
// 页长
const int pageSize = 20;

// 积分明细列表结果
class IntegralDetailResult {
  final int pageNo;
  final int type;
  final List<IntegralDetailItem?>? integralDetailList;
  final LoadState? loadState;

  const IntegralDetailResult({
    this.pageNo = 1,
    this.type = 1,
    this.integralDetailList,
    this.loadState,
  });

  IntegralDetailResult copyWith({
    int? pageNo,
    int? type,
    List<IntegralDetailItem?>? integralDetailList,
    LoadState? loadState,
  }) {
    return IntegralDetailResult(
      pageNo: pageNo ?? this.pageNo,
      type: type ?? this.type,
      integralDetailList: integralDetailList ?? this.integralDetailList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class IntegralDetailList extends _$IntegralDetailList {
  @override
  IntegralDetailResult build() {
    state = const IntegralDetailResult();
    loadGoods();
    return state;
  }

  // 切换tab
  void changeTab(int type) {
    state = state.copyWith(type: type);
    loadGoods();
  }

  /// 加载数据
  void loadGoods() async {
    state = state.copyWith(
      pageNo: 1,
      loadState: null,
    );
    var result = await ConfigService.getUserIntegralDetail(
      state.pageNo,
      pageSize,
      state.type == 1 ? "增加" : "减少",
    );
    if (result.status == Status.completed) {
      bool hasNextPage = false;
      var data = result.data?.data;
      var total = result.data?.total;
      if (data != null && data.isNotEmpty && total != null && total != 0) {
        int totalPages = (total / pageSize).ceil(); // 总页数
        hasNextPage = state.pageNo < totalPages;
      }
      state = state.copyWith(
        integralDetailList: result.data?.data ?? [],
        loadState: hasNextPage ? LoadState.idle : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      pageNo: state.pageNo + 1,
    );
    var result = await ConfigService.getUserIntegralDetail(
      state.pageNo,
      pageSize,
      state.type == 1 ? "增加" : "减少",
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        bool hasNextPage = false;
        var data = result.data?.data;
        var total = result.data?.total;
        if (data != null && data.isNotEmpty && total != null && total != 0) {
          int totalPages = (total / pageSize).ceil(); // 总页数
          hasNextPage = state.pageNo < totalPages;
        }
        state = state.copyWith(
          integralDetailList: [
            ...?state.integralDetailList,
            ...?result.data?.data
          ],
          loadState: hasNextPage ? LoadState.idle : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
