import 'package:msmds_platform/app/repository/modals/sign/luck_gift_info.dart';
import 'package:msmds_platform/app/repository/modals/sign/luck_gift_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../repository/service/config_service.dart';

part 'koi_red_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.provider.sign
/// @ClassName: koi_red_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/14 16:58
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/14 16:58
/// @UpdateRemark: 更新说明

// 获取锦鲤红包列表
@riverpod
class FetchLuckGiftInfo extends _$FetchLuckGiftInfo {
  @override
  LuckGiftInfo? build() {
    getLuckGiftInfo();
    return null;
  }

  void getLuckGiftInfo() async {
    var result = await ConfigService.luckyGiftInfo();
    if (result.status == Status.completed) {
      var giftInfo = result.data;
      giftInfo?.luckyGifts = _list(giftInfo);
      state = giftInfo;
    }
  }

  List<LuckGiftItem?> _list(LuckGiftInfo? giftInfo) {
    List<LuckGiftItem?> dataList = [];
    if (giftInfo == null || giftInfo.showNumber == null) return dataList;
    for (var i = 0; i < giftInfo.showNumber!; i++) {
      dataList.add(_item(i, giftInfo.luckyGifts));
    }
    return dataList;
  }

  LuckGiftItem? _item(int i, List<LuckGiftItem?>? list) {
    LuckGiftItem? gift = LuckGiftItem()..localIndex = i;
    if (list == null || list.isEmpty) return gift;
    try {
      gift = list.singleWhere((element) => element?.prizeIndex == i);
    } catch (e) {
      gift = LuckGiftItem()..localIndex = i;
    }
    return gift;
  }
}

// 用户领取锦鲤红包
@riverpod
class ReceiveKoiRedPacket extends _$ReceiveKoiRedPacket {
  @override
  LuckGiftItem? build() {
    return null;
  }

  // 获取领取指定锦鲤红包
  void receive(int? index) async {
    var result = await ConfigService.receiveLuckyGift(index, true);
    if (result.status == Status.completed) {
      state = result.data;
      // 更新红包列表
      ref.read(fetchLuckGiftInfoProvider.notifier).getLuckGiftInfo();
    }
  }
}
