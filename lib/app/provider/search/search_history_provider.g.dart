// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_history_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchKeywordHistoryHash() =>
    r'485f2ad086a538e919936b7b19ae34c2b0c38272';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_history_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/21 10:56
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/21 10:56
/// @UpdateRemark: 更新说明
///
/// Copied from [SearchKeywordHistory].
@ProviderFor(SearchKeywordHistory)
final searchKeywordHistoryProvider =
    AutoDisposeNotifierProvider<SearchKeywordHistory, HistoryList?>.internal(
  SearchKeywordHistory.new,
  name: r'searchKeywordHistoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$searchKeywordHistoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchKeywordHistory = AutoDisposeNotifier<HistoryList?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
