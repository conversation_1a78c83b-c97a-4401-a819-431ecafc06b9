import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/search/history_list.dart';
import 'package:msmds_platform/app/repository/service/search_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'search_history_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_history_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/21 10:56
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/21 10:56
/// @UpdateRemark: 更新说明
@riverpod
class SearchKeywordHistory extends _$SearchKeywordHistory {
  @override
  HistoryList? build() {
    listSearchHistory();
    return null;
  }

  /// 获取搜索历史列表
  void listSearchHistory() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var result = await SearchService.listSearchHistory();
      if (result.status == Status.completed) {
        state = result.data;
      }
    }
  }

  /// 保存搜索历史
  void saveHistory(String searchTerm) async {
    var result = await SearchService.saveSearchHistory(searchTerm);
    if (result.status == Status.completed) {
      listSearchHistory();
    }
  }

  /// 清除搜索历史
  void cleanHistory() async {
    var result = await SearchService.cleanSearchHistory();
    if (result.status == Status.completed) {
      listSearchHistory();
    }
  }
}
