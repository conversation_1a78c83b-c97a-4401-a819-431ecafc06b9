// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'filter_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$searchFilterHash() => r'44a580cd595755a39e49bd746712d74a0dc3b61b';

/// See also [SearchFilter].
@ProviderFor(SearchFilter)
final searchFilterProvider =
    AutoDisposeNotifierProvider<SearchFilter, FilterData>.internal(
  SearchFilter.new,
  name: r'searchFilterProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$searchFilterHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchFilter = AutoDisposeNotifier<FilterData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
