import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/search/filter_provider.dart';
import 'package:msmds_platform/app/provider/search/platform_provider.dart';
import 'package:msmds_platform/app/provider/search/search_history_provider.dart';
import 'package:msmds_platform/app/repository/modals/search/search_goods.dart';
import 'package:msmds_platform/app/repository/service/search_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../utils/toast_util.dart';

part 'search_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON>y Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: search_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 10:18
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 10:18
/// @UpdateRemark: 更新说明

/// 页长
const int pageSize = 20;

/// 搜索词
@riverpod
class SearchKeyword extends _$SearchKeyword {
  @override
  String? build() {
    return null;
  }

  /// 设置搜索词
  void setKeyword(String? keyWord) {
    state = keyWord;
  }
}

/// 搜索结果
class SearchResult {
  final int pageNo;
  final int pageNum;
  final List<SearchGoods?>? goods;
  final LoadState? loadState;

  const SearchResult({
    this.pageNo = 1,
    this.pageNum = pageSize,
    this.goods,
    this.loadState,
  });

  SearchResult copyWith({
    int? page,
    int? size,
    List<SearchGoods?>? goods,
    LoadState? loadState,
  }) {
    return SearchResult(
      pageNo: page ?? pageNo,
      pageNum: size ?? pageNum,
      goods: goods ?? this.goods,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class Search extends _$Search {
  @override
  SearchResult build() {
    state = const SearchResult();
    return state;
  }

  /// 加载数据
  void search() async {
    var platform = ref.read(platformProvider);
    var keyword = ref.read(searchKeywordProvider);
    var filter = ref.read(searchFilterProvider);
    if (keyword != null && keyword.isNotEmpty) {
      state = state.copyWith(
        loadState: null,
        page: 1,
      );
      SmartDialog.showLoading(msg: "加载中...");
      var result = await SearchService.searchKeyword(
        state.pageNo,
        keyword,
        filter,
        storeType: platform.storeType,
      );
      debugPrint("search_result: ${result.data}");
      SmartDialog.dismiss();
      // 保存搜索历史
      ref.read(searchKeywordHistoryProvider.notifier).saveHistory(keyword);
      if (result.status == Status.completed) {
        state = state.copyWith(
          goods: result.data ?? [],
          loadState: LoadState.idle,
        );
      } else {
        state = state.copyWith(
          goods: [],
          loadState: LoadState.idle,
        );
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    var platform = ref.read(platformProvider);
    var keyword = ref.read(searchKeywordProvider);
    var filter = ref.read(searchFilterProvider);
    if (keyword != null && keyword.isNotEmpty) {
      state = state.copyWith(
        loadState: LoadState.loading,
        page: state.pageNo + 1,
      );
      var result = await SearchService.searchKeyword(
        state.pageNo,
        keyword,
        filter,
        storeType: platform.storeType,
      );
      if (result.status == Status.completed) {
        if (result.data != null && result.data!.isNotEmpty) {
          state = state.copyWith(
            goods: [...?state.goods, ...?result.data],
            loadState: LoadState.idle,
          );
        } else {
          state = state.copyWith(
            loadState: LoadState.noMore,
          );
        }
      }
    }
  }
}

/// search header focus
@riverpod
class SearchFocusNode extends _$SearchFocusNode {
  @override
  FocusNode build() {
    state = FocusNode();
    ref.onDispose(() {
      state.dispose();
    });
    return state;
  }

  void requestFocus() {
    state.requestFocus();
  }
}

/// 搜索页是否自动获取键盘焦点
@riverpod
class SearchAutoFocus extends _$SearchAutoFocus {
  @override
  bool build() {
    return true;
  }

  void setAutoFocus(bool auto) {
    state = auto;
  }
}
