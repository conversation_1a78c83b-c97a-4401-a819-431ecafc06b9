import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'filter_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: filter_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 14:41
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 14:41
/// @UpdateRemark: 更新说明

/// 筛选条件
class FilterData {
  final int hasCoupon;
  final int sortType;
  final int sort;
  final int priceTag;

  const FilterData({
    /// 是否有券，-1（不限制）或者1（有券）
    this.hasCoupon = -1,

    /// 排序类型：-1(综合排序)、4：价格 3：销量
    this.sortType = -1,

    /// 排序：-1(不限)、1：升序 2：降序
    this.sort = -1,

    /// 价格排序标签，多次点击价格进行升序和降序切换
    this.priceTag = 0,
  });

  FilterData copyWith({
    int? hasCoupon,
    int? sortType,
    int? sort,
    int? priceTag,
  }) {
    return FilterData(
      hasCoupon: hasCoupon ?? this.hasCoupon,
      sortType: sortType ?? this.sortType,
      sort: sort ?? this.sort,
      priceTag: priceTag ?? this.priceTag,
    );
  }
}

@riverpod
class SearchFilter extends _$SearchFilter {
  @override
  FilterData build() {
    return const FilterData();
  }

  /// 是否有券筛选
  void hasCoupon() {
    if (state.hasCoupon == -1) {
      state = state.copyWith(hasCoupon: 1);
    } else {
      state = state.copyWith(hasCoupon: -1);
    }

    /// 搜索
    ref.read(searchProvider.notifier).search();
  }

  /// 排序
  /// type=null（综合）
  /// type=4（价格）
  /// type=3（销量）
  void setSortType(int type) {
    if (type == -1) {
      /// 综合
      state = state.copyWith(sortType: -1, sort: -1, priceTag: 0);
    } else if (type == 2) {
      /// 价格
      if (state.priceTag == 0) {
        state = state.copyWith(sortType: 2, sort: 2, priceTag: 1);
      } else if (state.priceTag == 1) {
        state = state.copyWith(sortType: 2, sort: 1, priceTag: 2);
      } else if (state.priceTag == 2) {
        state = state.copyWith(sortType: 2, sort: 2, priceTag: 1);
      }
    } else {
      /// 销量
      state = state.copyWith(sortType: 1, sort: 1, priceTag: 0);
    }

    /// 搜索
    ref.read(searchProvider.notifier).search();
  }
}
