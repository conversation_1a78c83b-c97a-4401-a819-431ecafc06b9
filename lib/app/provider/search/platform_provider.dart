import 'package:msmds_platform/app/provider/search/search_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'platform_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: platform_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 11:10
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 11:10
/// @UpdateRemark: 更新说明

/// 搜索平台
class PlatformData {
  final String name;
  final int storeType;

  const PlatformData({
    this.name = "京东",
    this.storeType = 2,
  });
}

final List<PlatformData> platformList = [
  const PlatformData(name: "淘宝", storeType: 1),
  const PlatformData(name: "京东", storeType: 2),
  const PlatformData(name: "拼多多", storeType: 3),
  const PlatformData(name: "唯品会", storeType: 7),
  // const PlatformData(name: "抖音", storeType: 6),
];

@riverpod
class PlatformList extends _$PlatformList {
  @override
  List<PlatformData> build() {
    return platformList;
  }
}

@riverpod
class Platform extends _$Platform {
  @override
  PlatformData build() {
    var data = ref.watch(platformListProvider);
    return data[0];
  }

  /// 设置当前选中的平台
  void setCurrentPlatform(int storeType, {bool isSearch = true}) {
    var data = ref.watch(platformListProvider);
    state = data.firstWhere((element) => element.storeType == storeType);
    if (isSearch) {
      ref.read(searchProvider.notifier).search();
    }
  }
}
