import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/config/theme_config.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: theme_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:24
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:24
/// @UpdateRemark: 更新说明
ThemeData get lightTheme => ThemeData(
      useMaterial3: false,
      brightness: Brightness.light,
      primarySwatch: lightColor,
      highlightColor: Colors.transparent,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: Colors.red,
        selectionColor: Colors.red,
        selectionHandleColor: Colors.red,
      ),
      scaffoldBackgroundColor: const Color(0xFFF9F9F9),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: const Color(0xFFFFFFFF),
        selectedLabelStyle: TextStyle(fontSize: 12.sp),
        unselectedLabelStyle: TextStyle(fontSize: 12.sp),
        unselectedItemColor: const Color(0xFF333333),
        selectedItemColor: const Color(0xFFFF0E38),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: Colors.blueAccent,
      ),
      tabBarTheme: const TabBarTheme(
        labelColor: Color(0xFF000000),
        unselectedLabelColor: Color(0xFF666666),
        splashFactory: NoSplash.splashFactory,
      ),
    );

ThemeData get darkTheme => ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: darkColor,
      scaffoldBackgroundColor: const Color(0xFF161616),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        unselectedItemColor: Color(0xFF000000),
        selectedItemColor: Color(0xFFFF0E38),
      ),
    );
