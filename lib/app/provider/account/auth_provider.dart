import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/account/login_provider.dart';
import 'package:msmds_platform/app/repository/modals/account/account.dart';
import 'package:msmds_platform/app/repository/modals/account/account_detail.dart';
import 'package:msmds_platform/app/repository/service/account_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/config/global_config.dart';
import 'package:msmds_platform/utils/toast_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_provider.g.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: auth_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/5 17:02
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/5 17:02
/// @UpdateRemark: 更新说明
@riverpod
class Auth extends _$Auth {
  @override
  Account? build() {
    return GlobalConfig.account;
  }

  /// 登录
  Future<Account?> login() async {
    var code = ref.read(verifyCodeProvider);
    var phone = ref.read(loginProvider);
    debugPrint("login: $phone, $code");
    if (code != null && code.isNotEmpty && phone != null && phone.isNotEmpty) {
      SmartDialog.showLoading(msg: "登录中...");
      var result = await AccountService.loginByPhone(phone, code);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        GlobalConfig.account = result.data;
        GlobalConfig.saveProfile();
        state = result.data;
        return result.data;
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
    return null;
  }

  // 更新用户信息
  void refreshUserInfo(AccountDetail? accountDetail) async {
    state = state?.copyWith(data: accountDetail);
    GlobalConfig.account = state;
    GlobalConfig.saveProfile();
  }

  /// 退出登录
  void logout() {
    GlobalConfig.account = null;
    GlobalConfig.saveProfile();
    state = null;
  }
}

/// 获取酷赛唯一ID
@riverpod
Future<String?> fetchChipId(FetchChipIdRef ref) async {
  return null;
}
