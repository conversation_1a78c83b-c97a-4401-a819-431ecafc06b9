import 'package:msmds_platform/app/repository/modals/account/user_fans_item.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/http/api_response.dart';
import '../../../widgets/refresh/refresh_container.dart';
import '../../repository/modals/account/user_fans_list.dart';
import '../../repository/service/account_service.dart';

part 'invite_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: app.provider.account
/// @ClassName: invite_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/11/25 14:31
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/25 14:31
/// @UpdateRemark: 更新说明
/// 页长
const int pageSize = 20;

/// 粉丝收益列表结果
class UserFansResult {
  final int pageNo;
  final List<UserFansItem?>? userFansList;
  final UserFansList? userFansData;
  final LoadState? loadState;

  const UserFansResult({
    this.pageNo = 1,
    this.userFansList,
    this.userFansData,
    this.loadState,
  });

  UserFansResult copyWith({
    int? pageNo,
    List<UserFansItem?>? userFansList,
    UserFansList? userFansData,
    LoadState? loadState,
  }) {
    return UserFansResult(
      pageNo: pageNo ?? this.pageNo,
      userFansList: userFansList ?? this.userFansList,
      userFansData: userFansData ?? this.userFansData,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class UserFansInfoList extends _$UserFansInfoList {
  @override
  UserFansResult build() {
    state = const UserFansResult();
    loadGoods();
    return state;
  }

  /// 加载数据
  void loadGoods() async {
    state = state.copyWith(
      pageNo: 1,
      loadState: null,
    );
    var result = await AccountService.getInvitationInfo(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      bool? hasNextPage = result.data?.hasNextPage;
      state = state.copyWith(
        userFansData: result.data,
        userFansList: result.data?.fansInfoList ?? [],
        loadState: hasNextPage == true ? LoadState.idle : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  void loadMore() async {
    state = state.copyWith(
      loadState: LoadState.loading,
      pageNo: state.pageNo + 1,
    );
    var result = await AccountService.getInvitationInfo(
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        bool? hasNextPage = result.data?.hasNextPage;
        state = state.copyWith(
          userFansList: [...?state.userFansList, ...?result.data?.fansInfoList],
          loadState: hasNextPage == true ? LoadState.idle : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}
