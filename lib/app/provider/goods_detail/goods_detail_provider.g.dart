// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'goods_detail_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchGoodsDetailHash() => r'5c398f86fcddd1c66c53313247e2b1bbdea06813';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

typedef FetchGoodsDetailRef = AutoDisposeFutureProviderRef<GoodsDetail?>;

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.goods_detail
/// @ClassName: goods_detail_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 14:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 14:35
/// @UpdateRemark: 更新说明
/// 查询商品详情
///
/// Copied from [fetchGoodsDetail].
@ProviderFor(fetchGoodsDetail)
const fetchGoodsDetailProvider = FetchGoodsDetailFamily();

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.goods_detail
/// @ClassName: goods_detail_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 14:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 14:35
/// @UpdateRemark: 更新说明
/// 查询商品详情
///
/// Copied from [fetchGoodsDetail].
class FetchGoodsDetailFamily extends Family<AsyncValue<GoodsDetail?>> {
  /// Copyright (C), 2021-2024, Franky Lee
  /// @ProjectName: msmds_platform
  /// @Package: app.provider.goods_detail
  /// @ClassName: goods_detail_provider
  /// @Description:
  /// @Author: frankylee
  /// @CreateDate: 2024/9/18 14:35
  /// @UpdateUser: frankylee
  /// @UpdateData: 2024/9/18 14:35
  /// @UpdateRemark: 更新说明
  /// 查询商品详情
  ///
  /// Copied from [fetchGoodsDetail].
  const FetchGoodsDetailFamily();

  /// Copyright (C), 2021-2024, Franky Lee
  /// @ProjectName: msmds_platform
  /// @Package: app.provider.goods_detail
  /// @ClassName: goods_detail_provider
  /// @Description:
  /// @Author: frankylee
  /// @CreateDate: 2024/9/18 14:35
  /// @UpdateUser: frankylee
  /// @UpdateData: 2024/9/18 14:35
  /// @UpdateRemark: 更新说明
  /// 查询商品详情
  ///
  /// Copied from [fetchGoodsDetail].
  FetchGoodsDetailProvider call(
    Object? arguments,
  ) {
    return FetchGoodsDetailProvider(
      arguments,
    );
  }

  @override
  FetchGoodsDetailProvider getProviderOverride(
    covariant FetchGoodsDetailProvider provider,
  ) {
    return call(
      provider.arguments,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGoodsDetailProvider';
}

/// Copyright (C), 2021-2024, Franky Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.goods_detail
/// @ClassName: goods_detail_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 14:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 14:35
/// @UpdateRemark: 更新说明
/// 查询商品详情
///
/// Copied from [fetchGoodsDetail].
class FetchGoodsDetailProvider extends AutoDisposeFutureProvider<GoodsDetail?> {
  /// Copyright (C), 2021-2024, Franky Lee
  /// @ProjectName: msmds_platform
  /// @Package: app.provider.goods_detail
  /// @ClassName: goods_detail_provider
  /// @Description:
  /// @Author: frankylee
  /// @CreateDate: 2024/9/18 14:35
  /// @UpdateUser: frankylee
  /// @UpdateData: 2024/9/18 14:35
  /// @UpdateRemark: 更新说明
  /// 查询商品详情
  ///
  /// Copied from [fetchGoodsDetail].
  FetchGoodsDetailProvider(
    this.arguments,
  ) : super.internal(
          (ref) => fetchGoodsDetail(
            ref,
            arguments,
          ),
          from: fetchGoodsDetailProvider,
          name: r'fetchGoodsDetailProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchGoodsDetailHash,
          dependencies: FetchGoodsDetailFamily._dependencies,
          allTransitiveDependencies:
              FetchGoodsDetailFamily._allTransitiveDependencies,
        );

  final Object? arguments;

  @override
  bool operator ==(Object other) {
    return other is FetchGoodsDetailProvider && other.arguments == arguments;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, arguments.hashCode);

    return _SystemHash.finish(hash);
  }
}

String _$fetchNoticeDetailHash() => r'39efb7e0c09650fdd6b4e1fbeae7dbd0c748c1a1';

/// 查询商品详情通知信息
///
/// Copied from [fetchNoticeDetail].
@ProviderFor(fetchNoticeDetail)
final fetchNoticeDetailProvider =
    AutoDisposeFutureProvider<List<Popularize>?>.internal(
  fetchNoticeDetail,
  name: r'fetchNoticeDetailProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchNoticeDetailHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FetchNoticeDetailRef = AutoDisposeFutureProviderRef<List<Popularize>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
