import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/repository/modals/popularize/popularize.dart';
import 'package:msmds_platform/app/repository/service/config_service.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../repository/modals/goods/goods_detail.dart';

part 'goods_detail_provider.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON> Lee
/// @ProjectName: msmds_platform
/// @Package: app.provider.goods_detail
/// @ClassName: goods_detail_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/9/18 14:35
/// @UpdateUser: frankylee
/// @UpdateData: 2024/9/18 14:35
/// @UpdateRemark: 更新说明

/// 查询商品详情
@riverpod
Future<GoodsDetail?> fetchGoodsDetail(
  FetchGoodsDetailRef ref,
  Object? arguments,
) async {
  try {
    var skuId = (arguments as Map)["skuId"];
    var platformType = (arguments)["platformType"];
    var bizSceneId = (arguments)["bizSceneId"];

    var result = await GoodsService.getGoodsDetail(
      skuId,
      platformType,
      bizSceneId: bizSceneId,
    );
    if (result.data == null) {
      SmartDialog.showToast("该商品已被卖家下架了");
    }
    return result.data;
  } catch (e) {
    return null;
  }
}

/// 查询商品详情通知信息
@riverpod
Future<List<Popularize>?> fetchNoticeDetail(
  FetchNoticeDetailRef ref,
) async {
  try {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var result = await ConfigService.getPopularizeConfig(
      4,
      packageInfo.version,
    );
    return result.data;
  } catch (e) {
    return null;
  }
}
