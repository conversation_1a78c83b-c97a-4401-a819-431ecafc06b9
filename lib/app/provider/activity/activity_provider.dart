import 'package:msmds_platform/app/repository/modals/goods/goods.dart';
import 'package:msmds_platform/app/repository/modals/goods/goods_pkg.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:msmds_platform/widgets/refresh/refresh_container.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../dialog/ordinary_dialog.dart';
import '../../repository/modals/config/icon_config.dart';
import '../../repository/service/config_service.dart';

part 'activity_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON>y <PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: pdd_activity_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 11:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 11:12
/// @UpdateRemark: 更新说明

/// ======================加载商品包列表构建Tab

/// 拼多多二级页商品包列表
@riverpod
class PddGoodsPkgList extends _$PddGoodsPkgList {
  @override
  List<GoodsPkg?>? build() {
    fetchPkgList();
    return null;
  }

  void fetchPkgList() async {
    var result = await GoodsService.getListGoodsPkg("pdd_icon", 3);
    state = result.data;
    if (result.data != null && result.data!.isNotEmpty) {
      ref.watch(goodsByPkgProvider.notifier).setPkg(result.data?.first);
    }
  }
}

/// 京东二级页商品包列表
@riverpod
class JdGoodsPkgList extends _$JdGoodsPkgList {
  @override
  List<GoodsPkg?>? build() {
    fetchPkgList();
    return null;
  }

  void fetchPkgList() async {
    var result = await GoodsService.getListGoodsPkg("jd_icon", 2);
    state = result.data;
    if (result.data != null && result.data!.isNotEmpty) {
      ref.watch(goodsByPkgProvider.notifier).setPkg(result.data?.first);
    }
  }
}

/// 淘宝二级页商品包列表
@riverpod
class TbGoodsPkgList extends _$TbGoodsPkgList {
  @override
  List<GoodsPkg?>? build() {
    fetchPkgList();
    return null;
  }

  void fetchPkgList() async {
    var result = await GoodsService.getListGoodsPkg("tb_icon", 1);
    state = result.data;
    if (result.data != null && result.data!.isNotEmpty) {
      ref.watch(goodsByPkgProvider.notifier).setPkg(result.data?.first);
    }
  }
}

/// ======================加载商品包列表构建Tab

/// ======================云配弹窗
/// 首页和我的页普通弹窗
@riverpod
class ActivityOrdinaryDialog extends _$ActivityOrdinaryDialog {
  @override
  void build() {
    return;
  }

  // 配置淘宝活动页的弹窗显示
  void showTbActivityDialog() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var result = await ConfigService.getIconConfig(6, packageInfo.version);
    if (result.status == Status.completed &&
        result.data != null &&
        result.data!.isNotEmpty) {
      OrdinaryDialog.show(result.data!);
    }
  }

  // 配置京东活动页的弹窗显示
  void showJdActivityDialog() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var result = await ConfigService.getIconConfig(5, packageInfo.version);
    if (result.status == Status.completed &&
        result.data != null &&
        result.data!.isNotEmpty) {
      OrdinaryDialog.show(result.data!);
    }
  }

  // 配置拼多多活动页的弹窗显示
  void showPddActivityDialog() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    var result = await ConfigService.getIconConfig(7, packageInfo.version);
    if (result.status == Status.completed &&
        result.data != null &&
        result.data!.isNotEmpty) {
      OrdinaryDialog.show(result.data!);
    }
  }
}

/// ======================云配弹窗

/// ======================活动页云配icon
@riverpod
Future<List<List<IconConfig>>?> fetchActivityIconConfig(
  FetchActivityIconConfigRef ref,
  int iconType,
) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  var result = await ConfigService.getIconConfig(iconType, packageInfo.version);
  if (result.status == Status.completed) {
    /// 10个icon为一组
    var configs = result.data ?? [];
    if (configs.isNotEmpty) {
      List<List<IconConfig>> data = [];
      for (int i = 0; i < configs.length; i += 10) {
        int start = i;
        int end = i + 10;
        if (end > configs.length) {
          end = configs.length;
        }
        var item = configs.getRange(start, end).toList();
        data.add(item);
      }
      return data;
    }
  }
  return null;
}

/// ======================活动页云配icon

/// 页长
const int pageSize = 20;

/// 商品包获取商品列表结果
class GoodsResult {
  final int pageNo;
  final GoodsPkg? pkgId;
  final List<Goods?>? goods;
  final LoadState? loadState;

  const GoodsResult({
    this.pageNo = 1,
    this.pkgId,
    this.goods,
    this.loadState,
  });

  GoodsResult copyWith({
    int? page,
    GoodsPkg? pkgId,
    List<Goods?>? goods,
    LoadState? loadState,
  }) {
    return GoodsResult(
      pageNo: page ?? pageNo,
      pkgId: pkgId ?? this.pkgId,
      goods: goods ?? this.goods,
      loadState: loadState ?? this.loadState,
    );
  }
}

/// ======================加载商品======================
@riverpod
class GoodsByPkg extends _$GoodsByPkg {
  @override
  GoodsResult build() {
    state = const GoodsResult();
    return state;
  }

  /// 选择tab
  void setPkg(GoodsPkg? goodsPkg) {
    state = state.copyWith(pkgId: goodsPkg);
    loadGoods();
  }

  /// 加载数据
  void loadGoods() async {
    var pkgInfo = state.pkgId;
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        page: 1,
        loadState: null,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        state = state.copyWith(
          goods: result.data?.list,
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMore() async {
    var pkgInfo = state.pkgId;
    if (pkgInfo != null && pkgInfo.id != null && pkgInfo.goodsType != null) {
      state = state.copyWith(
        loadState: LoadState.loading,
        page: state.pageNo + 1,
      );
      var result = await GoodsService.getListGoodsByPkg(
        state.pageNo,
        pageSize,
        pkgInfo.id!,
        pkgInfo.goodsType!,
      );
      if (result.status == Status.completed) {
        if (result.data != null) {
          state = state.copyWith(
            goods: [...?state.goods, ...?result.data?.list],
            loadState: result.data?.hasNextPage == true
                ? LoadState.idle
                : LoadState.noMore,
          );
        } else {
          state = state.copyWith(
            loadState: LoadState.noMore,
          );
        }
      }
    }
  }
}
