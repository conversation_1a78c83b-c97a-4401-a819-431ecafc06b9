// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'activity_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchActivityIconConfigHash() =>
    r'ef914214e5cab2c6fa6cd9249f87c6942fbe2197';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

typedef FetchActivityIconConfigRef
    = AutoDisposeFutureProviderRef<List<List<IconConfig>>?>;

/// ======================云配弹窗
/// ======================活动页云配icon
///
/// Copied from [fetchActivityIconConfig].
@ProviderFor(fetchActivityIconConfig)
const fetchActivityIconConfigProvider = FetchActivityIconConfigFamily();

/// ======================云配弹窗
/// ======================活动页云配icon
///
/// Copied from [fetchActivityIconConfig].
class FetchActivityIconConfigFamily
    extends Family<AsyncValue<List<List<IconConfig>>?>> {
  /// ======================云配弹窗
  /// ======================活动页云配icon
  ///
  /// Copied from [fetchActivityIconConfig].
  const FetchActivityIconConfigFamily();

  /// ======================云配弹窗
  /// ======================活动页云配icon
  ///
  /// Copied from [fetchActivityIconConfig].
  FetchActivityIconConfigProvider call(
    int iconType,
  ) {
    return FetchActivityIconConfigProvider(
      iconType,
    );
  }

  @override
  FetchActivityIconConfigProvider getProviderOverride(
    covariant FetchActivityIconConfigProvider provider,
  ) {
    return call(
      provider.iconType,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchActivityIconConfigProvider';
}

/// ======================云配弹窗
/// ======================活动页云配icon
///
/// Copied from [fetchActivityIconConfig].
class FetchActivityIconConfigProvider
    extends AutoDisposeFutureProvider<List<List<IconConfig>>?> {
  /// ======================云配弹窗
  /// ======================活动页云配icon
  ///
  /// Copied from [fetchActivityIconConfig].
  FetchActivityIconConfigProvider(
    this.iconType,
  ) : super.internal(
          (ref) => fetchActivityIconConfig(
            ref,
            iconType,
          ),
          from: fetchActivityIconConfigProvider,
          name: r'fetchActivityIconConfigProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchActivityIconConfigHash,
          dependencies: FetchActivityIconConfigFamily._dependencies,
          allTransitiveDependencies:
              FetchActivityIconConfigFamily._allTransitiveDependencies,
        );

  final int iconType;

  @override
  bool operator ==(Object other) {
    return other is FetchActivityIconConfigProvider &&
        other.iconType == iconType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, iconType.hashCode);

    return _SystemHash.finish(hash);
  }
}

String _$pddGoodsPkgListHash() => r'3a2c9a5332baf62a29879964073af303c967e011';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: pdd_activity_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/19 11:12
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/19 11:12
/// @UpdateRemark: 更新说明
/// ======================加载商品包列表构建Tab
/// 拼多多二级页商品包列表
///
/// Copied from [PddGoodsPkgList].
@ProviderFor(PddGoodsPkgList)
final pddGoodsPkgListProvider =
    AutoDisposeNotifierProvider<PddGoodsPkgList, List<GoodsPkg?>?>.internal(
  PddGoodsPkgList.new,
  name: r'pddGoodsPkgListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pddGoodsPkgListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PddGoodsPkgList = AutoDisposeNotifier<List<GoodsPkg?>?>;
String _$jdGoodsPkgListHash() => r'dbe5d345e37c6285c4f6b4993a0a0dff3c78bd83';

/// 京东二级页商品包列表
///
/// Copied from [JdGoodsPkgList].
@ProviderFor(JdGoodsPkgList)
final jdGoodsPkgListProvider =
    AutoDisposeNotifierProvider<JdGoodsPkgList, List<GoodsPkg?>?>.internal(
  JdGoodsPkgList.new,
  name: r'jdGoodsPkgListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$jdGoodsPkgListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$JdGoodsPkgList = AutoDisposeNotifier<List<GoodsPkg?>?>;
String _$tbGoodsPkgListHash() => r'2eb52ab57623f78a949d2c71a6c6ae727f8deb20';

/// 淘宝二级页商品包列表
///
/// Copied from [TbGoodsPkgList].
@ProviderFor(TbGoodsPkgList)
final tbGoodsPkgListProvider =
    AutoDisposeNotifierProvider<TbGoodsPkgList, List<GoodsPkg?>?>.internal(
  TbGoodsPkgList.new,
  name: r'tbGoodsPkgListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tbGoodsPkgListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TbGoodsPkgList = AutoDisposeNotifier<List<GoodsPkg?>?>;
String _$activityOrdinaryDialogHash() =>
    r'e51902fec4ca17e20889d71e2227926d08947bea';

/// ======================加载商品包列表构建Tab
/// ======================云配弹窗
/// 首页和我的页普通弹窗
///
/// Copied from [ActivityOrdinaryDialog].
@ProviderFor(ActivityOrdinaryDialog)
final activityOrdinaryDialogProvider =
    AutoDisposeNotifierProvider<ActivityOrdinaryDialog, void>.internal(
  ActivityOrdinaryDialog.new,
  name: r'activityOrdinaryDialogProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$activityOrdinaryDialogHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ActivityOrdinaryDialog = AutoDisposeNotifier<void>;
String _$goodsByPkgHash() => r'bdb1565ff2b54cfb983fde2ac376d93ed76147a2';

/// ======================加载商品======================
///
/// Copied from [GoodsByPkg].
@ProviderFor(GoodsByPkg)
final goodsByPkgProvider =
    AutoDisposeNotifierProvider<GoodsByPkg, GoodsResult>.internal(
  GoodsByPkg.new,
  name: r'goodsByPkgProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$goodsByPkgHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$GoodsByPkg = AutoDisposeNotifier<GoodsResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
