import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/dialog/pdd_auth_dialog.dart';
import 'package:msmds_platform/app/dialog/tb_auth_dialog.dart';
import 'package:msmds_platform/app/provider/account/auth_provider.dart';
import 'package:msmds_platform/app/repository/modals/activity/ele_activity.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_cms_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/ddk_resource_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/jd_change_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/pdd_change_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/tb_activity_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/tb_change_url.dart';
import 'package:msmds_platform/app/repository/modals/transfer/wph_change_goods.dart';
import 'package:msmds_platform/app/repository/service/account_service.dart';
import 'package:msmds_platform/app/repository/service/chain_transfer_service.dart';
import 'package:msmds_platform/app/repository/service/goods_service.dart';
import 'package:msmds_platform/app/view/home/<USER>/dialog/share_dialog.dart';
import 'package:msmds_platform/common/http/api_response.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../plugin/alibc/alibc_ohos_plugin.dart';
import '../../../utils/toast_util.dart';
import '../../navigation/coosea.dart';
import '../../navigation/router.dart';
import '../../repository/modals/goods/goods_tb_pkg.dart';
import '../../repository/modals/transfer/dy_change_goods.dart';

part 'link_conversion_provider.g.dart';

/// Copyright (C), 2021-2023, Franky Lee
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: link_conversion
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/23 16:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/23 16:54
/// @UpdateRemark: 更新说明

/// 商品类型
enum GoodsType {
  tb(6, "淘宝"),
  jd(2, "京东"),
  pdd(10, "拼多多"),
  wph(19, "唯品会"),
  dy(21, "抖音");

  final int type;
  final String name;

  const GoodsType(this.type, this.name);
}

/// 商品统一点击入口
@riverpod
class OnGoodsItemTap extends _$OnGoodsItemTap {
  @override
  void build() {
    return;
  }

  /// materialId、couponUrl京东相关
  /// goodsSign 拼多多
  /// goodsId、code淘宝相关
  void onGoodsItemTap(
    int? goodsType, {
    String? materialId,
    String? couponUrl,
    String? goodsUrl,
    String? goodsSign,
    String? goodsId,
    String? code,
    String? adCode,
    String? rid,
  }) {
    GoodsType gt = GoodsType.values.firstWhere(
      (element) => element.type == goodsType,
    );
    switch (gt) {
      case GoodsType.jd:
        ref.read(jdConversionProvider.notifier).jdChangeUrl(
              goodsId,
              couponUrl: couponUrl,
            );
        break;
      case GoodsType.tb:
        ref.read(tbConversionProvider.notifier).tbChangeUrlByGoodsId(
              goodsUrl,
              code: code,
              couponUrl: couponUrl,
            );
        break;
      case GoodsType.pdd:
        ref.read(pddConversionProvider.notifier).changeUrlWithPddSkuId(goodsId);
        break;
      case GoodsType.wph:
        ref.read(wphConversionProvider.notifier).wphChangeUrl(
              goodsId,
              goodsId,
              adCode: adCode,
              rid: rid,
            );
        break;
      case GoodsType.dy:
        ref.read(dyConversionProvider.notifier).dyChangeUrl(goodsUrl, goodsId);
        break;
    }
  }
}

@riverpod
class TbConversion extends _$TbConversion {
  @override
  void build() {
    return;
  }

  /// 检查淘宝授权状态
  Future<bool?> checkAuth() async {
    // var result = await AccountService.userIsAuthTb(tbAppKey);
    return false;
  }

  /// 授权淘宝
  Future<bool> launchTbAuth() async {
    /// 传入阿里百川平台注册的应用名称和appId
    AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
    var authResult = await aliPlugin.topNativeAccess("买什么都省", "");
    debugPrint("launchTbAuth: $authResult");
    var accessToken = authResult["accessToken"] as String?;
    if (accessToken != null) {
      SmartDialog.showLoading(msg: "授权中...");
      var result = await AccountService.userTopNativeAuth(accessToken);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        ToastUtil.showToast("授权成功");
        return true;
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
    return false;
  }

  /// 淘宝商品转链
  Future<TbChangeUrl?> changeUrl(String goodsId, {String? code}) async {
    var result = await ChainTransferService.tbChangeUrl(goodsId, code: code);
    return result.data;
  }

  /// 淘宝活动转链
  Future<TbActivityUrl?> changeActivityId(String activityId) async {
    var result = await ChainTransferService.tbActivityChangeUrl(activityId);
    return result.data;
  }

  /// 淘宝商品转链
  void tbChangeUrlByGoodsId(
    String? goodsUrl, {
    String? code,
    String? couponUrl,
  }) async {
    if ((goodsUrl == null || goodsUrl.isEmpty) &&
        (couponUrl == null || couponUrl.isEmpty)) return;
    // 转链，转链失败错误码5006需要打开授权
    SmartDialog.showLoading(msg: "加载中...");
    var url = goodsUrl;
    if (couponUrl != null && couponUrl.isNotEmpty) {
      url = couponUrl;
    }
    var result = await GoodsService.getTBGoodChangeUrl(url, code: code);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("tbChangeUrlByGoodsId: ${result.data}");
      if (result.data != null && result.data!.isNotEmpty) {
        AlibcFlutterPlugin aliPlugin = AlibcFlutterPlugin();
        aliPlugin.launcherTbByUrl(result.data!);
      }
    } else {
      var code = result.exception?.getCode();
      debugPrint("tbChangeUrlByGoodsId-code: $code");
      if (code == 5006) {
        // 唤起授权弹窗
        TbAuthDialog.authDialog(() {
          launchTbAuth();
        });
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }

  // 淘宝分享转链
  void tbShareChangeUrlByGoodsId(GoodsTbPkg? goodsTbPkg) async {
    var goodsUrl = goodsTbPkg?.goodsUrl;
    var couponUrl = goodsTbPkg?.couponUrl;
    if ((goodsUrl == null || goodsUrl.isEmpty) &&
        (couponUrl == null || couponUrl.isEmpty)) return;
    // 转链，转链失败错误码5006需要打开授权
    SmartDialog.showLoading(msg: "加载中...");
    var url = goodsUrl;
    if (couponUrl != null && couponUrl.isNotEmpty) {
      url = couponUrl;
    }
    var result = await GoodsService.getTBGoodChangeUrl(url);
    if (result.status == Status.completed) {
      debugPrint("tbChangeUrlByGoodsId: ${result.data}");
      if (result.data != null && result.data!.isNotEmpty) {
        var shareResult = await GoodsService.shareContent(
          goodsTbPkg?.tbItemId ?? goodsTbPkg?.goodsId,
          6,
          goodsTbPkg?.purchaseDescription,
        );
        SmartDialog.dismiss();
        if (shareResult.status == Status.completed) {
          // 复制文案
          Clipboard.setData(ClipboardData(text: "${shareResult.data}"));
          // 唤起分享弹窗
          var userInfo = ref.read(authProvider);
          var vipLevel = userInfo?.data?.vipLevel ?? -1;
          var isVip = vipLevel >= 0;
          ShareDialog.show(
            isVip
                ? "${goodsTbPkg?.commission}"
                : "${goodsTbPkg?.noVipCommission}",
          );
        } else {
          ToastUtil.showToast(shareResult.exception!.getMessage());
        }
      }
    } else {
      SmartDialog.dismiss();
      var code = result.exception?.getCode();
      debugPrint("tbChangeUrlByGoodsId-code: $code");
      if (code == 5006) {
        // 唤起授权弹窗
        TbAuthDialog.authDialog(() {
          launchTbAuth();
        });
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }
}

@riverpod
class JdConversion extends _$JdConversion {
  @override
  JdChangeUrl? build() {
    return null;
  }

  /// 京东商品转链
  void jdChangeUrl(
    String? goodsId, {
    String? couponUrl,
  }) async {
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ChainTransferService.jdChangeUrl(
      goodsId,
      couponLink: couponUrl,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("changeUrlWithJdMaterialId: $result");

      /// 跳转京东或者h5
      if (result.data != null && result.data!.url != null) {
        var url = result.data!.url!;
        var data = jsonEncode({"category": "jump", "des": "m", "url": url});
        var schemeUrl =
            "openapp.jdmobile://virtual?params=${Uri.encodeComponent(data)}";
        ref.read(launchAppProvider.notifier).launchApp(schemeUrl, url);
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 京东活动转链
  void changeUrlWithJdActivityUrl(String? activityUrl) async {
    if (activityUrl == null || activityUrl.isEmpty) return;
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ChainTransferService.jdActivityChangeUrl(activityUrl);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("changeUrlWithJdActivityUrl: $result");

      /// 跳转京东或者h5
      if (result.data != null && result.data!.shortUrl != null) {
        var url = result.data!.shortUrl!;
        var data = jsonEncode({"category": "jump", "des": "m", "url": url});
        var schemeUrl =
            "openapp.jdmobile://virtual?params=${Uri.encodeComponent(data)}";
        ref.read(launchAppProvider.notifier).launchApp(schemeUrl, url);
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

@riverpod
class PddConversion extends _$PddConversion {
  @override
  PddChangeUrl? build() {
    return null;
  }

  /// 拼多多商品转链
  /// 商品转链会返回授权链接，这里不用判断是否授权
  /// 可直接调用转链接口
  void changeUrlWithPddSkuId(String? skuId) async {
    if (skuId == null || skuId.isEmpty) return;
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ChainTransferService.pddChangeUrl(skuId);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      /// 跳转拼多多或者h5
      var appCouponUrl = result.data?.appCouponUrl;
      if (appCouponUrl != null) {
        var splitUrl = appCouponUrl.split("//mobile.yangkeduo.com");
        if (splitUrl.length >= 2) {
          var url = splitUrl[1];
          var schemaUrl = "pinduoduo://com.xunmeng.pinduoduo$url";
          debugPrint("changeUrlWithPddGoodsSign: $schemaUrl");
          debugPrint("changeUrlWithPddGoodsSign: $appCouponUrl");
          ref
              .read(launchAppProvider.notifier)
              .launchApp(schemaUrl, appCouponUrl);
        }
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 检查授权状态
  Future<bool?> checkAuth() async {
    var result = await AccountService.userIsAuthPdd();
    return result.data;
  }

  /// 获取授权链接
  Future<PddChangeUrl?> getAuthUrl() async {
    var result = await AccountService.userGetAuthPddUrl();
    return result.data;
  }

  /// 多多进宝频道推广转链
  Future<DdkResourceUrl?> ddkResourceChangeUrl({
    int? resourceType,
    String? originalLink,
  }) async {
    var result = await ChainTransferService.ddkResourceUrl(
      resourceType: resourceType,
      url: originalLink,
    );
    return result.data;
  }

  /// 获取商城-频道推广链接
  Future<DdkCmsUrl?> cmsUrlChangeUrl(int channelType) async {
    var result = await ChainTransferService.cmsPromUrlGenerate(
      channelType: channelType,
    );
    return result.data;
  }

  /// 生成营销工具推广链接
  Future<DdkCmsUrl?> rpUrlChangeUrl(int channelType) async {
    var result = await ChainTransferService.rpPromUrlGenerate(
      channelType: channelType,
    );
    return result.data;
  }

  /// 拼多多活动转链
  /// 先检查拼多多是否授权
  ///   已授权：调转链接接口，跳转
  ///   未授权：获取授权链接， 唤起授权弹窗，跳转拼多多授权
  ///
  void pddChangeUrlByActivity({
    int? channelType,
    int? jumpType,
    int? resourceType,
    String? originalLink,
  }) async {
    SmartDialog.showLoading(msg: "加载中...");
    var isAuth = await checkAuth();
    if (isAuth == true) {
      /// 已授权
      if (channelType != null) {
        DdkCmsUrl? ddkCmsUrl;
        if (jumpType == 7) {
          /// 生成营销工具推广链接
          ddkCmsUrl = await rpUrlChangeUrl(channelType);
        } else {
          /// 获取商城-频道推广链接
          ddkCmsUrl = await cmsUrlChangeUrl(channelType);
        }
        var urlList = ddkCmsUrl?.urlList?.first;
        if (urlList != null) {
          var schemaUrl = "";
          var h5Url = "";
          if (jumpType == 7) {
            schemaUrl = urlList.schemaUrl ?? "";
            h5Url = urlList.mobileShortUrl ?? "";
          } else {
            if (urlList.multiUrlList != null) {
              schemaUrl = urlList.multiUrlList?.schemaUrl ?? "";
              h5Url = urlList.multiUrlList?.mobileUrl ??
                  urlList.multiUrlList?.mobileShortUrl ??
                  "";
            } else {
              schemaUrl = urlList.singleUrlList?.schemaUrl ?? "";
              h5Url = urlList.singleUrlList?.mobileUrl ??
                  urlList.singleUrlList?.mobileShortUrl ??
                  "";
            }
          }
          ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
        }
      } else if (resourceType != null || originalLink != null) {
        /// 生成多多进宝频道推广
        var ddkResourceUrl = await ddkResourceChangeUrl(
          resourceType: resourceType,
          originalLink: originalLink,
        );
        var schemaUrl = "";
        var h5Url = "";
        if (ddkResourceUrl?.multiUrlList != null) {
          var splitUrl = ddkResourceUrl?.multiUrlList?.url
              ?.split("//mobile.yangkeduo.com")[1];
          schemaUrl = "pinduoduo://com.xunmeng.pinduoduo$splitUrl";
          h5Url = ddkResourceUrl?.multiUrlList?.url ?? "";
        } else {
          var splitUrl = ddkResourceUrl?.singleUrlList?.url
              ?.split("//mobile.yangkeduo.com")[1];
          schemaUrl = "pinduoduo://com.xunmeng.pinduoduo$splitUrl";
          h5Url = ddkResourceUrl?.singleUrlList?.url ?? "";
        }
        ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
      }
    } else {
      /// 未授权
      var authUrl = await getAuthUrl();
      var schemaUrl = authUrl?.schemaUrl;
      var h5Url = authUrl?.mobileShortUrl ?? authUrl?.mobileUrl;
      if (schemaUrl != null && h5Url != null) {
        PddAuthDialog.authDialog(() {
          ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
        });
      }
    }
    SmartDialog.dismiss();
  }
}

@riverpod
class EleConversion extends _$EleConversion {
  @override
  EleActivity? build() {
    return null;
  }

  /// 饿了么活动转链
  void changeUrlWithEleActivity(
    String? activityId,
  ) async {
    if (activityId != null && activityId.isEmpty) return;
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ChainTransferService.getPromotionOfficialActivity(
      activityId: activityId,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("changeUrlWithEleActivity: $result");

      /// 跳转饿了么或者h5
      if (result.data != null) {
        var schemaUrl = result.data?.eleSchemeUrl ?? "";
        var h5Url = result.data?.h5Url ?? "";
        ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

@riverpod
class MtConversion extends _$MtConversion {
  @override
  String? build() {
    return null;
  }

  /// 美团活动转链 - 原有方法（保持向后兼容）
  void changeUrlWithMtActivity(
    String? actId,
  ) async {
    if (actId != null && actId.isEmpty) return;
    SmartDialog.showLoading(msg: "加载中...");

    /// 判断能否打开美团
    bool canLaunchMt = await canLaunchUrl(Uri.parse("imeituan://"));

    /// 3:deepLink
    int linkType = canLaunchMt ? 3 : 1;
    var result = await ChainTransferService.getReferralLink(
      actId: actId,
      linkType: linkType,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("changeUrlWithMtActivity: $result");

      /// 跳转美团或者h5
      if (result.data != null) {
        String schemaUrl = canLaunchMt ? result.data! : "";
        String h5Url = canLaunchMt ? "" : result.data!;
        ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }

  /// 美团活动转链 - type 20 使用 getMtActivityInfo API
  void mtActivityType20(String? activityId) async {
    if (activityId == null || activityId.isEmpty) {
      debugPrint("mtActivityType20: activityId is null or empty");
      ToastUtil.showToast("活动ID不能为空");
      return;
    }

    // 检查用户登录状态
    final userInfo = ref.read(authProvider);
    if (userInfo?.data == null) {
      debugPrint("mtActivityType20: user not logged in");
      ToastUtil.showToast("请先登录");
      return;
    }

    debugPrint("mtActivityType20: starting conversion for activityId: $activityId");
    SmartDialog.showLoading(msg: "加载中...");

    try {
      var result = await ChainTransferService.getMtActivityInfo(activity: activityId);
      SmartDialog.dismiss();

      if (result.status == Status.completed) {
        debugPrint("mtActivityType20 API response: ${result.data}");
        final data = result.data;

        if (data != null && data['code'] == 200 && data['data'] != null) {
          final inData = data['data'];
          final commonLink = inData['commonLink'] as String?;
          final shortLink = inData['shortLink'] as String?;

          debugPrint("mtActivityType20 links - commonLink: $commonLink, shortLink: $shortLink");

          /// 判断能否打开美团
          bool canLaunchMt = await canLaunchUrl(Uri.parse("imeituan://"));
          debugPrint("mtActivityType20 can launch Meituan: $canLaunchMt");

          String? url = commonLink ?? shortLink; // type 20 优先使用 commonLink，回退到 shortLink
          if (url != null) {
            String schemaUrl = "";
            String h5Url = url;

            if (canLaunchMt) {
              // type 20 构造美团深度链接
              schemaUrl = "imeituan://www.meituan.com/web?url=$url";
            }

            debugPrint("mtActivityType20 final URLs - schemaUrl: $schemaUrl, h5Url: $h5Url");
            ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
          } else {
            debugPrint("mtActivityType20: both commonLink and shortLink are null");
            ToastUtil.showToast("获取跳转链接失败");
          }
        } else {
          debugPrint("mtActivityType20: invalid response data - code: ${data?['code']}, data: ${data?['data']}");
          ToastUtil.showToast("获取活动信息失败");
        }
      } else {
        debugPrint("mtActivityType20: API call failed - ${result.exception?.getMessage()}");
        ToastUtil.showToast(result.exception?.getMessage() ?? "网络请求失败");
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint("mtActivityType20: unexpected error - $e");
      ToastUtil.showToast("转链过程中发生错误：$e");
    }
  }

  /// 美团商品转链 - type 26 使用 getMtActivityInfo API
  void mtGoodsType26(String? activityId) async {
    if (activityId == null || activityId.isEmpty) {
      debugPrint("mtGoodsType26: activityId is null or empty");
      ToastUtil.showToast("活动ID不能为空");
      return;
    }

    // 检查用户登录状态
    final userInfo = ref.read(authProvider);
    if (userInfo?.data == null) {
      debugPrint("mtGoodsType26: user not logged in");
      ToastUtil.showToast("请先登录");
      return;
    }

    debugPrint("mtGoodsType26: starting conversion for activityId: $activityId");
    SmartDialog.showLoading(msg: "加载中...");

    try {
      var result = await ChainTransferService.getMtActivityInfo(activity: activityId);
      SmartDialog.dismiss();

      if (result.status == Status.completed) {
        debugPrint("mtGoodsType26 API response: ${result.data}");
        final data = result.data;

        if (data != null && data['code'] == 200 && data['data'] != null) {
          final inData = data['data'];
          final commonLink = inData['commonLink'] as String?;
          final deepLink = inData['deepLink'] as String?;

          debugPrint("mtGoodsType26 links - commonLink: $commonLink, deepLink: $deepLink");

          /// 判断能否打开美团
          bool canLaunchMt = await canLaunchUrl(Uri.parse("imeituan://"));
          debugPrint("mtGoodsType26 can launch Meituan: $canLaunchMt");

          if (commonLink != null) {
            String schemaUrl = "";
            String h5Url = commonLink;

            if (canLaunchMt) {
              // type 26 直接使用 deepLink 打开
              schemaUrl = deepLink ?? "";
            }

            debugPrint("mtGoodsType26 final URLs - schemaUrl: $schemaUrl, h5Url: $h5Url");
            ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
          } else {
            debugPrint("mtGoodsType26: commonLink is null");
            ToastUtil.showToast("获取跳转链接失败");
          }
        } else {
          debugPrint("mtGoodsType26: invalid response data - code: ${data?['code']}, data: ${data?['data']}");
          ToastUtil.showToast("获取活动信息失败");
        }
      } else {
        debugPrint("mtGoodsType26: API call failed - ${result.exception?.getMessage()}");
        ToastUtil.showToast(result.exception?.getMessage() ?? "网络请求失败");
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint("mtGoodsType26: unexpected error - $e");
      ToastUtil.showToast("转链过程中发生错误：$e");
    }
  }

  /// 兼容方法 - 保持向后兼容，默认按照type 20处理
  void mtActivityWithGetActivityInfo(String? activityId) async {
    mtActivityType20(activityId);
  }

  /// 美团频道转链 - type 34 使用 lmGenerateLink API
  void mtChannelWithGenerateLink(String? activityIdStr) async {
    if (activityIdStr == null || activityIdStr.isEmpty) {
      debugPrint("mtChannelWithGenerateLink: activityIdStr is null or empty");
      ToastUtil.showToast("活动ID不能为空");
      return;
    }

    int? actId = int.tryParse(activityIdStr);
    if (actId == null) {
      debugPrint("mtChannelWithGenerateLink: invalid activityId format: $activityIdStr");
      ToastUtil.showToast("活动ID格式错误");
      return;
    }

    debugPrint("mtChannelWithGenerateLink: starting conversion for actId: $actId");
    SmartDialog.showLoading(msg: "加载中...");

    try {
      /// 判断能否打开美团
      bool canLaunchMt = await canLaunchUrl(Uri.parse("imeituan://"));
      debugPrint("mtChannelWithGenerateLink can launch Meituan: $canLaunchMt");

      /// 根据是否支持APP调整 linkType: 支持时为2，不支持时为1
      int linkType = canLaunchMt ? 2 : 1;
      debugPrint("mtChannelWithGenerateLink using linkType: $linkType");

      var result = await ChainTransferService.lmGenerateLink(
        actId: actId,
        code: 'mtwaimai',
        linkType: linkType,
        shortLink: 1,
      );

      SmartDialog.dismiss();

      if (result.status == Status.completed) {
        debugPrint("mtChannelWithGenerateLink API response: ${result.data}");

        if (result.data != null && result.data!.isNotEmpty) {
          String url = result.data!;
          String schemaUrl = canLaunchMt ? url : "";
          String h5Url = canLaunchMt ? "" : url;

          debugPrint("mtChannelWithGenerateLink final URLs - schemaUrl: $schemaUrl, h5Url: $h5Url");
          ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
        } else {
          debugPrint("mtChannelWithGenerateLink: empty or null response data");
          ToastUtil.showToast("获取推广链接失败");
        }
      } else {
        debugPrint("mtChannelWithGenerateLink: API call failed - ${result.exception?.getMessage()}");
        ToastUtil.showToast(result.exception?.getMessage() ?? "网络请求失败");
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint("mtChannelWithGenerateLink: unexpected error - $e");
      ToastUtil.showToast("转链过程中发生错误：$e");
    }
  }
}

// 唯品会转链
@riverpod
class WphConversion extends _$WphConversion {
  @override
  WphChangeGoods? build() {
    return null;
  }

  /// 唯品会商品转链
  void wphChangeUrl(
    String? skuId,
    String? val, {
    String? adCode,
    String? rid,
  }) async {
    if (skuId != null && skuId.isEmpty) return;
    if (val != null && val.isEmpty) return;
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ChainTransferService.wphChangeUrl(
      skuId!,
      val!,
      adCode: adCode,
      rid: rid,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("wphChangeUrl: $result");

      /// 跳转唯品会或者h5
      if (result.data != null) {
        String schemaUrl = result.data?.deeplinkUrl ?? "";
        schemaUrl = schemaUrl.replaceAll("goodsType", "goodType");
        String h5Url = result.data?.url ?? "";
        debugPrint("wphChangeUrl-result: $schemaUrl");
        debugPrint("wphChangeUrl-result: $h5Url");
        ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

// 抖音转链
@riverpod
class DyConversion extends _$DyConversion {
  @override
  DyChangeGoods? build() {
    return null;
  }

  /// 抖音商品转链
  void dyChangeUrl(String? goodsUrl, String? skuId) async {
    if (goodsUrl == null && skuId == null) return;
    SmartDialog.showLoading(msg: "加载中...");
    var result = await ChainTransferService.dyChangeUrl(goodsUrl ?? skuId!);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      debugPrint("dyChangeUrl: $result");

      /// 跳转抖音或者h5
      if (result.data != null) {
        String schemaUrl = result.data?.dy_deeplink ?? "";
        String h5Url = result.data?.dy_zlink ?? "";
        debugPrint("dyChangeUrl-result: $schemaUrl");
        debugPrint("dyChangeUrl-result: $h5Url");
        ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
      }
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

/// 通过schemaUrl打开app或者打开h5链接
@riverpod
class LaunchApp extends _$LaunchApp {
  @override
  void build() {
    return;
  }

  void launchApp(String schemaUrl, String h5Url) async {
    try {
      // var result = await canLaunchUrl(Uri.parse(schemaUrl));
      // debugPrint("launchApp: $result");
      //
      // await launchUrl(
      //   Uri.parse(schemaUrl),
      //   mode: LaunchMode.platformDefault,
      // );
      if (await canLaunchUrl(Uri.parse(schemaUrl))) {
        debugPrint("launchApp-schemaUrl: $schemaUrl");
        await launchUrl(
          Uri.parse(schemaUrl),
          mode: LaunchMode.platformDefault,
        );
      } else {
        launchBrowser(h5Url);
      }
    } catch (err) {
      debugPrint("launchApp-err: $err");
      launchBrowser(h5Url);
    }
  }

  /// 使用WebView打开
  void launchBrowser(String h5Url) async {
    if (navigatorKey.currentContext == null) return;
    Navigator.pushNamed(
      navigatorKey.currentContext!,
      CsRouter.webPage,
      arguments: ["", h5Url, false],
    );
  }
}
