import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/provider/conversion/link_conversion_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/http/api_response.dart';
import '../../repository/modals/fun/fun_douyin_response.dart';
import '../../repository/modals/fun/fun_meituan_response.dart';
import '../../repository/modals/fun/fun_tab_config.dart';
import '../../repository/service/fun_service.dart';

part 'fun_list_provider.g.dart';

@riverpod
Future<ApiResponse<DouyinLifeResponse>> fetchDouyinLifeList(
    Ref ref, DouyinLifeParams params) async {
  final response = await FunService.fetchDouyinLifeList(params);
  return response;
}

@riverpod
Future<ApiResponse<MeituanCouponResponse?>> fetchMeituanCouponList(
  Ref ref,
  FunTabConfig mainTabConfig,
  FunTabConfig childTabConfig, {
  double? latitude,
  double? longitude,
  int pageNo = 1,
  int pageSize = 10,
  FunSortOption? sortOption,
}) async {
  try {
    final mainTabCode = mainTabConfig.code ?? '';
    if (mainTabCode != "meituan_groupBuying" &&
        mainTabCode != "meituan_sharpshooter") {
      return ApiResponse.completed(null);
    }

    final params = MeituanCouponParams(
      latitude: latitude ?? 39.9042,
      longitude: longitude ?? 116.4074,
      listTopiId: childTabConfig.listTopiId ?? 0,
      platform: childTabConfig.platform ?? 0,
      pageNo: pageNo,
      pageSize: pageSize,
      sortField: sortOption?.sortField,
      ascDescOrder: sortOption?.ascDescOrder,
      bizLine: childTabConfig.bizLine,
    );

    final response = await FunService.fetchMeituanCoupons(params);
    return response;
  } catch (e) {
    return ApiResponse.error(null);
  }
}

@riverpod
Future<FunTabResult?> fetchFunMainTabConfig(Ref ref, String code) async {
  final response = await FunService.fetchTabConfig(code);
  if (response.status == Status.completed) {
    return response.data;
  }
  return null;
}

@riverpod
Future<FunTabResult?> fetchFunChildrenTabConfig(Ref ref, String code) async {
  final response = await FunService.fetchTabConfig(code);
  if (response.status == Status.completed) {
    return response.data;
  }
  return null;
}

@riverpod
class MeituanShareLink extends _$MeituanShareLink {

  @override
  Future<String> build(MeituanCouponItem item) async {
    SmartDialog.showLoading(msg: "加载中...");
    final response = await FunService.fetchMeituanShareLink(item);
    SmartDialog.dismiss();

    if (response.status == Status.completed) {
      return response.data;
    }
    return "";
  }

}

@riverpod
class FunItemClick extends _$FunItemClick {
  @override
  void build() {
    return;
  }
  void meituanGetCoupon(MeituanCouponItem item) async {
    SmartDialog.showLoading(msg: "跳转中...");
    /// 判断能否打开美团
    bool canLaunchMt = await canLaunchUrl(Uri.parse("imeituan://"));
    try {
      var result = await FunService.fetchMeituanGetCoupon(item, canLaunchMt ? 3 : 2);
      SmartDialog.dismiss();

      if (result.status == Status.completed && result.data != null) {
        debugPrint("changeUrlWithMtActivity: $result");

        /// 跳转美团或者h5
        if (result.data != null) {
          String schemaUrl = canLaunchMt ? result.data! : "";
          String h5Url = canLaunchMt ? "" : result.data!;
          ref.read(launchAppProvider.notifier).launchApp(schemaUrl, h5Url);
        }
      } else {
        // 如果请求失败，显示错误提示
        SmartDialog.showToast('获取优惠券失败，请稍后重试');
      }
    } catch (e) {
      SmartDialog.dismiss();
      SmartDialog.showToast('跳转失败：${e.toString()}');
      debugPrint('美团优惠券跳转失败: $e');
    }
  }
}


@riverpod
class ManageFunTabConfig extends _$ManageFunTabConfig {
  late TabStateConfig? _currentTabConfig;

  @override
  Future<TabStateConfig?> build() async {
    final mainTab = await FunService.fetchTabConfig("free_lunch_meituan_tab");
    if (mainTab.status == Status.error) {
      return null;
    }
    final childrenTab =
        await FunService.fetchTabConfig(mainTab.data!.tabs.first.code ?? "");

    _currentTabConfig = TabStateConfig(
        mainTab: mainTab.data!,
        childrenTab: childrenTab.data!,
        currentSort: childrenTab.data?.tabs.first.sort?.first);
    return _currentTabConfig;
  }

  void getChildrenTab(String code) async {
    final childrenTab = await FunService.fetchTabConfig(code);

    if (childrenTab.status == Status.completed) {
      // Update the current TabConfig with new children tab data
      if (_currentTabConfig != null) {
        _currentTabConfig!.childrenTab = childrenTab.data!;
        // 设置默认排序
        _currentTabConfig!.currentSort =
            childrenTab.data?.tabs.first.sort?.first;

        state = AsyncData(_currentTabConfig!);
      }
    }
  }

  // Function to handle click event on the main tab
  void onMainTabClick(String mainTabCode) async {
    final mainTab = await FunService.fetchTabConfig(mainTabCode);
    if (mainTab.status == Status.error) {
      return;
    }

    // Update the main tab and fetch the children tab
    final childrenTab =
        await FunService.fetchTabConfig(mainTab.data!.tabs.first.code ?? "");
    _currentTabConfig =
        TabStateConfig(mainTab: mainTab.data!, childrenTab: childrenTab.data!);

    // Update the state with the new TabConfig
    state = AsyncData(_currentTabConfig!);
  }

  setCurrentSort(FunSortOption sortOption) {
    if (_currentTabConfig != null) {
      _currentTabConfig!.currentSort = sortOption;
      state = AsyncData(_currentTabConfig!);
    }
  }
}

@riverpod
class TabSelectionState extends _$TabSelectionState {
  @override
  TabSelection build() {
    return const TabSelection();
  }

  void setMainTabIndex(int index) {
    state = state.copyWith(
      mainTabIndex: index,
      childTabIndex: 0,
      childTabChildIndex: -1,
    );
  }

  void setChildTabIndex(int index) {
    state = state.copyWith(
      childTabIndex: index,
      childTabChildIndex: -1,
    );
  }

  void setChildTabChildIndex(int index) {
    state = state.copyWith(childTabChildIndex: index);
  }
}

class TabSelection {
  final int mainTabIndex;
  final int childTabIndex;
  final int childTabChildIndex;

  const TabSelection({
    this.mainTabIndex = 0,
    this.childTabIndex = 0,
    this.childTabChildIndex = -1,
  });

  TabSelection copyWith({
    int? mainTabIndex,
    int? childTabIndex,
    int? childTabChildIndex,
  }) {
    return TabSelection(
      mainTabIndex: mainTabIndex ?? this.mainTabIndex,
      childTabIndex: childTabIndex ?? this.childTabIndex,
      childTabChildIndex: childTabChildIndex ?? this.childTabChildIndex,
    );
  }
}

class TabStateConfig {
  FunTabResult mainTab;
  FunTabResult childrenTab;
  FunSortOption? currentSort;

  TabStateConfig({
    required this.mainTab,
    required this.childrenTab,
    this.currentSort = const FunSortOption(),
  });
}
