// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fun_list_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchDouyinLifeListHash() =>
    r'19583144e8ebff31a1ca7bdcf17621262789918b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

typedef FetchDouyinLifeListRef
    = AutoDisposeFutureProviderRef<ApiResponse<DouyinLifeResponse>>;

/// See also [fetchDouyinLifeList].
@ProviderFor(fetchDouyinLifeList)
const fetchDouyinLifeListProvider = FetchDouyinLifeListFamily();

/// See also [fetchDouyinLifeList].
class FetchDouyinLifeListFamily
    extends Family<AsyncValue<ApiResponse<DouyinLifeResponse>>> {
  /// See also [fetchDouyinLifeList].
  const FetchDouyinLifeListFamily();

  /// See also [fetchDouyinLifeList].
  FetchDouyinLifeListProvider call(
    DouyinLifeParams params,
  ) {
    return FetchDouyinLifeListProvider(
      params,
    );
  }

  @override
  FetchDouyinLifeListProvider getProviderOverride(
    covariant FetchDouyinLifeListProvider provider,
  ) {
    return call(
      provider.params,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchDouyinLifeListProvider';
}

/// See also [fetchDouyinLifeList].
class FetchDouyinLifeListProvider
    extends AutoDisposeFutureProvider<ApiResponse<DouyinLifeResponse>> {
  /// See also [fetchDouyinLifeList].
  FetchDouyinLifeListProvider(
    this.params,
  ) : super.internal(
          (ref) => fetchDouyinLifeList(
            ref,
            params,
          ),
          from: fetchDouyinLifeListProvider,
          name: r'fetchDouyinLifeListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchDouyinLifeListHash,
          dependencies: FetchDouyinLifeListFamily._dependencies,
          allTransitiveDependencies:
              FetchDouyinLifeListFamily._allTransitiveDependencies,
        );

  final DouyinLifeParams params;

  @override
  bool operator ==(Object other) {
    return other is FetchDouyinLifeListProvider && other.params == params;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, params.hashCode);

    return _SystemHash.finish(hash);
  }
}

String _$fetchMeituanCouponListHash() =>
    r'9eb799c230cda18cda0cc0d3a24500e291668d9b';
typedef FetchMeituanCouponListRef
    = AutoDisposeFutureProviderRef<ApiResponse<MeituanCouponResponse?>>;

/// See also [fetchMeituanCouponList].
@ProviderFor(fetchMeituanCouponList)
const fetchMeituanCouponListProvider = FetchMeituanCouponListFamily();

/// See also [fetchMeituanCouponList].
class FetchMeituanCouponListFamily
    extends Family<AsyncValue<ApiResponse<MeituanCouponResponse?>>> {
  /// See also [fetchMeituanCouponList].
  const FetchMeituanCouponListFamily();

  /// See also [fetchMeituanCouponList].
  FetchMeituanCouponListProvider call(
    FunTabConfig mainTabConfig,
    FunTabConfig childTabConfig, {
    double? latitude,
    double? longitude,
    int pageNo = 1,
    int pageSize = 10,
    FunSortOption? sortOption,
  }) {
    return FetchMeituanCouponListProvider(
      mainTabConfig,
      childTabConfig,
      latitude: latitude,
      longitude: longitude,
      pageNo: pageNo,
      pageSize: pageSize,
      sortOption: sortOption,
    );
  }

  @override
  FetchMeituanCouponListProvider getProviderOverride(
    covariant FetchMeituanCouponListProvider provider,
  ) {
    return call(
      provider.mainTabConfig,
      provider.childTabConfig,
      latitude: provider.latitude,
      longitude: provider.longitude,
      pageNo: provider.pageNo,
      pageSize: provider.pageSize,
      sortOption: provider.sortOption,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchMeituanCouponListProvider';
}

/// See also [fetchMeituanCouponList].
class FetchMeituanCouponListProvider
    extends AutoDisposeFutureProvider<ApiResponse<MeituanCouponResponse?>> {
  /// See also [fetchMeituanCouponList].
  FetchMeituanCouponListProvider(
    this.mainTabConfig,
    this.childTabConfig, {
    this.latitude,
    this.longitude,
    this.pageNo = 1,
    this.pageSize = 10,
    this.sortOption,
  }) : super.internal(
          (ref) => fetchMeituanCouponList(
            ref,
            mainTabConfig,
            childTabConfig,
            latitude: latitude,
            longitude: longitude,
            pageNo: pageNo,
            pageSize: pageSize,
            sortOption: sortOption,
          ),
          from: fetchMeituanCouponListProvider,
          name: r'fetchMeituanCouponListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchMeituanCouponListHash,
          dependencies: FetchMeituanCouponListFamily._dependencies,
          allTransitiveDependencies:
              FetchMeituanCouponListFamily._allTransitiveDependencies,
        );

  final FunTabConfig mainTabConfig;
  final FunTabConfig childTabConfig;
  final double? latitude;
  final double? longitude;
  final int pageNo;
  final int pageSize;
  final FunSortOption? sortOption;

  @override
  bool operator ==(Object other) {
    return other is FetchMeituanCouponListProvider &&
        other.mainTabConfig == mainTabConfig &&
        other.childTabConfig == childTabConfig &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.pageNo == pageNo &&
        other.pageSize == pageSize &&
        other.sortOption == sortOption;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, mainTabConfig.hashCode);
    hash = _SystemHash.combine(hash, childTabConfig.hashCode);
    hash = _SystemHash.combine(hash, latitude.hashCode);
    hash = _SystemHash.combine(hash, longitude.hashCode);
    hash = _SystemHash.combine(hash, pageNo.hashCode);
    hash = _SystemHash.combine(hash, pageSize.hashCode);
    hash = _SystemHash.combine(hash, sortOption.hashCode);

    return _SystemHash.finish(hash);
  }
}

String _$fetchFunMainTabConfigHash() =>
    r'b632e4e45480305049fb899c75739f58311060c1';
typedef FetchFunMainTabConfigRef = AutoDisposeFutureProviderRef<FunTabResult?>;

/// See also [fetchFunMainTabConfig].
@ProviderFor(fetchFunMainTabConfig)
const fetchFunMainTabConfigProvider = FetchFunMainTabConfigFamily();

/// See also [fetchFunMainTabConfig].
class FetchFunMainTabConfigFamily extends Family<AsyncValue<FunTabResult?>> {
  /// See also [fetchFunMainTabConfig].
  const FetchFunMainTabConfigFamily();

  /// See also [fetchFunMainTabConfig].
  FetchFunMainTabConfigProvider call(
    String code,
  ) {
    return FetchFunMainTabConfigProvider(
      code,
    );
  }

  @override
  FetchFunMainTabConfigProvider getProviderOverride(
    covariant FetchFunMainTabConfigProvider provider,
  ) {
    return call(
      provider.code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchFunMainTabConfigProvider';
}

/// See also [fetchFunMainTabConfig].
class FetchFunMainTabConfigProvider
    extends AutoDisposeFutureProvider<FunTabResult?> {
  /// See also [fetchFunMainTabConfig].
  FetchFunMainTabConfigProvider(
    this.code,
  ) : super.internal(
          (ref) => fetchFunMainTabConfig(
            ref,
            code,
          ),
          from: fetchFunMainTabConfigProvider,
          name: r'fetchFunMainTabConfigProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchFunMainTabConfigHash,
          dependencies: FetchFunMainTabConfigFamily._dependencies,
          allTransitiveDependencies:
              FetchFunMainTabConfigFamily._allTransitiveDependencies,
        );

  final String code;

  @override
  bool operator ==(Object other) {
    return other is FetchFunMainTabConfigProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

String _$fetchFunChildrenTabConfigHash() =>
    r'e4c4b7a81c79f28599ad1416316642f5d8ff0bc7';
typedef FetchFunChildrenTabConfigRef
    = AutoDisposeFutureProviderRef<FunTabResult?>;

/// See also [fetchFunChildrenTabConfig].
@ProviderFor(fetchFunChildrenTabConfig)
const fetchFunChildrenTabConfigProvider = FetchFunChildrenTabConfigFamily();

/// See also [fetchFunChildrenTabConfig].
class FetchFunChildrenTabConfigFamily
    extends Family<AsyncValue<FunTabResult?>> {
  /// See also [fetchFunChildrenTabConfig].
  const FetchFunChildrenTabConfigFamily();

  /// See also [fetchFunChildrenTabConfig].
  FetchFunChildrenTabConfigProvider call(
    String code,
  ) {
    return FetchFunChildrenTabConfigProvider(
      code,
    );
  }

  @override
  FetchFunChildrenTabConfigProvider getProviderOverride(
    covariant FetchFunChildrenTabConfigProvider provider,
  ) {
    return call(
      provider.code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchFunChildrenTabConfigProvider';
}

/// See also [fetchFunChildrenTabConfig].
class FetchFunChildrenTabConfigProvider
    extends AutoDisposeFutureProvider<FunTabResult?> {
  /// See also [fetchFunChildrenTabConfig].
  FetchFunChildrenTabConfigProvider(
    this.code,
  ) : super.internal(
          (ref) => fetchFunChildrenTabConfig(
            ref,
            code,
          ),
          from: fetchFunChildrenTabConfigProvider,
          name: r'fetchFunChildrenTabConfigProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchFunChildrenTabConfigHash,
          dependencies: FetchFunChildrenTabConfigFamily._dependencies,
          allTransitiveDependencies:
              FetchFunChildrenTabConfigFamily._allTransitiveDependencies,
        );

  final String code;

  @override
  bool operator ==(Object other) {
    return other is FetchFunChildrenTabConfigProvider && other.code == code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, code.hashCode);

    return _SystemHash.finish(hash);
  }
}

String _$meituanShareLinkHash() => r'f78920bcdb768ba6be2f226cdc7951ba2dedfcd0';

abstract class _$MeituanShareLink
    extends BuildlessAutoDisposeAsyncNotifier<String> {
  late final MeituanCouponItem item;

  Future<String> build(
    MeituanCouponItem item,
  );
}

/// See also [MeituanShareLink].
@ProviderFor(MeituanShareLink)
const meituanShareLinkProvider = MeituanShareLinkFamily();

/// See also [MeituanShareLink].
class MeituanShareLinkFamily extends Family<AsyncValue<String>> {
  /// See also [MeituanShareLink].
  const MeituanShareLinkFamily();

  /// See also [MeituanShareLink].
  MeituanShareLinkProvider call(
    MeituanCouponItem item,
  ) {
    return MeituanShareLinkProvider(
      item,
    );
  }

  @override
  MeituanShareLinkProvider getProviderOverride(
    covariant MeituanShareLinkProvider provider,
  ) {
    return call(
      provider.item,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'meituanShareLinkProvider';
}

/// See also [MeituanShareLink].
class MeituanShareLinkProvider
    extends AutoDisposeAsyncNotifierProviderImpl<MeituanShareLink, String> {
  /// See also [MeituanShareLink].
  MeituanShareLinkProvider(
    this.item,
  ) : super.internal(
          () => MeituanShareLink()..item = item,
          from: meituanShareLinkProvider,
          name: r'meituanShareLinkProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$meituanShareLinkHash,
          dependencies: MeituanShareLinkFamily._dependencies,
          allTransitiveDependencies:
              MeituanShareLinkFamily._allTransitiveDependencies,
        );

  final MeituanCouponItem item;

  @override
  bool operator ==(Object other) {
    return other is MeituanShareLinkProvider && other.item == item;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, item.hashCode);

    return _SystemHash.finish(hash);
  }

  @override
  Future<String> runNotifierBuild(
    covariant MeituanShareLink notifier,
  ) {
    return notifier.build(
      item,
    );
  }
}

String _$funItemClickHash() => r'e364e0e1312f17ac71fbe12f6e8e4b729d28f517';

/// See also [FunItemClick].
@ProviderFor(FunItemClick)
final funItemClickProvider =
    AutoDisposeNotifierProvider<FunItemClick, void>.internal(
  FunItemClick.new,
  name: r'funItemClickProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$funItemClickHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FunItemClick = AutoDisposeNotifier<void>;
String _$manageFunTabConfigHash() =>
    r'609f4fb17a64a86e39a7c86f7dd2b8d83a7b0e61';

/// See also [ManageFunTabConfig].
@ProviderFor(ManageFunTabConfig)
final manageFunTabConfigProvider = AutoDisposeAsyncNotifierProvider<
    ManageFunTabConfig, TabStateConfig?>.internal(
  ManageFunTabConfig.new,
  name: r'manageFunTabConfigProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$manageFunTabConfigHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ManageFunTabConfig = AutoDisposeAsyncNotifier<TabStateConfig?>;
String _$tabSelectionStateHash() => r'8a250e033dc370bb01f94f0018ea007fe490271e';

/// See also [TabSelectionState].
@ProviderFor(TabSelectionState)
final tabSelectionStateProvider =
    AutoDisposeNotifierProvider<TabSelectionState, TabSelection>.internal(
  TabSelectionState.new,
  name: r'tabSelectionStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$tabSelectionStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TabSelectionState = AutoDisposeNotifier<TabSelection>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
