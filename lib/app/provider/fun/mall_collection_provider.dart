import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:msmds_platform/app/repository/service/fun_service.dart';
import 'package:msmds_platform/common/http/api_response.dart';

import '../../repository/modals/fun/fun_promotion.dart';

part 'mall_collection_provider.g.dart';

/// 商城收藏数据 Provider
@riverpod
Future<List<FunPromotionCollection>> fetchListPromotion(FetchListPromotionRef ref) async {
  final result = await FunService.fetchListPromotionColumn();

  if (result.status == Status.completed) {
    return result.data ?? [];
  }

  throw Exception('获取商城收藏数据失败: ${result.exception}');
}

/// 当前选中的索引 Provider
@riverpod
class MallCollectionSelectedIndex extends _$MallCollectionSelectedIndex {
  @override
  int build() {
    return 0;
  }

  /// 设置选中索引
  void setIndex(int index) {
    state = index;
  }

  /// 重置选中索引
  void reset() {
    state = 0;
  }
}

/// 顶部菜单数据
@riverpod
List<FunPromotionCollection> mallCollectionTopMenuData(MallCollectionTopMenuDataRef ref) {
  final mallDataAsync = ref.watch(fetchListPromotionProvider);

  return mallDataAsync.when(
    data: (data) => data,
    loading: () => [],
    error: (_, __) => [],
  );
}

/// 当前选中分类的商品数据 Provider（派生状态）
// @riverpod
// List<TypeData> mallCollectionSelectedCategoryData(MallCollectionSelectedCategoryDataRef ref) {
//   final mallDataAsync = ref.watch(fetchListPromotionProvider);
//   final selectedIndex = ref.watch(mallCollectionSelectedIndexProvider);

//   return mallDataAsync.when(
//     data: (data) {
//       if (data.isEmpty || selectedIndex >= data.length) return [];
//       return data[selectedIndex].typeData ?? [];
//     },
//     loading: () => [],
//     error: (_, __) => [],
//   );
// }

/// 展开的商城数据 Provider（将所有商品数据展开为扁平列表）
@riverpod
List<MallCollectionContentItem> mallCollectionContentData(MallCollectionContentDataRef ref) {
  final mallDataAsync = ref.watch(fetchListPromotionProvider);

  return mallDataAsync.when(
    data: (data) {
      List<MallCollectionContentItem> displayItems = [];

      for (int categoryIndex = 0; categoryIndex < data.length; categoryIndex++) {
        final category = data[categoryIndex];

        // 添加分类标题
        displayItems.add(MallCollectionContentItem(
          type: MallCollectionItemType.title,
          categoryIndex: categoryIndex,
          title: category.typeName ?? '',
        ));

        // 添加分类下的具体项目
        final typeDataList = category.typeData ?? [];
        for (int itemIndex = 0; itemIndex < typeDataList.length; itemIndex++) {
          final typeData = typeDataList[itemIndex];
          displayItems.add(MallCollectionContentItem(
            type: MallCollectionItemType.item,
            categoryIndex: categoryIndex,
            itemIndex: itemIndex,
            typeData: typeData,
          ));
        }
      }

      return displayItems;
    },
    loading: () => [],
    error: (_, __) => [],
  );
}

/// 商城收藏显示项目类型
enum MallCollectionItemType {
  title,
  item,
}

/// 商城收藏显示项目
class MallCollectionContentItem {
  final MallCollectionItemType type;
  final int categoryIndex;
  final int? itemIndex;
  final String? title;
  final TypeData? typeData;

  MallCollectionContentItem({
    required this.type,
    required this.categoryIndex,
    this.itemIndex,
    this.title,
    this.typeData,
  });

  /// 是否为分类标题
  bool get isTitle => type == MallCollectionItemType.title;

  /// 是否为具体项目
  bool get isItem => type == MallCollectionItemType.item;
}