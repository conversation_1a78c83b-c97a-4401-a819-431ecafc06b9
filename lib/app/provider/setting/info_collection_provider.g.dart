// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'info_collection_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$collectionVersionHash() => r'961ebd635612648ca316283b41c63776954fc41e';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: info_collection_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/5 11:42
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/5 11:42
/// @UpdateRemark: 更新说明
/// 收集版本号信息
///
/// Copied from [CollectionVersion].
@ProviderFor(CollectionVersion)
final collectionVersionProvider =
    AutoDisposeNotifierProvider<CollectionVersion, int>.internal(
  CollectionVersion.new,
  name: r'collectionVersionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$collectionVersionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CollectionVersion = AutoDisposeNotifier<int>;
String _$collectionClipboardHash() =>
    r'c3bfc60771f3c7ce07f385e987a817096c639712';

/// See also [CollectionClipboard].
@ProviderFor(CollectionClipboard)
final collectionClipboardProvider = AutoDisposeNotifierProvider<
    CollectionClipboard, CollectionClipboardData>.internal(
  CollectionClipboard.new,
  name: r'collectionClipboardProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$collectionClipboardHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CollectionClipboard = AutoDisposeNotifier<CollectionClipboardData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member
