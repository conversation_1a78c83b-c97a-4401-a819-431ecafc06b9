import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'info_collection_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: info_collection_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/5 11:42
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/5 11:42
/// @UpdateRemark: 更新说明

/// 收集版本号信息
@riverpod
class CollectionVersion extends _$CollectionVersion {
  @override
  int build() {
    return 0;
  }

  void collection() {
    state = state + 1;
  }
}

/// 粘贴板收集信息
class CollectionClipboardData {
  String? content;
  int num;

  CollectionClipboardData({this.num = 0, this.content});

  CollectionClipboardData copyWith({
    int? num,
    String? content,
  }) {
    return CollectionClipboardData(
      num: num ?? this.num,
      content: content ?? this.content,
    );
  }
}

@riverpod
class CollectionClipboard extends _$CollectionClipboard {
  @override
  CollectionClipboardData build() {
    return CollectionClipboardData(content: null);
  }

  void collection(String? content) {
    state = state.copyWith(num: state.num + 1, content: content);
  }
}
