import 'dart:io';

import 'package:msmds_platform/app/provider/setting/info_collection_provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:path_provider/path_provider.dart';

part 'setting_provider.g.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: setting_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/30 15:06
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/30 15:06
/// @UpdateRemark: 更新说明

/// 版本号
@riverpod
Future<String?> fetchVersion(FetchVersionRef ref) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  ref.watch(collectionVersionProvider.notifier).collection();
  return "v${packageInfo.version}(${packageInfo.buildNumber})";
}

/// 个性化消息推荐
@riverpod
class SettingRecommend extends _$SettingRecommend {
  @override
  bool build() {
    return false;
  }

  void toggle() {
    state = !state;
  }
}

/// 清除缓存
@riverpod
class SettingCleanCache extends _$SettingCleanCache {
  @override
  String build() {
    total();
    return "-";
  }

  /// 获取缓存大小
  void total() async {
    Directory tempDir = await getTemporaryDirectory();
    int total = await _reduce(tempDir);
    state = "${(total / 1024 / 1024).toStringAsFixed(2)}MB";
  }

  /// 递归缓存目录，计算缓存大小
  Future<int> _reduce(final FileSystemEntity file) async {
    /// 如果是一个文件，则直接返回文件大小
    if (file is File) {
      int length = await file.length();
      return length;
    }

    /// 如果是目录，则遍历目录并累计大小
    if (file is Directory) {
      final List<FileSystemEntity> children = file.listSync();

      int total = 0;

      if (children.isNotEmpty) {
        for (final FileSystemEntity child in children) {
          total += await _reduce(child);
        }
      }

      return total;
    }

    return 0;
  }

  /// 清除缓存
  void clear() async {
    Directory tempDir = await getTemporaryDirectory();
    await _delete(tempDir);
    state = "0MB";
  }

  /// 递归删除缓存目录和文件
  Future<void> _delete(FileSystemEntity file) async {
    if (file is Directory) {
      final List<FileSystemEntity> children = file.listSync();
      for (final FileSystemEntity child in children) {
        await _delete(child);
      }
    } else {
      await file.delete();
    }
  }
}
