import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:msmds_platform/app/navigation/router.dart';
import 'package:msmds_platform/app/view/splash/splash_ad_page.dart';

import '../../config/global_config.dart';
import '../../widgets/toast/custom_toast.dart';
import '../lifecycle/router_observer.dart';
import '../provider/theme/locale_provider.dart';
import '../provider/theme/theme_provider.dart';
import '../view/splash/splash_page.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: CooSea
/// @Package:
/// @ClassName: CooSea
/// @Description: APP入口，可以设置一些配置信息，
/// 目前配置了支持多语言，暗黑模式，路由等等
/// @Author: frankylee
/// @CreateDate: 2023/5/17 14:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 14:46
/// @UpdateRemark: 更新说明

/// 创建一个全局的 navigatorKey
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/// 自定义路由监听组件
final CsRouteObserver<PageRoute> csRouteObserver = CsRouteObserver<PageRoute>();

class CooSea extends ConsumerWidget {
  const CooSea({
    Key? key,
    this.navigatorObserver,
  }) : super(key: key);

  final NavigatorObserver? navigatorObserver;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ScreenUtilInit(
      useInheritedMediaQuery: true,
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: "买什么都省",
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          locale: ref.watch(localeSettingProvider),
          theme: lightTheme,
          darkTheme: lightTheme,
          onGenerateRoute: CsRouter.generateRoute,
          navigatorObservers: [
            FlutterSmartDialog.observer,
            csRouteObserver,
          ],
          builder: FlutterSmartDialog.init(
            // builder: (context, child) {
            //   /// 字体大小随系统变化系数
            //   final mediaQueryData = MediaQuery.of(context);
            //   final scale = mediaQueryData.textScaler.clamp(
            //     minScaleFactor: 1.0,
            //     maxScaleFactor: 1.1,
            //   );
            //   return MediaQuery(
            //     data: MediaQuery.of(context).copyWith(textScaler: scale),
            //     child: child ?? Container(),
            //   );
            // },
            toastBuilder: (String msg) => CustomToast(msg: msg),
          ),
          home: GlobalConfig.agreePrivacy == true
              ? const SplashAdPage()
              : const SplashPage(),
        );
      },
    );
  }
}
