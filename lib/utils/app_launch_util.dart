import 'package:url_launcher/url_launcher.dart';

class AppLaunchUtil {
  /// 美团 app 的 URL Scheme
  static const String _meituanScheme = 'imeituan://www.meituan.com';

  /// 检查美团 app 是否可以打开
  static Future<bool> canOpenMeituan() async {
    try {
      final uri = Uri.parse(_meituanScheme);
      return await canLaunchUrl(uri);
    } catch (e) {
      return false;
    }
  }

  /// 打开美团 app 或跳转到指定链接
  static Future<bool> launchMeituan(String url) async {
    try {
      final uri = Uri.parse(url);
      return await launchUrl(uri);
    } catch (e) {
      return false;
    }
  }

  /// 打开 URL（通用方法）
  static Future<bool> launchAppUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      return await launchUrl(uri);
    } catch (e) {
      return false;
    }
  }
}