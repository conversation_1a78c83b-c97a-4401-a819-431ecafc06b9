import 'package:flutter/material.dart';

import '../app/navigation/router.dart';
import '../config/global_config.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: login_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 16:25
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 16:25
/// @UpdateRemark: 更新说明
class RouterUtil {
  RouterUtil._internal();

  static final RouterUtil _instance = RouterUtil._internal();

  factory RouterUtil() {
    return _instance;
  }

  /// 检查登录状态,未登录跳转登录页
  /// call: 需要登录才能执行的方法
  /// execute：true（登录成功后继续执行后续方法）false（则不执行）
  ///
  /// 例如：我的页面需要登录才能切换，execute传入true时，
  /// 当点击我的时，拉起登录页，登录成功后继续切换到我的页面
  static void checkLogin(
    BuildContext context, {
    Function? call,
    bool execute = false,
    Function? loginCall,
  }) async {
    if (GlobalConfig.account != null) {
      call?.call();
    } else {
      if (loginCall != null) {
        /// 自定义登录方法
        loginCall.call();
      } else {
        /// 默认登录页
        await Navigator.pushNamed(context, CsRouter.login);
        if (execute && GlobalConfig.account != null) {
          call?.call();
        }
      }
    }
  }

  /// 退出登录
  static void exitLogin(BuildContext context) {
    Navigator.popUntil(context, (router) {
      bool isBack = true;
      if (router.settings.name == CsRouter.verification) {
        isBack = false;
      }
      if (router.settings.name == CsRouter.login) {
        isBack = false;
      }
      return isBack;
    });
  }
}
