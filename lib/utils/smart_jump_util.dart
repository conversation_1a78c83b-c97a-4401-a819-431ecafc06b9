import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:msmds_platform/app/provider/conversion/link_conversion_provider.dart';
import 'package:msmds_platform/app/repository/modals/fun/fun_promotion.dart';
import 'package:msmds_platform/utils/toast_util.dart';

import '../app/navigation/coosea.dart';
import '../app/navigation/router.dart';
import '../app/repository/service/chain_transfer_service.dart';

/// 类型别名，兼容原有的跳转数据结构
typedef FunPromotionJump = Jump;

/// 跳转类型枚举 - 对应RN中的type
enum JumpType {
  internal(1), // 内部跳转
  webView(2), // 跳H5页面
  taobaoSdk(3), // SDK跳转淘宝
  jdSdk(4), // SDK跳转京东
  wechatShare(5), // 微信分享-动态配置参数
  miniProgram(6), // 跳小程序
  internalWithParams(7), // 内部跳转-动态配置参数
  broadcast(8), // 内部广播
  reserved9(9), // 保留
  taobaoActivity(10), // 淘宝跳转链接添加转链
  jdActivity(11), // 京东跳转链接添加转链
  eleActivity(12), // 饿了么买菜专用
  feedback(13), // 用户反馈
  lifeCoupon(14), // 必应鸟
  discountRadar(15), // 优惠雷达
  urlLauncher(16), // Linking跳转
  reserved17(17), // 保留
  vipShop(18), // 唯品会
  gameCenter(19), // 游戏中心
  mtActivity(20), // 美团活动
  wechatMini2(21), // 微信小程序2
  sunning(22), // 苏宁
  pddActivity(23), // 拼多多活动
  jdGoods(24), // 京东商品
  jdActivityGoods(25), // 京东活动商品
  mtGoods(26), // 美团商品
  reserved27(27), // 保留
  pddGoods(28), // 拼多多商品
  pddActivity2(29), // 拼多多活动2
  reserved30(30), // 保留
  reserved31(31), // 保留
  reserved32(32), // 保留
  pddChannel(33), // 拼多多频道
  mtChannel(34), // 美团频道
  thirdParty(35), // 第三方平台
  pddPromo(36), // 拼多多推广
  douyin(37), // 抖音
  activityWeb(38); // 活动网页

  const JumpType(this.value);
  final int value;

  static JumpType? fromValue(int? value) {
    if (value == null) return null;
    try {
      return JumpType.values.firstWhere(
        (type) => type.value == value,
      );
    } catch (e) {
      return JumpType.urlLauncher;
    }
  }
}

/// 智能跳转工具类
/// 根据RN版本的smartJump逻辑转换为Flutter实现
class SmartJumpUtil {
  SmartJumpUtil._();

  /// 获取平台名称
  static String getPlatformName(JumpType? type) {
    switch (type) {
      case JumpType.taobaoSdk:
      case JumpType.taobaoActivity:
      case JumpType.eleActivity:
      case JumpType.discountRadar:
        return '淘宝';
      case JumpType.jdSdk:
      case JumpType.jdActivity:
      case JumpType.jdGoods:
      case JumpType.jdActivityGoods:
        return '京东';
      case JumpType.wechatShare:
      case JumpType.miniProgram:
      case JumpType.wechatMini2:
        return '微信';
      case JumpType.mtActivity:
      case JumpType.mtGoods:
      case JumpType.mtChannel:
        return '美团';
      case JumpType.vipShop:
        return '唯品会';
      case JumpType.sunning:
        return '苏宁';
      case JumpType.pddActivity:
      case JumpType.pddGoods:
      case JumpType.pddActivity2:
      case JumpType.pddChannel:
      case JumpType.pddPromo:
        return '拼多多';
      case JumpType.douyin:
        return '抖音';
      default:
        return '';
    }
  }

  /// 判断是否需要确认跳转弹窗
  static bool needsConfirmDialog(JumpType? type) {
    return type != null &&
        ![
          JumpType.internal,
          JumpType.webView,
          JumpType.internalWithParams,
          JumpType.broadcast,
          JumpType.feedback,
          JumpType.lifeCoupon,
          JumpType.urlLauncher,
          JumpType.gameCenter,
          JumpType.activityWeb,
        ].contains(type);
  }

  /// 智能跳转主方法
  static Future<void> smartJump({
    required WidgetRef ref,
    required BuildContext context,
    required FunPromotionJump jumpData,
    String? fallbackUrl,
    bool isReturnUrl = false,
  }) async {
    final jumpType = JumpType.fromValue(jumpData.type);
    final url = fallbackUrl;

    if (jumpType == null) {
      debugPrint('SmartJump: unknown jump type ${jumpData.type}');
      ToastUtil.showToast('不支持的跳转类型');
      return;
    }

    debugPrint('SmartJump: type=${jumpType.value}, url=$url');

    // 检查是否需要确认弹窗
    if (!isReturnUrl && needsConfirmDialog(jumpType)) {
      final platformName = getPlatformName(jumpType);
      if (platformName.isNotEmpty && context.mounted) {
        await _showConfirmDialog(
          context,
          platformName,
          () => _executeJump(ref, jumpData, jumpType, url, isReturnUrl),
        );
        return;
      }
    }

    // 直接执行跳转
    await _executeJump(ref, jumpData, jumpType, url, isReturnUrl);
  }

  /// 显示确认跳转弹窗
  static Future<void> _showConfirmDialog(
    BuildContext context,
    String platformName,
    VoidCallback onConfirm,
  ) async {
    if (!context.mounted) return;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: Text('即将离开买什么都省，打开$platformName'),
        actions: [
          TextButton(
            style: TextButton.styleFrom(backgroundColor: Colors.grey),
            onPressed: () => Navigator.pop(context),
            child: const Text('取消', style: TextStyle(color: Colors.black26)),
          ),
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: Colors.orange,
            ),
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            child: const Text('确认', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 执行具体跳转逻辑
  static Future<void> _executeJump(
    WidgetRef ref,
    FunPromotionJump jumpData,
    JumpType jumpType,
    String? url,
    bool isReturnUrl,
  ) async {
    print("-------------${jumpType.value}, $url");
    print("-------------$jumpType, $url");
    if (url == null || url.isEmpty) {
      debugPrint('SmartJump: url is null or empty');
      ToastUtil.showToast('跳转链接为空');
      return;
    }

    try {
      switch (jumpType) {
        case JumpType.taobaoSdk:
        case JumpType.taobaoActivity:
        case JumpType.eleActivity:
        case JumpType.discountRadar:
          await _handleTaobaoJump(ref, jumpType, url);
          break;

        case JumpType.jdSdk:
        case JumpType.jdActivity:
        case JumpType.jdGoods:
        case JumpType.jdActivityGoods:
          await _handleJdJump(ref, jumpType, url);
          break;

        case JumpType.pddActivity:
        case JumpType.pddGoods:
        case JumpType.pddActivity2:
        case JumpType.pddChannel:
        case JumpType.pddPromo:
          await _handlePddJump(ref, jumpType, url);
          break;

        case JumpType.mtActivity:
        case JumpType.mtGoods:
        case JumpType.mtChannel:
          await _handleMeituanJump(ref, url);
          break;

        case JumpType.vipShop:
          await _handleVipShopJump(ref, url);
          break;

        case JumpType.douyin:
          await _handleDouyinJump(ref, url);
          break;

        case JumpType.wechatShare:
        case JumpType.miniProgram:
        case JumpType.wechatMini2:
          await _handleWechatJump(jumpType, url);
          break;

        case JumpType.internal:
        case JumpType.webView:
        case JumpType.internalWithParams:
        case JumpType.broadcast:
        case JumpType.feedback:
        case JumpType.urlLauncher:
        case JumpType.activityWeb:
          await _handleOtherJump(jumpType, url);
          break;

        default:
          ToastUtil.showToast('暂不支持该跳转类型');
          debugPrint('SmartJump: Unsupported jump type: $jumpType');
      }
    } catch (e) {
      debugPrint('SmartJump error: $e');
      ToastUtil.showToast('跳转失败，请稍后重试');
    }
  }

  /// 处理淘宝相关跳转
  static Future<void> _handleTaobaoJump(
    WidgetRef ref,
    JumpType jumpType,
    String url,
  ) async {
    switch (jumpType) {
      case JumpType.taobaoSdk:
        ref.read(tbConversionProvider.notifier).tbChangeUrlByGoodsId(url);
        break;
      case JumpType.eleActivity:
        ref.read(eleConversionProvider.notifier).changeUrlWithEleActivity(url);
        break;
      default:
        ToastUtil.showToast('淘宝跳转: $url');
    }
  }

  /// 处理京东相关跳转
  static Future<void> _handleJdJump(
    WidgetRef ref,
    JumpType jumpType,
    String url,
  ) async {
    switch (jumpType) {
      case JumpType.jdSdk:
      case JumpType.jdGoods:
        ref.read(jdConversionProvider.notifier).jdChangeUrl(url);
        break;
      case JumpType.jdActivity:
      case JumpType.jdActivityGoods:
        ref.read(jdConversionProvider.notifier).changeUrlWithJdActivityUrl(url);
        break;
      default:
        ToastUtil.showToast('京东跳转: $url');
    }
  }

  /// 处理拼多多相关跳转
  static Future<void> _handlePddJump(
    WidgetRef ref,
    JumpType jumpType,
    String url,
  ) async {
    switch (jumpType) {
      case JumpType.pddGoods:
        ref.read(pddConversionProvider.notifier).changeUrlWithPddSkuId(url);
        break;
      default:
        ToastUtil.showToast('拼多多跳转: $url');
    }
  }

  /// 处理美团相关跳转
  static Future<void> _handleMeituanJump(WidgetRef ref, String url) async {

  }

  /// 处理唯品会跳转
  static Future<void> _handleVipShopJump(WidgetRef ref, String url) async {
    ref.read(wphConversionProvider.notifier).wphChangeUrl(url, url);
  }

  /// 处理抖音跳转
  static Future<void> _handleDouyinJump(WidgetRef ref, String url) async {
    ref.read(dyConversionProvider.notifier).dyChangeUrl(url, null);
  }

  /// 处理微信相关跳转
  static Future<void> _handleWechatJump(JumpType jumpType, String url) async {
    switch (jumpType) {
      case JumpType.wechatShare:
        debugPrint('WeChat share: $url');
        ToastUtil.showToast('微信分享');
        break;
      case JumpType.miniProgram:
      case JumpType.wechatMini2:
        debugPrint('WeChat mini program: $url');
        ToastUtil.showToast('微信小程序跳转');
        break;
      default:
        ToastUtil.showToast('微信跳转');
    }
  }

  /// 处理其他类型跳转
  static Future<void> _handleOtherJump(JumpType jumpType, String url) async {
    switch (jumpType) {
      case JumpType.internal:
        debugPrint('Internal jump: $url');
        ToastUtil.showToast('内部跳转: $url');
        break;
      case JumpType.webView:
        debugPrint('WebView jump: $url');
        if (navigatorKey.currentContext == null) return;
        Navigator.pushNamed(
          navigatorKey.currentContext!,
          CsRouter.webPage,
          arguments: ["", url, false],
        );
        ToastUtil.showToast('网页跳转: $url');
        break;
      case JumpType.internalWithParams:
        debugPrint('Internal jump with params: $url');
        ToastUtil.showToast('内部跳转(带参数): $url');
        break;
      case JumpType.broadcast:
        debugPrint('Broadcast: $url');
        ToastUtil.showToast('内部广播: $url');
        break;
      case JumpType.feedback:
        debugPrint('User feedback');
        ToastUtil.showToast('用户反馈');
        break;
      case JumpType.urlLauncher:
        debugPrint('URL launcher: $url');
        ToastUtil.showToast('外部链接跳转: $url');
        break;
      case JumpType.activityWeb:
        debugPrint('Activity web jump: $url');
        ToastUtil.showToast('活动网页跳转: $url');
        break;
      default:
        ToastUtil.showToast('其他跳转: $url');
    }
  }
}
