import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../widgets/toast/custom_toast.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: toast_util
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/30 17:02
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/30 17:02
/// @UpdateRemark: Toast
class ToastUtil {
  /// 显示自定义Toast
  static showToast(String msg) {
    if (msg.isEmpty) return;
    SmartDialog.showToast(msg);
  }

  /// 显示错误自定义Toast
  static showErrorToast(String msg) {
    if (msg.isEmpty) return;
    SmartDialog.showToast("", builder: (context) {
      return CustomToast(msg: msg, color: Colors.red);
    });
  }
}
