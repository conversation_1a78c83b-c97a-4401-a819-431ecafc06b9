import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:msmds_platform/common/img/icon_addres.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package:
/// @ClassName: leading
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/22 10:53
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/22 10:53
/// @UpdateRemark: 更新说明
class Leading extends StatelessWidget {
  const Leading({
    super.key,
    this.color,
    this.onBack,
    this.alpha,
    this.leadingWidget,
  });

  final Color? color;

  final Function()? onBack;

  final int? alpha;

  final Widget? leadingWidget;

  @override
  Widget build(BuildContext context) {
    if (!Navigator.canPop(context)) {
      return leadingWidget ?? Container();
    }

    Widget arrowWidget = Padding(
      padding: const EdgeInsets.only(left: 3),
      child: Image.asset(
        back,
        width: 22.w,
        height: 22.w,
        color: color,
      ),
    );

    if (alpha != null) {
      arrowWidget = Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: Color.fromARGB(125, alpha!, alpha!, alpha!),
          borderRadius: BorderRadius.circular(22),
        ),
        child: Row(
          children: [
            const SizedBox(width: 7),
            Image.asset(
              back,
              width: 22.w,
              height: 22.w,
              color:
              Color.fromARGB(255, 255 - alpha!, 255 - alpha!, 255 - alpha!),
            )
          ],
        ),
      );
    }

    return InkWell(
      onTap: () {
        if (onBack != null) {
          onBack?.call();
        } else {
          Navigator.pop(context);
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          arrowWidget,
        ],
      ),
    );
  }
}
