import 'package:flutter/material.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: tab_image_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:49
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:49
/// @UpdateRemark: 更新说明
class TabImageWidget extends StatelessWidget {
  const TabImageWidget({
    super.key,
    required this.icon,
    required this.size,
  });

  final String icon;

  final double size;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Image.asset(
        icon,
        width: size,
        height: size,
      ),
    );
  }
}
