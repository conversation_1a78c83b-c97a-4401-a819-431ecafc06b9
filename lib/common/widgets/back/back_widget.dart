import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: back_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/20 16:47
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/20 16:47
/// @UpdateRemark: 更新说明
class BackWidget extends StatefulWidget {
  const BackWidget({
    super.key,
    required this.scrollController,
    this.assets,
    this.rate = 0.55,
    this.gradient,
  });

  final ScrollController scrollController;

  final String? assets;

  final double rate;

  final Gradient? gradient;

  @override
  BackWidgetState createState() => BackWidgetState();
}

class BackWidgetState extends State<BackWidget> {
  double _backgroundOffset = 0.0;

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    setState(() {
      if (widget.scrollController.offset < 0) {
        _backgroundOffset = 0;
      } else {
        _backgroundOffset = widget.scrollController.offset;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    Widget child = const SizedBox();
    if (widget.assets != null) {
      child = Image.network(
        widget.assets!,
        width: MediaQuery.of(context).size.width,
        fit: BoxFit.fitWidth,
      );
    } else {
      child = Container(
        height: 250.h,
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          gradient: widget.gradient ??
              const LinearGradient(
                colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFFFFFFF),
                ],
              ),
        ),
      );
    }
    return Positioned(
      top: -_backgroundOffset * widget.rate,
      child: child,
    );
  }
}
