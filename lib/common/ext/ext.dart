extension ExtString on String {
  bool get isValidEmail {
    final emailRegExp = RegExp(r"^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    return emailRegExp.hasMatch(this);
  }

  /// 不能下划线开始或结尾
  bool get isValidName {
    final nameRegExp = RegExp(r"^(?!_)(?!.*?_$)[a-zA-Z0-9_]{6,32}$");
    return nameRegExp.hasMatch(this);
  }

  /// 至少8-16个字符，至少1个大写字母，1个小写字母和1个数字，其他可以是任意字符
  bool get isValidPassword {
    final passwordRegExp =
        RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,16}$');
    return passwordRegExp.hasMatch(this);
  }
}
