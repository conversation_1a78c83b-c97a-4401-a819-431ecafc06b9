class BaseResponse<T> {
  late int code;
  int? errorCode;
  T? data;
  String? msg;

  BaseResponse(
      this.code, this.errorCode, this.data, this.msg);

  factory BaseResponse.fromJson(
          Map<String, dynamic> json, T Function(dynamic json) fromJsonT) =>
      _$BaseResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$BaseResponseToJson(this, toJsonT);
}

BaseResponse<T> _$BaseResponseFromJson<T>(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
    ) =>
    BaseResponse<T>(
      json['code'] as int,
      json['errorCode'] as int?,
      _$nullableGenericFromJson(json['data'], fromJsonT),
      json['msg'] as String?,
    );

Map<String, dynamic> _$BaseResponseToJson<T>(
    BaseResponse<T> instance,
    Object? Function(T value) toJsonT,
    ) =>
    <String, dynamic>{
      'code': instance.code,
      'errorCode': instance.errorCode,
      'data': _$nullableGenericToJson(instance.data, toJsonT),
      'msg': instance.msg,
    };

T? _$nullableGenericFromJson<T>(
    Object? input,
    T Function(Object? json) fromJson,
    ) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
    T? input,
    Object? Function(T value) toJson,
    ) =>
    input == null ? null : toJson(input);
