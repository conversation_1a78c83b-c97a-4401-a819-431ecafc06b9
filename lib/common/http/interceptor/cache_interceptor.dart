import 'dart:collection';

import 'package:dio/dio.dart';

class CacheObject {
  CacheObject(this.response)
      : timestamp = DateTime.now().millisecondsSinceEpoch;

  Response response;
  int timestamp;

  @override
  bool operator ==(Object other) {
    return response.hashCode == other.hashCode;
  }

  @override
  int get hashCode => response.realUri.hashCode;
}

class NetCacheInterceptor extends Interceptor {
  // 为确保迭代器顺序和对象插入时间一致顺序一致，我们使用LinkedHashMap
  LinkedHashMap<String, CacheObject> cache =
      LinkedHashMap<String, CacheObject>();

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // if (Global.profile.cache == null || !Global.profile.cache!.enable) {
    //   return super.onRequest(options, handler);
    // }
    //
    // // refresh标记是否是"下拉刷新"
    // bool refresh = options.extra["refresh"] == true;
    // // 是否磁盘缓存
    // bool cacheDisk = options.extra["cacheDisk"] == true;
    // //如果是下拉刷新，先删除相关缓存
    // if (refresh) {
    //   // 删除uri相同的内存缓存
    //   delete(options.uri.toString());
    //
    //   // 删除磁盘缓存
    //   if (cacheDisk) {
    //     await PrefsUtil().remove(options.uri.toString());
    //   }
    // }
    //
    // // get 请求，开启缓存
    // if (options.extra["noCache"] != true &&
    //     options.method.toLowerCase() == 'get') {
    //   String key = options.extra["cacheKey"] ?? options.uri.toString();
    //   // 策略 1 内存缓存优先，2 然后才是磁盘缓存
    //
    //   // 1 内存缓存
    //   CacheObject? ob = cache[key];
    //   if (ob != null) {
    //     //若缓存未过期，则返回缓存内容
    //     if ((DateTime.now().millisecondsSinceEpoch - ob.timestamp) / 1000 <
    //         Global.profile.cache!.maxAge) {
    //       debugPrint("缓存未过期-----");
    //       return;
    //     } else {
    //       //若已过期则删除缓存，继续向服务器请求
    //       cache.remove(key);
    //     }
    //   }
    //
    //   // 2 磁盘缓存
    //   if (cacheDisk) {
    //     var cacheData = PrefsUtil().getJSON(key);
    //     if (cacheData != null) {
    //       return;
    //     }
    //   }
    // }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    // // 如果启用缓存，将返回结果保存到缓存
    // if (Global.profile.cache!.enable) {
    //   await _saveCache(response);
    // }
    super.onResponse(response, handler);
  }

  // Future<void> _saveCache(Response response) async {
  //   RequestOptions options = response.requestOptions;
  //
  //   // 只缓存 get 的请求
  //   if (options.extra["noCache"] != true &&
  //       options.method.toLowerCase() == "get") {
  //     // 策略：内存、磁盘都写缓存
  //
  //     // 缓存key
  //     String key = options.extra["cacheKey"] ?? options.uri.toString();
  //
  //     // 磁盘缓存
  //     if (options.extra["cacheDisk"] == true) {
  //       await PrefsUtil().setJSON(key, response.data);
  //     }
  //
  //     // 内存缓存
  //     // 如果缓存数量超过最大数量限制，则先移除最早的一条记录
  //     if (cache.length == Global.profile.cache!.maxCount) {
  //       cache.remove(cache[cache.keys.first]);
  //     }
  //
  //     cache[key] = CacheObject(response);
  //   }
  // }

  void delete(String key) {
    cache.remove(key);
  }
}
