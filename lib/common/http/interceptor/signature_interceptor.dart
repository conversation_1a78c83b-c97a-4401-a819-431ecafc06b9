import 'package:dio/dio.dart';

/// 使用拦截器在options的headers中加入签名
/// 签名方式：
///

class SignatureInterceptor extends Interceptor {
  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // Map<String, dynamic> info = {};
    //
    // PackageInfo packageInfo = await PackageInfo.fromPlatform();
    //
    // String version = packageInfo.version;
    //
    // int timestamp = DateTime.now().millisecondsSinceEpoch;
    //
    // String str = "timestamp=$timestamp&version=$version";
    //
    // String signStr = await encodeStringRsa(str);
    //
    // info["Scope"] = signStr;
    // options.headers.addAll(info);
    // debugPrint('SignatureInterceptor: ${options.headers}');
    super.onRequest(options, handler);
  }

  /// 分段加密实现
  // static Future<String> encodeStringRsa(String content) async {
  //   if (GlobalConfig.encrypter != null) {
  //     List<int> sourceBytes = utf8.encode(content);
  //     int inputLen = sourceBytes.length;
  //     int maxLen = 117;
  //     List<int> totalBytes = [];
  //     for (var i = 0; i < inputLen; i += maxLen) {
  //       int endLen = inputLen - i;
  //       List<int> item;
  //       if (endLen > maxLen) {
  //         item = sourceBytes.sublist(i, i + maxLen);
  //       } else {
  //         item = sourceBytes.sublist(i, i + endLen);
  //       }
  //       totalBytes.addAll(GlobalConfig.encrypter!.encryptBytes(item).bytes);
  //     }
  //     return base64.encode(totalBytes);
  //   }
  //   return "";
  // }

// /// 分段解密实现
// static Future<String> decodeString(String content) async {
//   var publicKeyStr = await rootBundle.loadString('assets/rsa/private.pem');
//   var publicKey = ce.RSAKeyParser().parse(publicKeyStr);
//   final encrypter =
//       ce.Encrypter(ce.RSA(privateKey: publicKey as RSAPrivateKey));
//
//   Uint8List sourceBytes = base64.decode(content);
//   int inputLen = sourceBytes.length;
//   int maxLen = 128;
//   List<int> totalBytes = [];
//   for (var i = 0; i < inputLen; i += maxLen) {
//     int endLen = inputLen - i;
//     Uint8List item;
//     if (endLen > maxLen) {
//       item = sourceBytes.sublist(i, i + maxLen);
//     } else {
//       item = sourceBytes.sublist(i, i + endLen);
//     }
//     totalBytes.addAll(encrypter.decryptBytes(ce.Encrypted(item)));
//   }
//   return utf8.decode(totalBytes);
// }
}
