import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: log_interceptor
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/11 15:57
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/11 15:57
/// @UpdateRemark: 更新说明
class HttpLogInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    debugPrint('===============Request Start==============>>>>>>');
    debugPrint('headers: ${options.headers}');
    debugPrint('path: ${options.baseUrl}${options.path}');
    debugPrint('method: ${options.method}');
    debugPrint('parameters: ${options.queryParameters}');
    debugPrint('data: ${options.data}');
    debugPrint('===============Request End==============>>>>>>');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    debugPrint('===============Response Start==============>>>>>>');
    debugPrint('headers: ${response.requestOptions.headers}');
    debugPrint('path: ${response.realUri}');
    debugPrint('statusCode: ${response.statusCode}');
    debugPrint('data: ${response.data}');
    debugPrint('extra: ${response.extra}');
    debugPrint('statusMessage: ${response.statusMessage}');
    debugPrint('===============Response End==============>>>>>>');
    super.onResponse(response, handler);
  }
}
