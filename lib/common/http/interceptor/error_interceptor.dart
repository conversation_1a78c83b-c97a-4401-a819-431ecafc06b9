import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../app_exceptions.dart';
import '../base/base_response.dart';

/// 错误处理拦截器
class ErrorInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    /// 解析自定义错误码，200则放行
    BaseResponse br = BaseResponse.fromJson(response.data, (json) => null);
    debugPrint("ErrorInterceptor: ${br.code}, ${br.data}, ${br.errorCode}, ${br.msg}");
    if (br.code == 200 || br.code == 2000 || br.code == 4000) {
      super.onResponse(response, handler);
    } else {
      throw DioException(
          type: DioExceptionType.badResponse,
          requestOptions: response.requestOptions,
          response: response,
          error: br);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    try {
      if (err.response != null &&
          err.response!.data != null &&
          err.response?.statusCode != 404) {
        BaseResponse br =
            BaseResponse.fromJson(err.response!.data, (json) => null);
        err = DioException(
          type: DioExceptionType.badResponse,
          requestOptions: err.requestOptions,
          response: err.response,
          error: AppException(br.code, br.msg, br.errorCode),
        );
      } else if (err.error is SocketException) {
        err = DioException(
          type: DioExceptionType.connectionError,
          requestOptions: err.requestOptions,
          response: err.response,
          error: AppException.create(err, DioExceptionType.connectionError),
        );
      } else {
        /// error统一处理
        err = DioException(
          type: err.type,
          requestOptions: err.requestOptions,
          response: err.response,
          error: AppException.create(err, err.type),
        );
      }
      super.onError(err, handler);
    } catch (e) {
      err = DioException(
        type: DioExceptionType.unknown,
        requestOptions: err.requestOptions,
        response: err.response,
        error: AppException.create(err, DioExceptionType.unknown),
      );
      super.onError(err, handler);
    }
  }
}
