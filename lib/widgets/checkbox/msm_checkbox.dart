import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: msm_checkbox
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 18:39
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 18:39
/// @UpdateRemark: checkbox

class MsmCheckbox extends StatefulWidget {
  const MsmCheckbox({
    super.key,
    required this.onChanged,
    this.value = false,
  });

  final Function(bool) onChanged;

  final bool value;

  @override
  MsmCheckboxState createState() => MsmCheckboxState();
}

class MsmCheckboxState extends State<MsmCheckbox> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onChanged.call(!widget.value);
      },
      child: Container(
        width: 13.w,
        height: 13.w,
        margin: const EdgeInsets.only(top: 4),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2),
          border: Border.all(color: const Color(0xFF333333), width: 1),
        ),
        child: widget.value
            ? Icon(
                Icons.check,
                size: 10.w,
                color: const Color(0xFF333333),
              )
            : Container(),
      ),
    );
  }
}
