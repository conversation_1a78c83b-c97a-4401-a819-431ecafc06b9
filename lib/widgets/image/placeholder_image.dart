import 'package:flutter/cupertino.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: placeholder_image
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/30 14:07
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/30 14:07
/// @UpdateRemark: 更新说明
class PlaceholderImage extends StatelessWidget {
  const PlaceholderImage({
    super.key,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.image,
  });

  final double? width;
  final double? height;
  final BoxFit? fit;
  final String? placeholder;
  final String? image;

  @override
  Widget build(BuildContext context) {
    if (image == null || (image != null && image!.isEmpty)) {
      return SizedBox(
        width: width,
        height: height,
      );
    }

    return FadeInImage.assetNetwork(
      width: width,
      height: height,
      fit: fit,
      placeholder: placeholder ?? "assets/images/wallet/fill_round.png",
      image: image!,
      placeholderErrorBuilder: (context, o, s) {
        return SizedBox(
          width: width,
          height: height,
        );
      },
      imageErrorBuilder: (context, o, s) {
        return SizedBox(
          width: width,
          height: height,
        );
      },
    );
  }
}
