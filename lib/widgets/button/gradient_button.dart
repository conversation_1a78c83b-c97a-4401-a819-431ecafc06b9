import 'package:flutter/material.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: togrow
/// @Package:
/// @ClassName: gradient_button
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/10/25 14:20
/// @UpdateUser: frankylee
/// @UpdateData: 2022/10/25 14:20
/// @UpdateRemark: 渐变按钮
class GradientButton extends StatelessWidget {
  const GradientButton({
    super.key,
    this.padding = EdgeInsets.zero,
    this.margin = EdgeInsets.zero,
    required this.child,
    required this.gradient,
    this.onPress,
    this.radius = 8,
    this.enable = true,
    this.shadow = true,
    this.boxShadow,
    this.border,
  });

  final EdgeInsets padding;

  final EdgeInsets margin;

  final Widget child;

  final Gradient gradient;

  final double radius;

  final Function? onPress;

  final bool enable;

  final bool shadow;

  final List<BoxShadow>? boxShadow;

  final BoxBorder? border;

  @override
  Widget build(BuildContext context) {
    Gradient newGradient = gradient;
    if (!enable) {
      newGradient = LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            gradient.colors[0].withAlpha(120),
            gradient.colors[0].withAlpha(120),
          ]);
    }

    List<BoxShadow>? newBoxShadow;
    if (shadow) {
      newBoxShadow = [
        //阴影
        BoxShadow(
          color: enable ? gradient.colors[0] : gradient.colors[0].withAlpha(0),
          offset: const Offset(1.0, 1.0),
          blurRadius: 4.0,
        )
      ];
      if (boxShadow != null) {
        newBoxShadow = boxShadow;
      }
    }

    return Container(
      margin: margin,
      decoration: BoxDecoration(
        gradient: newGradient,
        boxShadow: newBoxShadow,
        borderRadius: BorderRadius.circular(radius),
        border: border,
      ),
      child: Material(
        type: MaterialType.transparency,
        child: InkWell(
          // splashColor: gradient.colors[0],
          borderRadius: BorderRadius.circular(radius),
          onTap: !enable ? null : () => {onPress?.call()},
          child: Container(
            padding: padding,
            alignment: Alignment.center,
            child: child,
          ),
        ),
      ),
    );
  }
}
