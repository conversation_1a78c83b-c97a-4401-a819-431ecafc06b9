/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: custom_stepper2
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/27 16:19
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/27 16:19
/// @UpdateRemark: 更新说明

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomStep {
  String name;
  bool active;

  CustomStep(this.name, this.active);
}

class CustomStepper extends StatefulWidget {
  final List<CustomStep> list;
  final Color bgColor;
  final Color progressColor;

  const CustomStepper({
    Key? key,
    required this.list,
    this.bgColor = Colors.black54,
    this.progressColor = Colors.blue,
  }) : super(key: key);

  @override
  CustomStepperState createState() => CustomStepperState();
}

class CustomStepperState extends State<CustomStepper> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: RepaintBoundary(
        // 绘制边界，不与其他层同时绘制
        child: CustomPaint(
          // 使用CustomPaint
          painter: PolyChartPainter(
              list: widget.list,
              bgColor: widget.bgColor,
              progressColor: widget.progressColor),
        ),
      ),
    );
  }
}

class PolyChartPainter extends CustomPainter {
  final List<CustomStep> list;

  final Color bgColor;
  final Color progressColor;

  Paint progressPaint = Paint()
    ..style = PaintingStyle.fill
    ..isAntiAlias = true
    ..color = Colors.grey.withAlpha(60);

  final TextPainter _textPainter = TextPainter()
    ..textDirection = TextDirection.ltr;

  PolyChartPainter({
    required this.list,
    this.bgColor = Colors.grey,
    this.progressColor = Colors.blue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    drawPoly(canvas, size);
  }

  void drawPoly(Canvas canvas, Size size) {
    // 绘制进度底色
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromPoints(
          Offset.zero,
          Offset(size.width, 7.h),
        ),
        const Radius.circular(4.0),
      ),
      progressPaint..color = bgColor,
    );

    // 绘制进度色
    var index = list.lastIndexWhere((element) => element.active == true);
    if (index == -1) {
      index = 0;
    }
    var black = 10;
    if (index == list.length - 1) {
      black = 0;
    }
    var progress =
        (black + (size.width / (list.length - 1)) * index) / size.width;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromPoints(
          Offset.zero,
          Offset(size.width * progress, 7.h),
        ),
        const Radius.circular(4.0),
      ),
      progressPaint..color = progressColor,
    );

    // 绘制圆点
    for (int i = 0; i < list.length; i++) {
      var alpha = 5;
      if (i == list.length - 1) {
        alpha = -5;
      }
      var c = Offset(alpha + (size.width / (list.length - 1)) * i, 3.5.h);
      canvas.drawCircle(c, 2.h, progressPaint..color = Colors.white);
    }

    // 绘制文字
    for (int i = 0; i < list.length; i++) {
      var item = list[i];
      _textPainter.text = _buildTextSpan(
        item.name,
        8,
        const Color(0xFF999999),
      );
      _textPainter.layout();
      var w = _textPainter.width;
      if (i == 0) {
        _textPainter.paint(
          canvas,
          const Offset(
            0,
            12,
          ),
        );
      } else if (i == list.length - 1) {
        _textPainter.paint(
          canvas,
          Offset(
            size.width - w,
            12,
          ),
        );
      } else {
        _textPainter.paint(
          canvas,
          Offset(
            (size.width / (list.length - 1)) * i - w / 2 + 5,
            12,
          ),
        );
      }
    }
  }

  TextSpan _buildTextSpan(String name, double fontSize, Color color) {
    return TextSpan(
      text: name,
      style: TextStyle(
        fontSize: fontSize,
        color: color,
      ),
    );
  }

  @override
  bool shouldRepaint(covariant PolyChartPainter oldDelegate) => true;
}
