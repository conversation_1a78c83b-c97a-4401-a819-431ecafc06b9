import 'package:flutter/material.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: custom_toast
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/30 16:42
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/30 16:42
/// @UpdateRemark: 自定义Toast
enum Location {
  /// 顶部
  top,

  /// 中间
  center,

  /// 底部
  bottom,
}

class CustomToast extends StatelessWidget {
  const CustomToast(
      {Key? key,
      required this.msg,
      this.location = Location.center,
      this.color = Colors.black})
      : super(key: key);

  final String msg;

  final Location location;

  final Color color;

  @override
  Widget build(BuildContext context) {
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center;
    double marginTop = 0.0;
    if (location == Location.top) {
      marginTop = MediaQuery.of(context).padding.top + 20;
      mainAxisAlignment = MainAxisAlignment.start;
    }
    double marginBottom = 0.0;
    if (location == Location.bottom) {
      marginBottom = MediaQuery.of(context).padding.bottom +
          MediaQuery.of(context).size.height * 0.2;
      mainAxisAlignment = MainAxisAlignment.end;
    }

    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: mainAxisAlignment,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 24),
          margin: EdgeInsets.fromLTRB(30, marginTop, 30, marginBottom),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            msg,
            style: const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      ],
    );
  }
}
