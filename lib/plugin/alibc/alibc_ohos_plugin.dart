import 'dart:io';

import 'package:flutter/services.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: alibc
/// @ClassName: alibc_ohos_plugin
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/11/6 14:55
/// @UpdateUser: frankylee
/// @UpdateData: 2024/11/6 14:55
/// @UpdateRemark: 更新说明
class AlibcFlutterPlugin {
  final methodChannel = const MethodChannel('alibc_platform_plugin/ohos');

  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }

  Future<void> initAlibc(String version) async {
    if(Platform.isIOS){
      return;
    }
    await methodChannel.invokeMethod('initAlibc', version);
  }

  Future<void> authLogin() async {
    await methodChannel.invokeMethod('authLogin');
  }

  Future<void> logout() async {
    await methodChannel.invokeMethod<void>('logout');
  }

  Future<dynamic> topNativeAccess(String appName, String appId) async {
    final result = await methodChannel.invokeMethod<dynamic>(
      'topNativeAccess',
      {
        'appName': appName,
        'appId': appId,
      },
    );
    return result;
  }

  Future<dynamic> launcherTbByUrl(String url) async {
    final result =
    await methodChannel.invokeMethod<dynamic>('launcherTbByUrl', {'url': url});
    return result;
  }

  Future<dynamic> launcherTbByCode(String skuId, String pid, String pageType,
      String relationId, String flRate, String url) async {
    final result =
    await methodChannel.invokeMethod<dynamic>('launcherTbByCode', {
      'skuId': skuId,
      'pid': pid,
      'pageType': pageType,
      'relationId': relationId,
      'flRate': flRate,
      'url': url
    });
    return result;
  }

  Future<dynamic> launcherTbCart() async {
    final result = await methodChannel.invokeMethod<dynamic>('launcherTbCart');
    return result;
  }
}

