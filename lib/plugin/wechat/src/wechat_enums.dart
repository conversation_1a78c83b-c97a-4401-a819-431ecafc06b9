/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: plugin.wechat.src
/// @ClassName: wechat_enums
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/12/3 10:46
/// @UpdateUser: frankylee
/// @UpdateData: 2024/12/3 10:46
/// @UpdateRemark: 更新说明
/// [WXMiniProgramType.release]正式版
/// [WXMiniProgramType.test]测试版
/// [WXMiniProgramType.preview]预览版
enum WXMiniProgramType {
  release(0),
  test(1),
  preview(2);

  final int value;

  const WXMiniProgramType(this.value);
}

/// [WeChatScene.session]会话
/// [WeChatScene.timeline]朋友圈
/// [WeChatScene.favorite]收藏
enum WeChatScene { session, timeline, favorite }

