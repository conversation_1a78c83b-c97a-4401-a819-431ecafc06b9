/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: plugin.wechat.src.method_channel
/// @ClassName: fluwx_platform_interface
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/12/3 10:51
/// @UpdateUser: frankylee
/// @UpdateData: 2024/12/3 10:51
/// @UpdateRemark: 更新说明
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import '../foundation/arguments.dart';
import '../response/wechat_response.dart';
import 'fluwx_method_channel.dart';

abstract class FluwxPlatform extends PlatformInterface {
  /// Constructs a FluwxPlatform.
  FluwxPlatform() : super(token: _token);

  static final Object _token = Object();

  static FluwxPlatform _instance = MethodChannelFluwx();

  /// The default instance of [FluwxPlatform] to use.
  ///
  /// Defaults to [MethodChannelFluwx].
  static FluwxPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [FluwxPlatform] when
  /// they register themselves.
  static set instance(FluwxPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Stream<WeChatResponse> get responseEventHandler {
    throw UnimplementedError('responseEventHandler has not been implemented.');
  }

  Future<bool> get isWeChatInstalled {
    throw UnimplementedError('isWeChatInstalled has not been implemented.');
  }

  Future<bool> open(OpenType target) {
    throw UnimplementedError('open() has not been implemented.');
  }

  Future<bool> registerApi({
    required String appId,
    bool doOnIOS = true,
    bool doOnAndroid = true,
    String? universalLink,
  }) {
    throw UnimplementedError('registerWxApi() has not been implemented.');
  }

  Future<String?> getExtMsg() {
    throw UnimplementedError('getExtMsg() has not been implemented.');
  }

  Future<bool> share(WeChatShareModel what) {
    throw UnimplementedError('share() has not been implemented.');
  }

  Future<bool> sendAuth(
      {required String scope,
        String state = 'state',
        bool nonAutomatic = false}) {
    throw UnimplementedError('sendAuth() has not been implemented.');
  }

  Future<bool> authByPhoneLogin({
    required String scope,
    String state = 'state',
  }) async {
    throw UnimplementedError('authByPhoneLogin() has not been implemented.');
  }

  Future<bool> authByQRCode({
    required String appId,
    required String scope,
    required String nonceStr,
    required String timestamp,
    required String signature,
    String? schemeData,
  }) async {
    throw UnimplementedError('authByQRCode() has not been implemented.');
  }

  Future<bool> stopAuthByQRCode() async {
    throw UnimplementedError(
        'stopWeChatAuthByQRCode() has not been implemented.');
  }

  Future<bool> pay(PayType which) {
    throw UnimplementedError('pay() has not been implemented.');
  }

  Future<bool> autoDeduct(AutoDeduct data) {
    throw UnimplementedError('autoDeduct() has not been implemented.');
  }

  Future<bool> authBy(AuthType which) {
    throw UnimplementedError('authBy() has not been implemented.');
  }

  Future<void> attemptToResumeMsgFromWx() {
    throw UnimplementedError(
        'attemptToResumeMsgFromWx() has not been implemented.');
  }

  Future<void> selfCheck() {
    throw UnimplementedError('selfCheck() has not been implemented.');
  }

  Future<bool> get isSupportOpenBusinessView async {
    throw UnimplementedError(
        'isSupportOpenBusinessView() has not been implemented.');
  }
}

