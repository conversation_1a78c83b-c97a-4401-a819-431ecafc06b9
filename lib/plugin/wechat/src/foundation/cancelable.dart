/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: plugin.wechat.src.foundation
/// @ClassName: cancelable
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/12/3 10:49
/// @UpdateUser: frankylee
/// @UpdateData: 2024/12/3 10:49
/// @UpdateRemark: 更新说明
import 'package:flutter/foundation.dart';

mixin FluwxCancelable {
  void cancel();
}

class FluwxCancelableImpl implements FluwxCancelable {
  const FluwxCancelableImpl({required this.onCancel});

  final VoidCallback onCancel;

  @override
  void cancel() {
    onCancel();
  }
}

