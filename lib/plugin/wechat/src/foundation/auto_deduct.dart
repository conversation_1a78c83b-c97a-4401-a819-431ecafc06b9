/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: plugin.wechat.src.foundation
/// @ClassName: auto_deduct
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/12/3 10:49
/// @UpdateUser: frankylee
/// @UpdateData: 2024/12/3 10:49
/// @UpdateRemark: 更新说明
part of 'arguments.dart';

class AutoDeduct with _Argument {
  final Map<String, String> queryInfo;
  final int businessType;
  final Map<String, dynamic> _detailData;

  bool get isV2 => _detailData.isEmpty;

  AutoDeduct.detail({
    required String appId,
    required String mchId,
    required String planId,
    required String contractCode,
    required String requestSerial,
    required String contractDisplayAccount,
    required String notifyUrl,
    required String version,
    required String sign,
    required String timestamp,
    String returnApp = '3',
    this.businessType = 12,
  })  : queryInfo = {},
        _detailData = {
          'appid': appId,
          'mch_id': mchId,
          'plan_id': planId,
          'contract_code': contractCode,
          'request_serial': requestSerial,
          'contract_display_account': contractDisplayAccount,
          'notify_url': notifyUrl,
          'version': version,
          'sign': sign,
          'timestamp': timestamp,
          'return_app': returnApp,
          'businessType': businessType
        };

  AutoDeduct.custom({required this.queryInfo, this.businessType = 12})
      : _detailData = {};

  @override
  Map<String, dynamic> get arguments => isV2
      ? {'queryInfo': queryInfo, 'businessType': businessType}
      : _detailData;
}

