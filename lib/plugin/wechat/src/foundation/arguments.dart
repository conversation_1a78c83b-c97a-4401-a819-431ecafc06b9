/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: plugin.wechat.src.foundation
/// @ClassName: arguments
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/12/3 10:47
/// @UpdateUser: frankylee
/// @UpdateData: 2024/12/3 10:47
/// @UpdateRemark: 更新说明
import 'dart:io';
import 'dart:typed_data';

import '../wechat_enums.dart';
import '../wechat_file.dart';

part 'auth_type.dart';

part 'auto_deduct.dart';

part 'open_type.dart';

part 'pay_type.dart';

part 'share_models.dart';

mixin _Argument {
  Map<String, dynamic> get arguments => {};
}

