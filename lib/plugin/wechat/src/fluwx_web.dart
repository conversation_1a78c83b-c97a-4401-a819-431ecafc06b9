/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: msmds_platform
/// @Package: plugin.wechat.src
/// @ClassName: fluwx_web
/// @Description: 
/// @Author: frankylee
/// @CreateDate: 2024/12/3 10:46
/// @UpdateUser: frankylee
/// @UpdateData: 2024/12/3 10:46
/// @UpdateRemark: 更新说明
// In order to *not* need this ignore, consider extracting the "web" version
// of your plugin as a separate package, instead of inlining it in the same
// package as the core of your plugin.
// ignore: avoid_web_libraries_in_flutter
import 'package:flutter_web_plugins/flutter_web_plugins.dart';

import 'method_channel/fluwx_platform_interface.dart';

/// A web implementation of the FluwxPlatform of the Fluwx plugin.
class FluwxWeb extends FluwxPlatform {
  /// Constructs a FluwxWeb
  FluwxWeb();

  static void registerWith(Registrar registrar) {
    FluwxPlatform.instance = FluwxWeb();
  }
}

