name: msmds_platform
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.6.6+790

environment:
  sdk: '>=2.19.6 <3.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  intl: any

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  # shared preferences
  shared_preferences:
    git:
      url: https://gitee.com/openharmony-sig/flutter_packages
      path: "packages/shared_preferences/shared_preferences"

  # riverpod
  # 生成代码运行：flutter pub run build_runner watch
  # 版本冲突有时需要：flutter pub run build_runner watch --delete-conflicting-outputs
  flutter_riverpod: ^2.2.0
  riverpod_annotation: ^2.0.0

  # screen adapting
  flutter_screenutil: ^5.7.0

  # dio network
  dio: ^5.1.2

  # smart dialog
  flutter_smart_dialog: ^4.9.0+6

  # json generator
  # 生成代码运行：flutter pub run build_runner build
  json_annotation: ^4.8.1

  # webview
  flutter_inappwebview:
    git:
      url: https://gitee.com/openharmony-sig/flutter_inappwebview.git
      path: "flutter_inappwebview"

  # url launcher
  # url = url.replace('showgoodsdetail', 'showGoodsDetail');
  url_launcher:
    git:
      url: https://gitee.com/openharmony-sig/flutter_packages
      path: "packages/url_launcher/url_launcher"

  # 获取app包信息
  package_info_plus:
    git:
      url: https://gitee.com/openharmony-sig/flutter_plus_plugins.git
      path: "packages/package_info_plus/package_info_plus"

  # indicator
  smooth_page_indicator: ^1.1.0

  # app设置
  app_settings: ^4.3.0

  # 文件路径
  path_provider:
    git:
      url: https://gitee.com/openharmony-sig/flutter_packages
      path: "packages/path_provider/path_provider"

  # app links 、deep link
  app_links: ^3.4.5
  card_swiper: ^3.0.1

  # image_gallery_saver
  image_gallery_saver:
    git:
      url: https://gitee.com/openharmony-sig/flutter_image_gallery_saver

dev_dependencies:
  flutter_test:
    sdk: flutter

  # riverpod module
  build_runner:
  custom_lint:
  riverpod_generator: ^2.2.0
  riverpod_lint: ^1.4.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  json_serializable: ^6.5.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # 启用 generate 标志
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/tabbar/
    - assets/images/sign/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  fonts:
    - family: Ceyyt
      fonts:
        - asset: assets/font/cey.ttf
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
