# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "405666cd3cf0ee0a48d21ec67e65406aad2c726d9fa58840d3375e7bdcd32a07"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "60.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "1952250bd005bacb895a01bf1b4dc00e3ba1c526cf47dca54dfe24979c65f5b3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.12.0"
  analyzer_plugin:
    dependency: transitive
    description:
      name: analyzer_plugin
      sha256: c1d5f167683de03d5ab6c3b53fc9aeefc5d59476e7810ba7bbddff50c6f4392d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.11.2"
  app_links:
    dependency: "direct main"
    description:
      name: app_links
      sha256: eb83c2b15b78a66db04e95132678e910fcdb8dc3a9b0aed0c138f50b2bef0dae
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.5"
  app_settings:
    dependency: "direct main"
    description:
      name: app_settings
      sha256: e6a34735d4ddb24ca9c5fd7e965ec65c8b611cbd3a329152c294f9e9f4bacb33
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.3.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.10.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "3fbda25365741f8251b39f3917fb3c8e286a96fd068a5a242e11c2012d495777"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "757153e5d9cd88253cb13f28c2fb55a537dc31fefd98137549895b5beb7c6169"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "0713a05b0386bd97f9e63e78108805a4feca5898a4b821d6610857f10c91e975"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.0"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: b0a8a7b8a76c493e85f1b84bffa0588859a06197863dba8c9036b15581fd9727
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.3"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "0671ad4162ed510b70d0eb4ad6354c249f8429cab4ae7a4cec86bbc2886eb76e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.2.7+1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "69acb7007eb2a31dc901512bfe0f7b767168be34cb734835d54c070bfa74c1b2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.8.0"
  card_swiper:
    dependency: "direct main"
    description:
      name: card_swiper
      sha256: "21e52a144decbf0054e7cfed8bbe46fc89635e6c86b767eaccfe7d5aeba32528"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  ci:
    dependency: transitive
    description:
      name: ci
      sha256: "145d095ce05cddac4d797a158bc4cf3b6016d1fe63d8c3d2fbd7212590adca13"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.0"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: b8db3080e59b2503ca9e7922c3df2072cf13992354d5e944074ffa836fba43b7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "1be9be30396d7e4c0db42c35ea6ccd7cc6a1e19916b5dc64d6ac216b5544d677"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.7.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.17.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.6"
  custom_lint:
    dependency: "direct dev"
    description:
      name: custom_lint
      sha256: "3ce36c04d30c60cde295588c6185b3f9800e6c18f6670a7ffdb3d5eab39bb942"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0"
  custom_lint_builder:
    dependency: transitive
    description:
      name: custom_lint_builder
      sha256: "73d09c9848e9f6d5c3e0a1809eac841a8d7ea123d0849feefa040e1ad60b6d06"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0"
  custom_lint_core:
    dependency: transitive
    description:
      name: custom_lint_core
      sha256: "9170d9db2daf774aa2251a3bc98e4ba903c7702ab07aa438bc83bd3c9a0de57f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: d90ee57923d1828ac14e492ca49440f65477f4bb1263575900be731a3dac66a9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.9.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "0a2e95fc6bdeb623bb623fc41e90e6924e9a3bbd65089f9221f83c185366b479"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.4"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_inappwebview:
    dependency: "direct main"
    description:
      path: flutter_inappwebview
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "6.0.0"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      path: flutter_inappwebview_android
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "1.0.12"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      path: "dev_packages/flutter_inappwebview_internal_annotations"
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "1.1.1"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      path: flutter_inappwebview_ios
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "1.0.13"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      path: flutter_inappwebview_macos
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "1.0.11"
  flutter_inappwebview_ohos:
    dependency: transitive
    description:
      path: flutter_inappwebview_ohos
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "0.0.1"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      path: flutter_inappwebview_platform_interface
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "1.0.10"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      path: flutter_inappwebview_web
      ref: HEAD
      resolved-ref: c653ae23f5f0bd070f3d7a335bc523373612741a
      url: "https://gitee.com/openharmony-sig/flutter_inappwebview.git"
    source: git
    version: "1.0.8"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_riverpod:
    dependency: "direct main"
    description:
      name: flutter_riverpod
      sha256: b6cb0041c6c11cefb2dcb97ef436eba43c6d41287ac6d8ca93e02a497f53a4f3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.7"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "0a122936b450324cbdfd51be0819cc6fcebb093eb65585e9cd92263f7a1a8a39"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.7.0"
  flutter_smart_dialog:
    dependency: "direct main"
    description:
      name: flutter_smart_dialog
      sha256: "0852df132cb03fd8fc5144eb404c31eb7eb50c22aecb1cc2504f2f598090d756"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.9.8+9"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: c3fd9336eb55a38cc1bbd79ab17573113a8deccd0ecbbf926cca3c62803b5c2d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.1"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.0"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  hotreloader:
    dependency: transitive
    description:
      name: hotreloader
      sha256: "728c0613556c1d153f7e7f4a367cffacc3f5a677d7f6497a1c2b35add4e6dacf"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.6"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.13.6"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  image_gallery_saver:
    dependency: "direct main"
    description:
      path: "."
      ref: HEAD
      resolved-ref: b990604be3f945285e399f2d6e76922c6927df2c
      url: "https://gitee.com/openharmony-sig/flutter_image_gallery_saver"
    source: git
    version: "2.0.3"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.8.1"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: "43793352f90efa5d8b251893a63d767b2f7c833120e3cc02adad55eefec04dc7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.6.2"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: "direct main"
    description:
      path: "packages/package_info_plus/package_info_plus"
      ref: HEAD
      resolved-ref: "8361eb10f037f4114dd49040a1e30eecdddf11c7"
      url: "https://gitee.com/openharmony-sig/flutter_plus_plugins.git"
    source: git
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.2"
  path_provider:
    dependency: "direct main"
    description:
      path: "packages/path_provider/path_provider"
      ref: HEAD
      resolved-ref: "9d1662580cd07545628fd86694ceca1872818664"
      url: "https://gitee.com/openharmony-sig/flutter_packages"
    source: git
    version: "2.1.0"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_ohos:
    dependency: transitive
    description:
      path: "packages/path_provider/path_provider_ohos"
      ref: HEAD
      resolved-ref: "9d1662580cd07545628fd86694ceca1872818664"
      url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    source: git
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.3"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: da3fdfeccc4d4ff2da8f8c556704c08f912542c5fb3cf2233ed75372384a034d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.6"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.5.1"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.3"
  riverpod:
    dependency: transitive
    description:
      name: riverpod
      sha256: b0657b5b30c81a3184bdaab353045f0a403ebd60bb381591a8b7ad77dcade793
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.7"
  riverpod_analyzer_utils:
    dependency: transitive
    description:
      name: riverpod_analyzer_utils
      sha256: "1b2632a6fc0b659c923a4dcc7cd5da42476f5b3294c70c86c971e63bdd443384"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.1"
  riverpod_annotation:
    dependency: "direct main"
    description:
      name: riverpod_annotation
      sha256: "8b3f7a54ddd5d53d6ea04bfb4ff77ee1b0816a1b563c0d9d43e73ce94bf2016d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  riverpod_generator:
    dependency: "direct dev"
    description:
      name: riverpod_generator
      sha256: "691180275664a5420c87d72c1ed26ef8404d32b823807540172bfd1660425376"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.4"
  riverpod_lint:
    dependency: "direct dev"
    description:
      name: riverpod_lint
      sha256: "17ad319914ac6863c64524e598913c0f17e30688aca8f5b7509e96d6e372d493"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.27.7"
  shared_preferences:
    dependency: "direct main"
    description:
      path: "packages/shared_preferences/shared_preferences"
      ref: HEAD
      resolved-ref: "9d1662580cd07545628fd86694ceca1872818664"
      url: "https://gitee.com/openharmony-sig/flutter_packages"
    source: git
    version: "2.2.0"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  shared_preferences_ohos:
    dependency: transitive
    description:
      path: "packages/shared_preferences/shared_preferences_ohos"
      ref: HEAD
      resolved-ref: "9d1662580cd07545628fd86694ceca1872818664"
      url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    source: git
    version: "2.2.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: b21ebb8bc39cf72d11c7cfd809162a48c3800668ced1c9da3aade13a32cf6c1c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "373f96cf5a8744bc9816c1ff41cf5391bbdbe3d7a96fe98c622b6738a8a7bd33"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "6adebc0006c37dd63fe05bca0a929b99f06402fc95aa35bf36d67f5c06de01fd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.4"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.9.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.0"
  state_notifier:
    dependency: transitive
    description:
      name: state_notifier
      sha256: b8677376aa54f2d7c58280d5a007f9e8774f1968d1fb1c096adcb4792fba29bb
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.16"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: "direct main"
    description:
      path: "packages/url_launcher/url_launcher"
      ref: HEAD
      resolved-ref: "9d1662580cd07545628fd86694ceca1872818664"
      url: "https://gitee.com/openharmony-sig/flutter_packages"
    source: git
    version: "6.1.12"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "4ac97281cf60e2e8c5cc703b2b28528f9b50c8f7cebc71df6bdf0845f647268a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.0"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  url_launcher_ohos:
    dependency: transitive
    description:
      path: "packages/url_launcher/url_launcher_ohos"
      ref: HEAD
      resolved-ref: "9d1662580cd07545628fd86694ceca1872818664"
      url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    source: git
    version: "6.0.38"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: ba140138558fcc3eead51a1c42e92a9fb074a1b1149ed3c73e66035b2ccd94f2
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.19"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.7"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: c538be99af830f478718b51630ec1b6bee5e74e52c8a802d328d9e71d35d2583
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "11.10.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "5a751eddf9db89b3e5f9d50c20ab8612296e4e8db69009788d6c8b060a84191c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.4"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.3"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=2.19.6 <4.0.0"
  flutter: ">=3.7.0"
