{"app": {"signingConfigs": [{"name": "default1", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default1_ohos_Zv66goCswmU88etCh_VFEEfnV7TPm3n0FYXJXHTXXfE=.cer", "storePassword": "0000001B952704D00AF7CF1E2BC68B447D2A1B4DE0B804BA5A3B9D86D37B3E3C2978401D872FA24B56D14A", "keyAlias": "debugKey", "keyPassword": "0000001B34865B4704C2C97888B4F63DA377E56CA1821BA4AE7B1BE7EBC556B3357A667379C608E1AE6B87", "profile": "/Users/<USER>/.ohos/config/default1_ohos_Zv66goCswmU88etCh_VFEEfnV7TPm3n0FYXJXHTXXfE=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default1_ohos_Zv66goCswmU88etCh_VFEEfnV7TPm3n0FYXJXHTXXfE=.p12"}}], "products": [{"name": "default", "signingConfig": "default1", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"useNormalizedOHMUrl": true}}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}