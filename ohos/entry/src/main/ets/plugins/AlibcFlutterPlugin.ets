import {
  AbilityAware,
  AbilityPluginBinding,
  FlutterPlugin,
  FlutterPluginBinding,
  MethodCall,
  MethodCallHandler,
  MethodChannel,
  MethodResult,
} from '@ohos/flutter_ohos';
import { bundleManager, UIAbility } from '@kit.AbilityKit';
import hilog from '@ohos.hilog';

import { <PERSON><PERSON><PERSON>, AlibcDegradeType, AlibcOpenType, AlibcShowParams, AlibcTaokeParams } from '@ohos/alibc';
import { BusinessError } from '@kit.BasicServicesKit';

const TAG = "AlibcFlutterPlugin"

export default class AlibcFlutterPlugin implements FlutterPlugin, MethodCallHandler, AbilityAware {
  private channel: MethodChannel | null = null;
  public uiAbility: UIAbility | null = null;

  getUniqueClassName(): string {
    return "AlibcFlutterPlugin"
  }

  onAttachedToAbility(binding: AbilityPluginBinding): void {
    this.uiAbility = binding.getAbility()
  }

  onDetachedFromAbility(): void {
    this.uiAbility = null;
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.channel = new MethodChannel(binding.getBinaryMessenger(), "alibc_platform_plugin/ohos");
    this.channel.setMethodCallHandler(this)
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    if (this.channel != null) {
      this.channel.setMethodCallHandler(null)
    }
  }

  onMethodCall(call: MethodCall, result: MethodResult): void {
    switch (call.method) {
      case "initAlibc":
        hilog.info(0xF101, TAG, 'Alibc call initAlibc');
        this.initAlibc(result);
        break;
      case "authLogin":
        hilog.info(0xF101, TAG, 'Alibc call authLogin');
        this.authLogin(result);
        break;
      case "logout":
        hilog.info(0xF101, TAG, 'Alibc call logout');
        this.logout(result);
        break;
      case "topNativeAccess":
        hilog.info(0xF101, TAG, 'Alibc call topNativeAccess');
        let appName = call.argument("appName") as string;
        this.topNativeAccess(appName, result);
        break;
      case "launcherTbByUrl":
        let url = call.argument("url") as string;
        hilog.info(0xF101, TAG, 'Alibc call launcherTbByUrl:' + url);
        this.launcherTbByUrl(url, result);
        break;
      case "launcherTbByCode":
        result.success("call launcherTbByCode")
        break;
      case "launcherTbCart":
        result.success("call launcherTbCart")
        break;
      case "getPlatformVersion":
        // result.success("OpenHarmony ^ ^ ")
        this.getBundleInfoForSelf(result);
        break;
      default:
        result.notImplemented()
        break;
    }
  }

  // 初始化阿里百川
  initAlibc(result: MethodResult) {
    Alibc.init({
      context: this.uiAbility?.context,
      onSuccess: () => {
        hilog.info(0xF101, 'EntryAbility', 'Alibc init success');
        result.success("alibc init success")
      },
      onFailure: (code, msg) => {
        hilog.info(0xF101, 'EntryAbility', 'Alibc init fail code: %{public}d, msg: %{public}s', code, msg);
        result.error(code.toString(), msg, "")
      },
    })
  }

  // 授权登录
  authLogin(result: MethodResult) {
    Alibc.login({
      onSuccess: (user) => {
        result.success(user?.openId)
      },
      onFailure: (code, msg) => {
        result.error(code.toString(), msg, "")
      },
    })
  }

  // 推出登录
  logout(result: MethodResult) {
    Alibc.logout({
      onComplete: () => {
        result.success("退出成功")
      },
    })
  }

  // topNative授权
  topNativeAccess(appName: string, result: MethodResult) {
    Alibc.authorize({
      appName: appName,
      appLogoSrc: $r('app.media.icon'),
      appKey: '25651714',
      onSuccess: (params) => {
        hilog.info(0xF101, 'Index', 'topAuth success accessToken: %{public}s, expireTime: %{public}s',
          params.accessToken, params.expireTime);
        let info: Map<string, string> = new Map();
        info.set("accessToken", params.accessToken)
        result.success(info)
      },
      onFailure: (code, msg) => {
        hilog.info(0xF101, 'Index', 'topAuth fail code: %{public}d, msg: %{public}s', code, msg);
        result.error(code.toString(), msg, "")
      }
    });
  }

  // 通过url打开淘宝
  launcherTbByUrl(url: string, result: MethodResult) {
    let showParams: AlibcShowParams = {
      clientType: 'taobao',
      openType: AlibcOpenType.Native,
      backUrl: '',
      degradeUrl: url,
      degradeType: AlibcDegradeType.H5,
    }
    let taokeParams: AlibcTaokeParams = {}
    let needBaichuanLinkConvert = false;
    Alibc.openByUrl({
      url,
      onSuccess: (code, msg) => {
        hilog.info(0xF101, 'Index', 'openByUrl成功回调 code: %{public}d, msg: %{public}s', code, msg)
        let info: Map<string, string> = new Map();
        info.set("code", code.toString())
        info.set("message", msg)
        result.success(info)
      },
      onFailure: (code, msg) => {
        hilog.info(0xF101, 'Index', 'openByUrl失败回调 code: %{public}d, msg: %{public}s', code, msg)
        result.error(code.toString(), msg, "")
      },
      showParams,
      taokeParams,
      needBaichuanLinkConvert
    });

  }

  // 获取appInfo
  getBundleInfoForSelf(result: MethodResult) {
    let bundleFlags = bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_SIGNATURE_INFO;
    try {
      bundleManager.getBundleInfoForSelf(bundleFlags).then((data) => {
        hilog.info(0x0000, 'testTag', 'getBundleInfoForSelf successfully. Data: %{public}s', JSON.stringify(data));
      }).catch((err: BusinessError) => {
        hilog.error(0x0000, 'testTag', 'getBundleInfoForSelf failed. Cause: %{public}s', err.message);
      });
    } catch (err) {
      let message = (err as BusinessError).message;
      hilog.error(0x0000, 'testTag', 'getBundleInfoForSelf failed: %{public}s', message);
    }
  }
}