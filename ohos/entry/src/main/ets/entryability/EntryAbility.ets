/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd.
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

import { FlutterAbility, FlutterEngine } from '@ohos/flutter_ohos';
import { GeneratedPluginRegistrant } from '../plugins/GeneratedPluginRegistrant';
import window from '@ohos.window';
import { BusinessError } from '@kit.BasicServicesKit';
import AlibcFlutterPlugin from '../plugins/AlibcFlutterPlugin';
import { Alibc } from '@ohos/alibc';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import Want from '@ohos.app.ability.Want';
import FluwxPlugin from '../plugins/FluwxPlugin';

export default class EntryAbility extends FlutterAbility {
  configureFlutterEngine(flutterEngine: FlutterEngine) {
    super.configureFlutterEngine(flutterEngine)
    GeneratedPluginRegistrant.registerWith(flutterEngine)
    this.addPlugin(new AlibcFlutterPlugin())
    this.addPlugin(new FluwxPlugin())
  }

  onNewWant(want: Want, launchParams: AbilityConstant.LaunchParam): void {
    Alibc.onNewWant(want);
  }

  onWindowStageCreate(windowStage: window.WindowStage) {
    /**
     * 调用 authorize 方法前必须调用，否则会导致无法弹出授权浮层
     * 设置授权浮层弹出的 windowStage，可在宿主APP的 EntryAbility onWindowStageCreate 回调中调用
     * @param windowStage
     */
    Alibc.setAuthorizeWindowStage(windowStage);
    // 1.获取应用主窗口。
    let windowClass: window.Window | null = null;
    windowStage.getMainWindow((err: BusinessError, data) => {
      let errCode: number = err.code;
      if (errCode) {
        console.error('Failed to obtain the main window. Cause: ' + JSON.stringify(err));
        return;
      }
      windowClass = data;
      console.info('Succeeded in obtaining the main window. Data: ' + JSON.stringify(data));

      // 2.实现沉浸式效果。方式一：设置导航栏、状态栏不显示。
      // let names: Array<'status' | 'navigation'> = [];
      // windowClass.setWindowSystemBarEnable(names)
      //   .then(() => {
      //     console.info('Succeeded in setting the system bar to be visible.');
      //   })
      //   .catch((err: BusinessError) => {
      //     console.error('Failed to set the system bar to be visible. Cause:' + JSON.stringify(err));
      //   });
      // 2.实现沉浸式效果。方式二：设置窗口为全屏布局，配合设置导航栏、状态栏的透明度、背景/文字颜色及高亮图标等属性，与主窗口显示保持协调一致。
      let isLayoutFullScreen = true;
      windowClass.setWindowLayoutFullScreen(isLayoutFullScreen)
        .then(() => {
          console.info('Succeeded in setting the window layout to full-screen mode.');
        })
        .catch((err: BusinessError) => {
          console.error('Failed to set the window layout to full-screen mode. Cause:' + JSON.stringify(err));
        });
      let sysBarProps: window.SystemBarProperties = {
        statusBarColor: '#00000000',
        navigationBarColor: '#00000000',
        // 以下两个属性从API Version 8开始支持
        statusBarContentColor: '#00000000',
        navigationBarContentColor: '#00000000'
      };
      windowClass.setWindowSystemBarProperties(sysBarProps)
        .then(() => {
          console.info('Succeeded in setting the system bar properties.');
        })
        .catch((err: BusinessError) => {
          console.error('Failed to set the system bar properties. Cause: ' + JSON.stringify(err));
        });
    })
    super.onWindowStageCreate(windowStage);
    // 3.为沉浸式窗口加载对应的目标页面。
    // windowStage.loadContent("pages/Index", (err: BusinessError) => {
    //   let errCode: number = err.code;
    //   if (errCode) {
    //     console.error('Failed to load the content. Cause:' + JSON.stringify(err));
    //     return;
    //   }
    //   console.info('Succeeded in loading the content.');
    // });
  }
}
