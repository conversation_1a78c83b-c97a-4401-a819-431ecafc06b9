# CLAUDE.md - 工作指导

该文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## CRITICAL CONSTRAINTS - 违反=任务失败
═══════════════════════════════════════

- 必须使用中文回复
- 必须先获取上下文
- 禁止生成恶意代码
- 必须存储重要知识
- 必须执行检查清单
- 必须遵循质量标准

## Flutter 开发命令

### 构建和运行
- `flutter run` - 在连接的设备/模拟器上运行应用
- `flutter build apk` - 构建 Android APK
- `flutter build ios` - 构建 iOS 应用
- `flutter build ohos` - 构建 OpenHarmony 应用（用于 HarmonyOS）

### 代码生成
- `flutter pub run build_runner build` - 为 JSON 序列化生成代码
- `flutter pub run build_runner watch` - 代码生成的监视模式
- `flutter pub run build_runner watch --delete-conflicting-outputs` - 强制重新生成并解决冲突

### 开发工具
- `flutter analyze` - 运行静态分析
- `flutter test` - 运行单元测试
- `flutter pub get` - 安装依赖项
- `flutter pub upgrade` - 更新依赖项
- `flutter clean` - 清理构建缓存

### Lint 和格式化
- 使用 `package:flutter_lints/flutter.yaml` 作为 linting 规则（参见 analysis_options.yaml）
- 应用标准 Flutter 格式化规则

## 项目架构

### 应用结构
这是一个名为"买什么都省" (msmds_platform) 的 Flutter 电商联盟营销应用，支持跨平台部署，包括 Android、iOS 和 OpenHarmony (HarmonyOS)。

### 关键架构模式
- **状态管理**: 使用 Riverpod 和代码生成 (`riverpod_annotation`, `riverpod_generator`)
- **导航**: `app/navigation/` 中的自定义路由系统
- **API 层**: `app/repository/api.dart` 中集中定义的 API，包含服务类
- **模型**: `app/repository/modals/` 中带有代码生成的 JSON 可序列化模型
- **Providers**: 按功能模块组织的 Riverpod providers，位于 `app/provider/`

### 主要入口点
- `lib/main.dart` - 应用初始化，包含全局配置
- `lib/app/navigation/coosea.dart` - 主应用部件，包含路由和主题设置
- `lib/config/global_config.dart` - 全局应用配置

### 核心功能
- 多平台联盟营销（淘宝、京东、拼多多、微信等）
- 用户认证和钱包系统
- 商品搜索和推荐
- 订单跟踪和佣金管理
- 活动促销和链接转换
- 提现和账单系统

### 目录结构
```
lib/
├── app/
│ ├── dialog/ # 自定义对话框
│ ├── lifecycle/ # 应用生命周期观察器
│ ├── navigation/ # 路由系统
│ ├── provider/ # 按功能模块组织的 Riverpod providers
│ ├── repository/ # 数据层
│ │ ├── modals/ # 带有 JSON 序列化的数据模型
│ │ └── service/ # 服务类
│ └── view/ # UI 界面和部件
├── common/ # 通用工具和部件
├── config/ # 应用配置
├── l10n/ # 国际化
├── utils/ # 工具函数
└── widgets/ # 可复用部件
```


### 关键依赖项
- `flutter_riverpod` - 状态管理
- `dio` - HTTP 客户端，用于 API 调用
- `flutter_inappwebview` - WebView 功能
- `flutter_smart_dialog` - 对话框系统
- `json_annotation` + `json_serializable` - JSON 序列化
- `shared_preferences` - 本地存储
- `url_launcher` - 外部 URL 处理
- `flutter_screenutil` - 屏幕适配

### 平台特定说明
- **OpenHarmony**: 用于 HarmonyOS 兼容性的特殊 Git 依赖项
- **Android**: 发布模式下启用 WebView 调试
- **跨平台**: 使用 `utils/platform_util.dart` 中的平台工具

### API 集成
该应用集成了多个联盟营销平台：
- 淘宝
- 京东
- 拼多多
- 美团
- 饿了么
- 抖音
- 唯品会

API 端点集中在 `app/repository/api.dart` 中，不同功能有单独的类（Api、ActivityApi、ConfigApi、GoodsApi、SearchApi、BillApi）。

### 代码生成
修改模型或 providers 后运行代码生成：
```bash
flutter pub run build_runner build --delete-conflicting-outputs