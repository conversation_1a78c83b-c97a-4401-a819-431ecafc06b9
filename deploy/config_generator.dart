/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: config_generator
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/17 14:17
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/17 14:17
/// @UpdateRemark: 更新说明
import 'dart:io';

void main(List<String> args) {
  final vm = args.isNotEmpty ? args[0] : '';

  final content = '''
  /// config_generator脚本自动生成，通过vm参数区分测试包或者正式包
  class Config {
    static const bool vm = $vm;
  }
  ''';

  File('lib/config/config.dart').writeAsStringSync(content);
}
