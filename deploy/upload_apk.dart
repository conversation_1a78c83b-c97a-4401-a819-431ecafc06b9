/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: upload_apk
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/19 16:54
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/19 16:54
/// @UpdateRemark: 更新说明
// import 'dart:convert';
// import 'dart:io';
// import 'package:crypto/crypto.dart';
// import 'package:dio/dio.dart';
//
// import 'app_version.dart';

class UploadOss {
  // static String ossAccessKeyId = 'ossAccessKeyId';
  //
  // static String ossAccessKeySecret = 'ossAccessKeySecret';
  //
  // // oss设置的bucket的名字
  // static String bucket = 'msmds-resouce';
  //
  // // 发送请求的url,根据你自己设置的是哪个城市的
  // static String url = 'https://$bucket.oss-cn-guangzhou.aliyuncs.com';
  //
  // // 过期时间
  // static String expiration = '2025-01-01T12:00:00.000Z';
  //
  // /// @params file 要上传的文件对象
  // /// @params rootDir 阿里云oss设置的根目录文件夹名字
  // /// @param fileType 文件类型例如jpg,mp4等
  // /// @param callback 回调函数我这里用于传cancelToken，方便后期关闭请求
  // /// @param onSendProgress 上传的进度事件
  //
  // static Future<String?> upload({
  //   required File file,
  //   String rootDir = 'flkc-beta-apk',
  //   String fileType = 'apk',
  //   required Function callback,
  //   required Function(int count, int data) onSendProgress,
  // }) async {
  //   String policyText =
  //       '{"expiration": "$expiration","conditions": [{"bucket": "$bucket" },["content-length-range", 0, 1048576000]]}';
  //
  //   // 获取签名
  //   String signature = getSignature(policyText);
  //
  //   BaseOptions options = BaseOptions();
  //   options.responseType = ResponseType.plain;
  //
  //   //创建dio对象
  //   Dio dio = Dio(options);
  //   // 生成oss的路径和文件名
  //   String pathName = '$rootDir/${versionName}_$versionNumber.$fileType';
  //
  //   // 请求参数的form对象
  //   FormData data = FormData.fromMap({
  //     'key': pathName,
  //     'policy': getSplicyBase64(policyText),
  //     'OSSAccessKeyId': ossAccessKeyId,
  //     'success_action_status': '200', //让服务端返回200，不然，默认会返回204
  //     'signature': signature,
  //     'contentType': 'multipart/form-data',
  //     'file': MultipartFile.fromFileSync(file.path),
  //   });
  //
  //   CancelToken uploadCancelToken = CancelToken();
  //   try {
  //     // 发送请求
  //     await dio.post(url, data: data, cancelToken: uploadCancelToken,
  //         onSendProgress: (int count, int data) {
  //       onSendProgress(count, data);
  //     });
  //     // 成功后返回文件访问路径
  //     return '$url/$pathName';
  //   } catch (e) {
  //     // throw (e.message);
  //     /// ignore: avoid_print
  //     print(e);
  //   }
  //   return null;
  // }
  //
  // // 获取plice的base64
  // static getSplicyBase64(String policyText) {
  //   //进行utf8编码
  //   List<int> policyTextUtf8 = utf8.encode(policyText);
  //   //进行base64编码
  //   String policyBase64 = base64.encode(policyTextUtf8);
  //   return policyBase64;
  // }
  //
  // /// 获取签名
  // static String getSignature(String policyText) {
  //   //进行utf8编码
  //   List<int> policyTextUtf8 = utf8.encode(policyText);
  //   //进行base64编码
  //   String policyBase64 = base64.encode(policyTextUtf8);
  //   //再次进行utf8编码
  //   List<int> policy = utf8.encode(policyBase64);
  //   //进行utf8 编码
  //   List<int> key = utf8.encode(ossAccessKeySecret);
  //   //通过hmac,使用sha1进行加密
  //   List<int> signaturePre = Hmac(sha1, key).convert(policy).bytes;
  //   //最后一步，将上述所得进行base64 编码
  //   String signature = base64.encode(signaturePre);
  //   return signature;
  // }
}
