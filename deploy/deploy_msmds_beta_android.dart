/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: deploy_web
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/1/15 16:31
/// @UpdateUser: frankylee
/// @UpdateData: 2024/1/15 16:31
/// @UpdateRemark: 更新说明
import 'dart:io';

/// 构建 Flutter android 应用
void main() {
  /// 开始构建apk
  generatorConfig();
}

/// 生成配置选择开启服务和抓包选项
void generatorConfig() {
  var result = Process.runSync(
    'dart',
    ['deploy/config_generator.dart', 'true'],
    runInShell: true,
  );

  /// 检查是否配置成功
  if (result.exitCode != 0) {
    /// ignore: avoid_print
    print("${result.stderr}");
    exit(1);
  } else {
    /// 输出build信息
    /// ignore: avoid_print
    print("${result.stdout}");
    /// start build
    buildFlutterAndroidApp();
  }
}

/// 构建android apk
void buildFlutterAndroidApp() {
  /// ignore: avoid_print
  print("=================开始构建apk==================");
  var result = Process.runSync(
    'flutter',
    [
      'build',
      'apk',
      '--split-per-abi',
    ],
    runInShell: true,
  );

  /// 检查是否构建成功
  if (result.exitCode != 0) {
    /// ignore: avoid_print
    print("${result.stderr}");
    exit(1);
  } else {
    /// 输出build信息
    /// ignore: avoid_print
    print("${result.stdout}");
  }

  /// ignore: avoid_print
  print("=================apk构建完成==================");
}
